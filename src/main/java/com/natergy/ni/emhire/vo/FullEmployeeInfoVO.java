package com.natergy.ni.emhire.vo;

import com.natergy.ni.emhire.entity.EmployeeFileEntity;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 完整的员工信息
 * <AUTHOR>
 */
@Data
@ToString
public class FullEmployeeInfoVO  {
	/**
	 * 基础信息
	 */
	private EmployeeBaseInfoVO baseInfo;
	/**
	 * 扩展信息
	 */
	private EmployeeExtInfoVO extInfo;
	/**
	 * 家属信息
	 */
	private List<EmployeeHireFamilyVO> familyMembers;
	/**
	 * 教育经历
	 */
	private List<EmployeeHireEducationVO> educations;
	/**
	 * 工作经历
	 */
	private List<EmployeeHireWorkExperienceVO> workExperiences;
	/**
	 * 附件
	 */
	private List<EmployeeFileEntity> files;
	/**
	 * 培训记录
	 */
	private List<EmployeeHireTrainingVO> trainingList;
	/**
	 * 宿舍信息
	 */
	private EmployeeDormitoryVO dormitory;
	/**
	 * 特殊工种执业证
	 */
	private List<EmployeeSpecializedWorkVO> specializedWorkList;
}
