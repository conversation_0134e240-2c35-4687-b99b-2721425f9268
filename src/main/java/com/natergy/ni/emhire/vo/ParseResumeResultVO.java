package com.natergy.ni.emhire.vo;

import lombok.Data;

import java.util.List;

@Data
public class ParseResumeResultVO {
	/**
	 * 简历标识
	 */
	private String resumeId;
	/**
	 * 总结
	 */
	private String summary;
	/**
	 * 推荐值
	 */
	private Integer remend;
	/**
	 * 备注补充
	 */
	private String remarks;
	/**
	 * 推荐考题
	 */
	private List<String> examQuestions;

	/**
	 * 简历信息
	 */
	private ResumeInfo resumeInfo;
	/**
	 * 分析报告
	 */
	private AnalysisRepo analysisRepo;

	/**
	 * 简历信息
	 */
	@Data
	public static class ResumeInfo {
		/**
		 * 名称
		 */
		private String name;
		/**
		 * 意向岗位
		 */
		private String position;
		/**
		 * 学历
		 */
		private String education;
		/**
		 * 年龄
		 */
		private String age;
		/**
		 * 性别
		 */
		private String gender;
		/**
		 * 联系电话
		 */
		private String phone;
		/**
		 * 身份证号
		 */
		private String idCard;
		/**
		 * 就读学校
		 */
		private String school;
		/**
		 * 工龄
		 */
		private String workingAge;
		/**
		 * 意向城市
		 */
		private String city;
		/**
		 * 扩展字段
		 */
		private String ext;
	}

	/**
	 * 分析报告
	 */
	@Data
	public static class AnalysisRepo {
		/**
		 * 专业技能
		 */
		private String professional;

		/**
		 * 专业匹配度
		 */
		private String major_match_score;
		/**
		 * 综合能力
		 */
		private String comprehensive_ability_score;
		/**
		 * 关键词命中
		 */
		private String keyword_match_score;
		/**
		 * 项目相关性
		 */
		private String project_relevance_score;
		/**
		 * 项目说明
		 */
		private String project_relevance_notes;
		/**
		 * 逻辑
		 */
		private String logic;
		/**
		 * 成长
		 */
		private String growth_potential_score;
		/**
		 * 学习能力
		 */
		private String learning;
		/**
		 * 关键词
		 */
		private List<String> keywords;
		/**
		 * 推荐等级
		 */
		private String recommendationLevel;
		/**
		 * 推荐理由
		 */
		private String recommendationReason;
	}
}
