/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.emhire.entity.EmployeeResumeInterviewEntity;
import com.natergy.ni.emhire.vo.EmployeeResumeInterviewVO;
import java.util.Objects;

/**
 * 简历面试记录表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public class EmployeeResumeInterviewWrapper extends BaseEntityWrapper<EmployeeResumeInterviewEntity, EmployeeResumeInterviewVO>  {

	public static EmployeeResumeInterviewWrapper build() {
		return new EmployeeResumeInterviewWrapper();
 	}

	@Override
	public EmployeeResumeInterviewVO entityVO(EmployeeResumeInterviewEntity niEmployeeResumeInterview) {
		EmployeeResumeInterviewVO niEmployeeResumeInterviewVO = Objects.requireNonNull(BeanUtil.copy(niEmployeeResumeInterview, EmployeeResumeInterviewVO.class));

		//User createUser = UserCache.getUser(niEmployeeResumeInterview.getCreateUser());
		//User updateUser = UserCache.getUser(niEmployeeResumeInterview.getUpdateUser());
		//niEmployeeResumeInterviewVO.setCreateUserName(createUser.getName());
		//niEmployeeResumeInterviewVO.setUpdateUserName(updateUser.getName());

		return niEmployeeResumeInterviewVO;
	}


}
