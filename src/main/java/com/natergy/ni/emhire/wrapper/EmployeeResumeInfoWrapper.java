/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.wrapper;

import com.natergy.ni.emhire.enums.ResumeStatus;
import com.natergy.ni.emhire.vo.ParseResumeResultVO;
import org.apache.commons.lang.StringUtils;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.emhire.entity.EmployeeResumeInfoEntity;
import com.natergy.ni.emhire.vo.EmployeeResumeInfoVO;
import java.util.Objects;

/**
 * 简历表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public class EmployeeResumeInfoWrapper extends BaseEntityWrapper<EmployeeResumeInfoEntity, EmployeeResumeInfoVO>  {

	public static EmployeeResumeInfoWrapper build() {
		return new EmployeeResumeInfoWrapper();
 	}

	@Override
	public EmployeeResumeInfoVO entityVO(EmployeeResumeInfoEntity niEmployeeResumeInfo) {
		EmployeeResumeInfoVO niEmployeeResumeInfoVO = Objects.requireNonNull(BeanUtil.copy(niEmployeeResumeInfo, EmployeeResumeInfoVO.class));
		if (StringUtils.isNotBlank(niEmployeeResumeInfo.getAiResult()) && !Objects.equals(niEmployeeResumeInfo.getResumeStatus(), ResumeStatus.ParsingFailed.code)) {

			ParseResumeResultVO parseResumeResultVO = JsonUtil.parse(niEmployeeResumeInfo.getAiResult(), ParseResumeResultVO.class);
			niEmployeeResumeInfoVO.setParseResumeResultVO(parseResumeResultVO);
			niEmployeeResumeInfoVO.setSummary(parseResumeResultVO.getSummary());
			if (Objects.nonNull(parseResumeResultVO.getAnalysisRepo())){
				niEmployeeResumeInfoVO.setRecommendationLevel(parseResumeResultVO.getAnalysisRepo().getRecommendationLevel());
				niEmployeeResumeInfoVO.setRecommendationReason(parseResumeResultVO.getAnalysisRepo().getRecommendationReason());
			}
		}
		return niEmployeeResumeInfoVO;
	}


}
