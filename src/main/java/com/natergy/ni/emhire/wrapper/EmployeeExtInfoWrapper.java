/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.emhire.entity.EmployeeExtInfoEntity;
import com.natergy.ni.emhire.vo.EmployeeExtInfoVO;
import java.util.Objects;

/**
 * 员工档案扩展信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public class EmployeeExtInfoWrapper extends BaseEntityWrapper<EmployeeExtInfoEntity, EmployeeExtInfoVO>  {

	public static EmployeeExtInfoWrapper build() {
		return new EmployeeExtInfoWrapper();
 	}

	@Override
	public EmployeeExtInfoVO entityVO(EmployeeExtInfoEntity niHrEmployeeExtInfo) {
		if (niHrEmployeeExtInfo == null) {
			return new EmployeeExtInfoVO();
		}
		EmployeeExtInfoVO niHrEmployeeExtInfoVO = Objects.requireNonNull(BeanUtil.copy(niHrEmployeeExtInfo, EmployeeExtInfoVO.class));

		//User createUser = UserCache.getUser(niHrEmployeeExtInfo.getCreateUser());
		//User updateUser = UserCache.getUser(niHrEmployeeExtInfo.getUpdateUser());
		//niHrEmployeeExtInfoVO.setCreateUserName(createUser.getName());
		//niHrEmployeeExtInfoVO.setUpdateUserName(updateUser.getName());

		return niHrEmployeeExtInfoVO;
	}


}
