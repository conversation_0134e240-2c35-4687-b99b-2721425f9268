///*
// *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
// *
// *  Redistribution and use in source and binary forms, with or without
// *  modification, are permitted provided that the following conditions are met:
// *
// *  Redistributions of source code must retain the above copyright notice,
// *  this list of conditions and the following disclaimer.
// *  Redistributions in binary form must reproduce the above copyright
// *  notice, this list of conditions and the following disclaimer in the
// *  documentation and/or other materials provided with the distribution.
// *  Neither the name of the dreamlu.net developer nor the names of its
// *  contributors may be used to endorse or promote products derived from
// *  this software without specific prior written permission.
// *  Author: Chill 庄骞 (<EMAIL>)
// */
//package com.natergy.ni.emhire.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.natergy.ni.base.constant.PropertyConst;
//import com.natergy.ni.emhire.entity.*;
//import com.natergy.ni.emhire.enums.EmployeeEnums;
//import com.natergy.ni.emhire.service.*;
//import com.natergy.ni.emhire.vo.EmployeeBaseInfoVO;
//import com.natergy.ni.emhire.mapper.EmployeeBaseInfoMapper;
//import com.natergy.ni.emhire.vo.EmployeeExtInfoVO;
//import com.natergy.ni.emhire.vo.FullEmployeeInfoVO;
//import com.natergy.ni.emhire.wrapper.*;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springblade.core.mp.base.BaseServiceImpl;
//import org.springblade.core.secure.BladeUser;
//import org.springblade.core.secure.utils.AuthUtil;
//import org.springblade.core.tool.jackson.JsonUtil;
//import org.springblade.core.tool.utils.BeanUtil;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//import java.util.Optional;
//
///**
// * 员工基础信息表 服务实现类
// *
// * <AUTHOR>
// * @since 2025-06-09
// */
//@Service
//@Slf4j
//public class EmployeeBaseInfoServiceImpl extends BaseServiceImpl<EmployeeBaseInfoMapper, EmployeeBaseInfoEntity> implements IEmployeeBaseInfoService {
//
//	@Autowired
//	private IEmployeeHireWorkExperienceService employeeHireWorkExperienceService;
//	@Autowired
//	private IEmployeeHireFamilyService employeeHireFamilyService;
//	@Autowired
//	private IEmployeeEducationService employeeEducationService;
//	@Autowired
//	private IEmployeeExtInfoService employeeExtInfoService;
//	@Autowired
//	private IEmployeeHireTrainingService employeeHireTrainingService;;
//	@Autowired
//	private IEmployeeFileService employeeFileService;
//	@Override
//	public IPage<EmployeeBaseInfoVO> selectEmployeeBaseInfoPage(IPage<EmployeeBaseInfoVO> page, EmployeeBaseInfoVO niHrEmployeeBaseInfo) {
//		return page.setRecords(baseMapper.selectEmployeeBaseInfoPage(page, niHrEmployeeBaseInfo));
//	}
//
//	/**
//	 * 入职前 信息初始化
//	 *
//	 * @param id
//	 * @param name
//	 * @param phone
//	 */
//	@Override
//	public void onBoardingBefore(Long id, String name, String phone,Long resumeId) {
//		EmployeeBaseInfoEntity baseInfoEntity = new EmployeeBaseInfoEntity();
//		baseInfoEntity.setResumeId(id);
//		baseInfoEntity.setName(name);
//		baseInfoEntity.setPhone(phone);
//		baseInfoEntity.setResumeId(resumeId);
//
//		BladeUser user = AuthUtil.getUser();
//		baseInfoEntity.setCreateTime(new Date());
//		baseInfoEntity.setCreateUser(user.getUserId());
//		baseInfoEntity.setIsDeleted(PropertyConst.YesNo.NO.value());
////		baseInfoEntity.setCreateDept(Long.valueOf(user.getDeptId()));
//		baseInfoEntity.setStatus(EmployeeEnums.EmployeeStatus.OnBoardingBefore.code);
//		baseMapper.insert(baseInfoEntity);
//		log.info(" 员工档案模块 {} 办理入职 起源简历{}", name, id);
//	}
//
//	/**
//	 * 获取全量员工信息
//	 *
//	 * @param baseInfo
//	 * @return
//	 */
//	@Override
//	public FullEmployeeInfoVO fullEmployeeInfo(EmployeeBaseInfoEntity baseInfo) {
//		FullEmployeeInfoVO fullEmployeeInfoVO = new FullEmployeeInfoVO();
//
//		fullEmployeeInfoVO.setBaseInfo(EmployeeHireRecordWrapper.build().entityVO(baseInfo));
//
//		List<EmployeeExtInfoEntity> extInfos = employeeExtInfoService.list(new LambdaQueryWrapper<EmployeeExtInfoEntity>()
//			.eq(EmployeeExtInfoEntity::getEmployeeId, baseInfo.getId())
//			.eq(EmployeeExtInfoEntity::getIsDeleted, PropertyConst.YesNo.NO.value())
//		);
//		fullEmployeeInfoVO.setExtInfo(EmployeeExtInfoWrapper.build().entityVO(CollectionUtils.lastElement(extInfos)));
//
//		List<EmployeeEducationEntity> educationEntityList = employeeEducationService.list(new LambdaQueryWrapper<EmployeeEducationEntity>()
//			.eq(EmployeeEducationEntity::getEmployeeId, baseInfo.getId())
//			.eq(EmployeeEducationEntity::getIsDeleted, PropertyConst.YesNo.NO.value())
//		);
//		fullEmployeeInfoVO.setEducations(EmployeeHireEducationWrapper.build().listVO(educationEntityList));
//		log.info(" 员工档案 查询全量员工信息 {}获取教育经历{}",baseInfo.getId(), JsonUtil.toJson(educationEntityList));
//
//		List<EmployeeHireFamilyEntity> familyMembers = employeeHireFamilyService.list(new LambdaQueryWrapper<EmployeeHireFamilyEntity>()
//			.eq(EmployeeHireFamilyEntity::getEmployeeHireId, baseInfo.getId())
//			.eq(EmployeeHireFamilyEntity::getIsDeleted, PropertyConst.YesNo.NO.value())
//		);
//		fullEmployeeInfoVO.setFamilyMembers(EmployeeHireFamilyWrapper.build().listVO(familyMembers));
//		log.info(" 员工档案 查询全量员工信息 {}获取家庭成员{}",baseInfo.getId(), JsonUtil.toJson(familyMembers));
//
//		List<EmployeeHireWorkExperienceEntity> works = employeeHireWorkExperienceService.list(new LambdaQueryWrapper<EmployeeHireWorkExperienceEntity>()
//			.eq(EmployeeHireWorkExperienceEntity::getEmployeeHireId, baseInfo.getId())
//			.eq(EmployeeHireWorkExperienceEntity::getIsDeleted, PropertyConst.YesNo.NO.value())
//		);
//		fullEmployeeInfoVO.setWorkExperiences(EmployeeHireWorkExperienceWrapper.build().listVO(works));
//		log.info(" 员工档案 查询全量员工信息 {}获取工作履历{}",baseInfo.getId(), JsonUtil.toJson(works));
//
//		List<EmployeeHireTrainingEntity> trainingEntities = employeeHireTrainingService.list(new LambdaQueryWrapper<EmployeeHireTrainingEntity>().eq(EmployeeHireTrainingEntity::getEmployeeId, baseInfo.getId()));
//		fullEmployeeInfoVO.setTrainingList(EmployeeHireTrainingWrapper.build().listVO(trainingEntities));
//		log.info(" 员工档案 查询全量员工信息 {}获取培训记录{}",baseInfo.getId(), JsonUtil.toJson(trainingEntities));
//
//		List<EmployeeFileEntity> files = employeeFileService.list(new LambdaQueryWrapper<EmployeeFileEntity>().eq(EmployeeFileEntity::getEmployeeId, baseInfo.getId()));
//		fullEmployeeInfoVO.setFiles(files);
//		log.info(" 员工档案 查询全量员工信息 {}获取附件信息{}",baseInfo.getId(), JsonUtil.toJson(files));
//
//		return fullEmployeeInfoVO;
//	}
//
//
//	/**
//	 * 获取全量员工信息
//	 *
//	 * @param id
//	 * @return
//	 */
//	@Override
//	public FullEmployeeInfoVO fullEmployeeInfoById(Long id) {
//		FullEmployeeInfoVO full = new FullEmployeeInfoVO();
//		EmployeeBaseInfoEntity baseInfo = Optional.ofNullable(getById(id)).orElse(new EmployeeBaseInfoEntity());
//		full.setBaseInfo(EmployeeHireRecordWrapper.build().entityVO(baseInfo));
//
//		EmployeeExtInfoEntity extInfo = employeeExtInfoService.getOne(new LambdaQueryWrapper<EmployeeExtInfoEntity>()
//			.eq(EmployeeExtInfoEntity::getEmployeeId, id)
//			.eq(EmployeeExtInfoEntity::getIsDeleted, PropertyConst.YesNo.NO.value()), false);
//		full.setExtInfo(EmployeeExtInfoWrapper.build().entityVO(extInfo));
//
//		List<EmployeeEducationEntity> educationEntityList = employeeEducationService.list(new LambdaQueryWrapper<EmployeeEducationEntity>().eq(EmployeeEducationEntity::getEmployeeId, id));
//		full.setEducations(EmployeeHireEducationWrapper.build().listVO(educationEntityList));
//
//		List<EmployeeHireFamilyEntity> familyEntities = employeeHireFamilyService.list(new LambdaQueryWrapper<EmployeeHireFamilyEntity>().eq(EmployeeHireFamilyEntity::getEmployeeHireId, id));
//		full.setFamilyMembers(EmployeeHireFamilyWrapper.build().listVO(familyEntities));
//
//		List<EmployeeHireWorkExperienceEntity> works = employeeHireWorkExperienceService.list(new LambdaQueryWrapper<EmployeeHireWorkExperienceEntity>().eq(EmployeeHireWorkExperienceEntity::getEmployeeHireId, id));
//		full.setWorkExperiences(EmployeeHireWorkExperienceWrapper.build().listVO(works));
//
//		List<EmployeeHireTrainingEntity> trainingEntities = employeeHireTrainingService.list(new LambdaQueryWrapper<EmployeeHireTrainingEntity>().eq(EmployeeHireTrainingEntity::getEmployeeId, id));
//		full.setTrainingList(EmployeeHireTrainingWrapper.build().listVO(trainingEntities));
//
//		List<EmployeeFileEntity> files = employeeFileService.list(new LambdaQueryWrapper<EmployeeFileEntity>().eq(EmployeeFileEntity::getEmployeeId, id));
//		full.setFiles(files);
//
//		return full;
//	}
//
//	/**
//	 * 更新信息
//	 *
//	 * @param infoVO
//	 * @return
//	 */
//	@Transactional
//	@Override
//	public String employeeFillForm(FullEmployeeInfoVO infoVO) {
//		// 基础信息
//		EmployeeBaseInfoVO baseInfo = infoVO.getBaseInfo();
//		EmployeeBaseInfoEntity infoEntity = Optional.ofNullable(getById(infoVO.getBaseInfo().getId())).orElseThrow(()->new RuntimeException("查无此人信息"));
//
//		if (!Objects.equals(infoEntity.getStatus(), EmployeeEnums.EmployeeStatus.OnBoardingBefore.code)) {
//			log.warn("员工档案 邀请员工填写信息 员工信息{} 状态 {} 不在可编辑范围",infoEntity.getId(),infoEntity.getStatus());
//			return "员工信息 状态不在可操作范围 请联系HR";
//		}
//		// 冗余字段 用于查询
//		if (Objects.nonNull(infoVO.getExtInfo()) && StringUtils.isNotBlank(infoVO.getExtInfo().getCurrentAddress())) {
//			infoEntity.setAddress(infoVO.getExtInfo().getCurrentAddress());
//		}
//
//		mergeInfo(baseInfo,infoEntity);
//
//		updateById(infoEntity);
//		// 员工其他信息
//		employeeExtInfoService.updateInfoOnEmployeeFillForm(baseInfo.getId(),infoVO.getExtInfo());
//		// 教育经历
//		employeeEducationService.refresh(baseInfo.getId(),infoVO.getEducations());
//		// 家庭成员
//		employeeHireFamilyService.refresh(baseInfo.getId(),infoVO.getFamilyMembers());
//		// 工作履历
//		employeeHireWorkExperienceService.refresh(baseInfo.getId(),infoVO.getWorkExperiences());
//
//		return "操作成功";
//	}
//
//
//	/**
//	 * HR创建新的员工档案
//	 *
//	 * @param fullEmployeeInfoVO
//	 * @return
//	 */
//	@Override
//	@Transactional
//	public boolean onBoarding(FullEmployeeInfoVO fullEmployeeInfoVO) {
//		fullEmployeeInfoVO.getBaseInfo().setStatus(EmployeeEnums.EmployeeStatus.Process.code);
//		updateFullInfo(fullEmployeeInfoVO);
//		log.info("员工档案 HR确认信息后进入入职流程 {}",JsonUtil.toJson(fullEmployeeInfoVO));
//		return true;
//	}
//
//	@Override
//	public boolean updateFullInfo(FullEmployeeInfoVO fullEmployeeInfoVO) {
//		EmployeeBaseInfoVO baseInfo = fullEmployeeInfoVO.getBaseInfo();
//		EmployeeExtInfoVO extInfo = fullEmployeeInfoVO.getExtInfo();
//		// 冗余字段 用于查询
//		if (Objects.nonNull(extInfo) && StringUtils.isNotBlank(extInfo.getCurrentAddress())) {
//			baseInfo.setAddress(extInfo.getCurrentAddress());
//		}
//		this.saveOrUpdate(baseInfo);
//
//		employeeExtInfoService.updateInfoOnEmployeeFillForm(baseInfo.getId(),fullEmployeeInfoVO.getExtInfo());
//		// 教育经历
//		employeeEducationService.refresh(baseInfo.getId(),fullEmployeeInfoVO.getEducations());
//		// 家庭成员
//		employeeHireFamilyService.refresh(baseInfo.getId(),fullEmployeeInfoVO.getFamilyMembers());
//		// 工作履历
//		employeeHireWorkExperienceService.refresh(baseInfo.getId(),fullEmployeeInfoVO.getWorkExperiences());
//		return true;
//	}
//
//	private void mergeInfo(EmployeeBaseInfoVO baseInfoVO, EmployeeBaseInfoEntity infoEntity) {
//		BeanUtil.copyNonNull(baseInfoVO,infoEntity);
//		infoEntity.setStatus(EmployeeEnums.EmployeeStatus.OnBoarding.code);
//		infoEntity.setFillTime(new Date());
//	}
//
//	/**
//	 * 邀请员工填写
//	 *
//	 * @param inviteName
//	 * @param invitePhone
//	 * @return
//	 */
//	@Override
//	public String invite(String inviteName, String invitePhone) {
//		EmployeeBaseInfoEntity entity = new EmployeeBaseInfoEntity();
//		entity.setStatus(EmployeeEnums.EmployeeStatus.OnBoardingBefore.code);
//		entity.setName(inviteName);
//		entity.setPhone(invitePhone);
//		entity.setIsDeleted(PropertyConst.YesNo.NO.value());
//		entity.setCreateTime(new Date());
//		save(entity);
//		log.info("员工档案 HR邀请员工 填写信息:{}",JsonUtil.toJson(entity));
//		return "操作成功";
//	}
//}
