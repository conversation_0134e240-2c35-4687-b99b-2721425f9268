/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.service.impl;

import com.natergy.ni.emhire.entity.EmployeeResumeInterviewEntity;
import com.natergy.ni.emhire.vo.EmployeeResumeInterviewVO;
import com.natergy.ni.emhire.mapper.EmployeeResumeInterviewMapper;
import com.natergy.ni.emhire.service.IEmployeeResumeInterviewService;
import org.apache.commons.lang.StringUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

/**
 * 简历面试记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class EmployeeResumeInterviewServiceImpl extends BaseServiceImpl<EmployeeResumeInterviewMapper, EmployeeResumeInterviewEntity> implements IEmployeeResumeInterviewService {

	@Override
	public IPage<EmployeeResumeInterviewVO> selectEmployeeResumeInterviewPage(IPage<EmployeeResumeInterviewVO> page, EmployeeResumeInterviewVO niEmployeeResumeInterview) {
		return page.setRecords(baseMapper.selectEmployeeResumeInterviewPage(page, niEmployeeResumeInterview));
	}

	/**
	 * 录入面试评价
	 *
	 * @param id
	 * @param interviewSummary 面评
	 * @param interviewResult  面试结果
	 * @param attachmentId     附件信息
	 * @param attachmentUrl    附件URL
	 */
	@Transactional
	@Override
	public void updateOnEvaluation(Long id, String interviewSummary, Integer interviewResult, Long attachmentId, String attachmentUrl) {
		EmployeeResumeInterviewEntity interviewEntity = Optional.ofNullable(getById(id)).orElseThrow(() -> new RuntimeException("面试邀约信息错误"));
		if (StringUtils.isNotBlank(attachmentUrl) && Objects.nonNull(attachmentId)) {
			interviewEntity.setAttachmentId(attachmentId);
			interviewEntity.setAttachmentUrl(attachmentUrl);
		}
		interviewEntity.setInterviewResult(interviewResult);
		interviewEntity.setInterviewSummary(interviewSummary);
		updateById(interviewEntity);
	}
}
