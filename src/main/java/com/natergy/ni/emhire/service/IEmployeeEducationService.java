///*
// *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
// *
// *  Redistribution and use in source and binary forms, with or without
// *  modification, are permitted provided that the following conditions are met:
// *
// *  Redistributions of source code must retain the above copyright notice,
// *  this list of conditions and the following disclaimer.
// *  Redistributions in binary form must reproduce the above copyright
// *  notice, this list of conditions and the following disclaimer in the
// *  documentation and/or other materials provided with the distribution.
// *  Neither the name of the dreamlu.net developer nor the names of its
// *  contributors may be used to endorse or promote products derived from
// *  this software without specific prior written permission.
// *  Author: Chill 庄骞 (<EMAIL>)
// */
//package com.natergy.ni.emhire.service;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.natergy.ni.emhire.entity.EmployeeEducationEntity;
//import com.natergy.ni.emhire.vo.EmployeeHireEducationVO;
//import org.springblade.core.mp.base.BaseService;
//
//import java.util.List;
//
///**
// * 员工学历信息表 服务类
// *
// * <AUTHOR>
// * @since 2025-06-09
// */
//public interface IEmployeeEducationService extends BaseService<EmployeeEducationEntity> {
//
//	/**
//	 * 自定义分页
//	 *
//	 * @param page
//	 * @param niHrEmployeeEducation
//	 * @return
//	 */
//	IPage<EmployeeEducationEntity> selectEmployeeEducationPage(IPage<EmployeeEducationEntity> page, EmployeeEducationEntity niHrEmployeeEducation);
//
//	/**
//	 * 刷新员工教育信息
//	 * @param employeeId
//	 * @param educations
//	 */
//	void refresh(Long employeeId, List<EmployeeHireEducationVO> educations);
//}
