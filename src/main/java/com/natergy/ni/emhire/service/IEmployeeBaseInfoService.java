/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.service;

import com.natergy.ni.emhire.entity.EmployeeBaseInfoEntity;
import com.natergy.ni.emhire.vo.EmployeeBaseInfoVO;
import com.natergy.ni.emhire.vo.FullEmployeeInfoVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.validation.Valid;

/**
 * 员工基础信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public interface IEmployeeBaseInfoService extends BaseService<EmployeeBaseInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param niHrEmployeeBaseInfo
	 * @return
	 */
	IPage<EmployeeBaseInfoVO> selectEmployeeBaseInfoPage(IPage<EmployeeBaseInfoVO> page, EmployeeBaseInfoVO niHrEmployeeBaseInfo);

	/**
	 * 入职前 信息初始化
	 * @param id
	 * @param name
	 * @param phone
	 */
	void onBoardingBefore(Long id, String name, String phone,Long resumeId);

	/**
	 * 获取全量员工信息
	 * @param baseInfo
	 * @return
	 */
	FullEmployeeInfoVO fullEmployeeInfo(EmployeeBaseInfoEntity baseInfo);

	/**
	 * 获取全量员工信息
	 * @param id
	 * @return
	 */
	FullEmployeeInfoVO fullEmployeeInfoById(Long id);

	/**
	 * 更新信息
	 * @param infoVO
	 * @return
	 */
	String employeeFillForm(FullEmployeeInfoVO infoVO);

	/**
	 * HR创建新的员工档案
	 * @param fullEmployeeInfoVO
	 * @return
	 */
    boolean onBoarding(FullEmployeeInfoVO fullEmployeeInfoVO);

	boolean updateFullInfo(FullEmployeeInfoVO fullEmployeeInfoVO);

	/**
	 * 邀请员工填写
	 * @param inviteName
	 * @param invitePhone
	 * @return
	 */
    String invite(String inviteName, String invitePhone);
}
