/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.service;

import com.natergy.ni.emhire.entity.EmployeeResumeInfoEntity;
import com.natergy.ni.emhire.vo.EmployeeResumeInfoVO;
import com.natergy.ni.emhire.vo.EmployeeResumeInterviewVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.Date;
import java.util.List;

/**
 * 简历表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface IEmployeeResumeInfoService extends BaseService<EmployeeResumeInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param niEmployeeResumeInfo
	 * @return
	 */
	IPage<EmployeeResumeInfoVO> selectEmployeeResumeInfoPage(IPage<EmployeeResumeInfoEntity> page, EmployeeResumeInfoVO niEmployeeResumeInfo);

	/**
	 * 批量上传简历
	 * @param saveInfoVO
	 * @return
	 */
    boolean batch(EmployeeResumeInfoVO.BatchSaveInfoVO saveInfoVO);

	/**
	 * 重新分析
	 * @param id
	 * @param prompt
	 * @return
	 */
	EmployeeResumeInfoVO reanalysis(Long id, String prompt);

	/**
	 * 用人部门发起邀请
	 * @param id
	 * @param position 意向岗位
	 * @param interviewTime 预计面试时间
	 * @return
	 */
	String inviteOnDept(Long id,String position, Date interviewTime);

	/**
	 * 邀请面试
	 *
	 * @param id                简历
	 * @param interviewRecordId 面试邀请记录
	 * @param position
	 * @param interviewer       面试官
	 * @param interviewTime     面试时间
	 * @return
	 */
	String invite(Long id, Long interviewRecordId, String position, Long interviewer, Date interviewTime);

	/**
	 * 查询简历面试邀请九零
	 * @param id
	 * @return
	 */
	List<EmployeeResumeInterviewVO> listInviteRecord(Long id);

	/**
	 * 米录入面试评价
	 * @param evaluationInfoVO
	 */
	void evaluation(EmployeeResumeInfoVO.EvaluationInfoVO evaluationInfoVO);

	/**
	 * 详情
	 * @param id
	 * @return
	 */
	EmployeeResumeInfoVO details(Long id);

	/**
	 * 办理入职手续
	 * 面试流程中最后一步 与 入职流程交互
	 * @param id
	 * @param phone
	 * @return
	 */
	String onboarding(Long id, String phone);

	/**
	 * 获取关联部门主管ID
	 * @param id
	 * @return
	 */
	List<Long> findResumeInfoRelDeptLeader(Long id);
}
