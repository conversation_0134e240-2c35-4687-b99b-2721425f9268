/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.emhire.entity.EmployeeFamilyMembersEntity;
import com.natergy.ni.emhire.entity.EmployeeHireFamilyEntity;
import com.natergy.ni.emhire.entity.EmployeeHireWorkExperienceEntity;
import com.natergy.ni.emhire.mapper.EmployeeFamilyMembersMapper;
import com.natergy.ni.emhire.service.IEmployeeFamilyMembersService;
import com.natergy.ni.emhire.vo.EmployeeHireFamilyVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工家庭成员表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Service
@Slf4j
public class EmployeeFamilyMembersServiceImpl extends BaseServiceImpl<EmployeeFamilyMembersMapper, EmployeeFamilyMembersEntity> implements IEmployeeFamilyMembersService {

	@Override
	public IPage<EmployeeFamilyMembersEntity> selectEmployeeFamilyMembersPage(IPage<EmployeeFamilyMembersEntity> page, EmployeeFamilyMembersEntity niHrEmployeeFamilyMembers) {
		return page.setRecords(baseMapper.selectEmployeeFamilyMembersPage(page, niHrEmployeeFamilyMembers));
	}

}
