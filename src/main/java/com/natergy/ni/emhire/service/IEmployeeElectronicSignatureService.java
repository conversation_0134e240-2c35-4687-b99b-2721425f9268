///*
// *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
// *
// *  Redistribution and use in source and binary forms, with or without
// *  modification, are permitted provided that the following conditions are met:
// *
// *  Redistributions of source code must retain the above copyright notice,
// *  this list of conditions and the following disclaimer.
// *  Redistributions in binary form must reproduce the above copyright
// *  notice, this list of conditions and the following disclaimer in the
// *  documentation and/or other materials provided with the distribution.
// *  Neither the name of the dreamlu.net developer nor the names of its
// *  contributors may be used to endorse or promote products derived from
// *  this software without specific prior written permission.
// *  Author: Chill 庄骞 (<EMAIL>)
// */
//package com.natergy.ni.emhire.service;
//
//import com.natergy.ni.emhire.entity.EmployeeElectronicSignatureEntity;
//import com.natergy.ni.emhire.utils.TencentElectronicSignatureHelper;
//import com.natergy.ni.emhire.vo.FullEmployeeInfoVO;
//import com.tencentcloudapi.common.exception.TencentCloudSDKException;
//import org.springblade.core.mp.base.BaseService;
//
//import java.util.Map;
//
//
///**
// * 员工电子签
// */
//public interface IEmployeeElectronicSignatureService extends BaseService<EmployeeElectronicSignatureEntity> {
//
//
//	/**
//	 * 完成电子签
//	 * @param data
//	 */
//	void finishSign(Map<String,Object> data);
//
//	/**
//	 * 文档填充后
//	 * @param data
//	 */
//	void fillDocumentAfter(Map<String,Object> data);
//
//	/**
//	 * 发起入职电子签流程
//	 *
//	 * @param id
//	 * @param fullInfo
//	 * @param userData
//	 */
//	void startOnBoardingESign(Long id, FullEmployeeInfoVO fullInfo, Map<String, String> userData) throws TencentCloudSDKException;
//
//	EmployeeElectronicSignatureEntity findByProcessIdAndEmployeeId(String processId, Long employeeId);
//}
