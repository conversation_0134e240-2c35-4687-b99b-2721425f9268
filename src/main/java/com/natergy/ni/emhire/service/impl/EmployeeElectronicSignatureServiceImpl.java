///*
// *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
// *
// *  Redistribution and use in source and binary forms, with or without
// *  modification, are permitted provided that the following conditions are met:
// *
// *  Redistributions of source code must retain the above copyright notice,
// *  this list of conditions and the following disclaimer.
// *  Redistributions in binary form must reproduce the above copyright
// *  notice, this list of conditions and the following disclaimer in the
// *  documentation and/or other materials provided with the distribution.
// *  Neither the name of the dreamlu.net developer nor the names of its
// *  contributors may be used to endorse or promote products derived from
// *  this software without specific prior written permission.
// *  Author: Chill 庄骞 (<EMAIL>)
// */
//package com.natergy.ni.emhire.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.natergy.ni.base.constant.PropertyConst;
//import com.natergy.ni.emhire.entity.EmployeeElectronicSignatureEntity;
//import com.natergy.ni.emhire.entity.EmployeeFileEntity;
//import com.natergy.ni.emhire.enums.ElectronicSignatureEnums;
//import com.natergy.ni.emhire.enums.HireNode;
//import com.natergy.ni.emhire.mapper.EmployeeElectronicSignatureMapper;
//import com.natergy.ni.emhire.service.IEmployeeElectronicSignatureService;
//import com.natergy.ni.emhire.utils.EmployeeOnBoardingHelper;
//import com.natergy.ni.emhire.utils.FIleHelper;
//import com.natergy.ni.emhire.utils.TencentElectronicSignatureHelper;
//import com.natergy.ni.emhire.vo.FullEmployeeInfoVO;
//import com.tencentcloudapi.common.exception.TencentCloudSDKException;
//import lombok.extern.slf4j.Slf4j;
//import org.flowable.engine.RuntimeService;
//import org.springblade.core.mp.base.BaseServiceImpl;
//import org.springblade.core.tool.jackson.JsonUtil;
//import org.springblade.modules.resource.entity.Attach;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.util.*;
//
///**
// * 员工电子签
// */
//@Service
//@Slf4j
//public class EmployeeElectronicSignatureServiceImpl extends BaseServiceImpl<EmployeeElectronicSignatureMapper, EmployeeElectronicSignatureEntity> implements IEmployeeElectronicSignatureService {
//
//
//	@Autowired
//	TencentElectronicSignatureHelper helper;
//	@Autowired
//	RuntimeService runtimeService;
//	@Autowired
//	FIleHelper fIleHelper;
//	/**
//	 * 完成电子签
//	 *
//	 * @param map
//	 */
//	@Override
//	public void finishSign(Map<String,Object> map) {
//		Map<String,Object> data = (Map<String, Object>) map.get("MsgData");
//		Long employeeId = helper.getEmployeeId(data);
//		String flowId = helper.getFlowId(data);
//		String processFlowId = helper.getProcessFlowId(data);
//		// 获取已经存在的电子签信息
//		List<EmployeeElectronicSignatureEntity> signList = baseMapper.selectList(new LambdaQueryWrapper<EmployeeElectronicSignatureEntity>()
//			.eq(EmployeeElectronicSignatureEntity::getIsDeleted, PropertyConst.YesNo.NO.value())
//			.eq(EmployeeElectronicSignatureEntity::getEmployeeId, employeeId)
//			.eq(EmployeeElectronicSignatureEntity::getFlowId, flowId)
//			.eq(EmployeeElectronicSignatureEntity::getType, ElectronicSignatureEnums.Type.EmployeeOnboarding.code)
//			.eq(EmployeeElectronicSignatureEntity::getBizId, processFlowId)
//			.orderByDesc(EmployeeElectronicSignatureEntity::getCreateTime)
//		);
//		if (CollectionUtils.isEmpty(signList)){
//			log.error("员工电子签 完成入职流程 查询不到已存在记录:{}", JsonUtil.toJson(data));
//			// TODO 看后续要不要记录处理
//			return;
//		}
//		// 取最后一条记录
//		EmployeeElectronicSignatureEntity entity = signList.get(0);
//		ElectronicSignatureEnums.Status lastRecordStatus = ElectronicSignatureEnums.Status.findByCode(entity.getStatus());
//		if (Objects.isNull(lastRecordStatus)){
//			log.error("员工电子签 完成入职流程 脏数据 记录{}状态不正确",JsonUtil.toJson(entity));
//			return;
//		}
//		if (lastRecordStatus.isFinish){
//			log.warn("员工电子签 完成入职流程 记录已经是完成状态:{}",JsonUtil.toJson(entity));
//			return;
//		}
//
//		ElectronicSignatureEnums.Status flowStatus = ElectronicSignatureEnums.Status.findByName(helper.getStatus(data));
//		// 修改状态 追加数据
//		String original = JsonUtil.toJson(data);
//		entity.setStatus(flowStatus.code);
//		entity.setFlowStatus(flowStatus.name());
//		entity.setUpdateTime(new Date());
//		entity.setCallback(original);
//		entity.setCallbackDate(new Date());
//
//		if (flowStatus.isFinish){
//			log.info("员工电子签 完成入职流程 员工合同已完成 获取下载路径:{}",original);
//			String url = helper.getFileDownloadByFlowId(flowId);
//			entity.setResourceUrl(url);
//			Attach attach = fIleHelper.downloadAndSaveAttach("入职流程签署文件.pdf","employee_"+entity.getEmployeeId(),url);
//			changeProcessFlow(entity.getEmployeeId(),entity.getBizId(),attach);
//		}
//		baseMapper.updateById(entity);
//	}
//
//	/**
//	 * 文档填充后
//	 *
//	 * @param data
//	 */
//	@Override
//	public void fillDocumentAfter(Map<String,Object> data) {
//		Map<String,Object> map = (Map<String, Object>) data.get("MsgData");
//		Long employeeId = helper.getEmployeeId(map);
//		String flowId = helper.getFlowId(map);
//		String processFlowId = helper.getProcessFlowId(map);
//
//		// 获取已经存在的电子签信息
//		List<EmployeeElectronicSignatureEntity> signList = baseMapper.selectList(new LambdaQueryWrapper<EmployeeElectronicSignatureEntity>()
//			.eq(EmployeeElectronicSignatureEntity::getIsDeleted, PropertyConst.YesNo.NO.value())
//			.eq(EmployeeElectronicSignatureEntity::getEmployeeId, employeeId)
//			.eq(EmployeeElectronicSignatureEntity::getFlowId, flowId)
//			.eq(EmployeeElectronicSignatureEntity::getType, ElectronicSignatureEnums.Type.EmployeeOnboarding.code)
//			.eq(EmployeeElectronicSignatureEntity::getBizId, processFlowId)
//			.orderByDesc(EmployeeElectronicSignatureEntity::getCreateTime)
//		);
//		if (CollectionUtils.isEmpty(signList)){
//			log.error("员工电子签 填充合同文档 查询不到已存在记录:{}", JsonUtil.toJson(data));
//			return;
//		}
//		// 取最后一条记录
//		EmployeeElectronicSignatureEntity entity = signList.get(0);
//		ElectronicSignatureEnums.Status lastRecordStatus = ElectronicSignatureEnums.Status.findByCode(entity.getStatus());
//		if (Objects.isNull(lastRecordStatus)){
//			log.error("员工电子签 填充合同文档 脏数据 记录{}状态不正确",JsonUtil.toJson(entity));
//			return;
//		}
//		if (lastRecordStatus.isFinish){
//			log.warn("员工电子签 填充合同文档 记录已经是完成状态:{}",JsonUtil.toJson(entity));
//			return;
//		}
//		// 修改状态 追加数据
//		entity.setUpdateTime(new Date());
////		ElectronicSignatureEnums.Status flowStatus = ElectronicSignatureEnums.Status.findByName(helper.getStatus(map));
////		String original = JsonUtil.toJson(map);
////		entity.setStatus(flowStatus.code);
////		entity.setFlowStatus(flowStatus.name());
////		entity.setCallback(original);
////		entity.setCallbackDate(new Date());
//		entity.setDocumentUrl(helper.getDocumentUrl(map));
//		baseMapper.updateById(entity);
//
//
//	}
//
//	private void changeProcessFlow(Long employeeId, String processId, Attach attach) {
//		Object filesObj = runtimeService.getVariable(processId, EmployeeOnBoardingHelper.Files_Key);
//		List<EmployeeFileEntity> files = (List<EmployeeFileEntity>) filesObj;
//		EmployeeFileEntity file = new EmployeeFileEntity();
//		file.setEmployeeId(employeeId);
//		file.setFileId(attach.getId());
//		file.setFileUrl(attach.getLink());
//		file.setType(HireNode.TRAINING_HR.getFileType());
//		file.setName(attach.getName());
//		file.setIsDeleted(PropertyConst.YesNo.NO.value());
//		files.add(file);
//		runtimeService.setVariable(processId, EmployeeOnBoardingHelper.Files_Key, files);
//	}
//
//	/**
//	 * 发起入职电子签流程
//	 *
//	 * @param id
//	 * @param fullInfo
//	 * @param userData
//	 */
//	@Override
//	public void startOnBoardingESign(Long id, FullEmployeeInfoVO fullInfo, Map<String, String> userData) throws TencentCloudSDKException {
//		EmployeeElectronicSignatureEntity entity = helper.startEmployeeOnboardingFlow(fullInfo, JsonUtil.toJson(userData));
//		entity.setBizId(userData.get(EmployeeOnBoardingHelper.PROCESS_ID));
//		entity.setEmployeeId(id);
//		entity.setStatus(ElectronicSignatureEnums.Status.INIT.code);
//		entity.setCreateTime(new Date());
//		entity.setFlowStatus(ElectronicSignatureEnums.Status.INIT.name());
//		entity.setIsDeleted(PropertyConst.YesNo.NO.value());
//		baseMapper.insert(entity);
//		log.info("员工电子签 入职流程 发起电子签{}",JsonUtil.toJson(fullInfo));
//	}
//
//	@Override
//	public EmployeeElectronicSignatureEntity findByProcessIdAndEmployeeId(String processId, Long employeeId) {
//		List<EmployeeElectronicSignatureEntity> signList = baseMapper.selectList(new LambdaQueryWrapper<EmployeeElectronicSignatureEntity>()
//				.eq(EmployeeElectronicSignatureEntity::getIsDeleted, PropertyConst.YesNo.NO.value())
//				.eq(EmployeeElectronicSignatureEntity::getEmployeeId, employeeId)
//				.eq(EmployeeElectronicSignatureEntity::getType, ElectronicSignatureEnums.Type.EmployeeOnboarding.code)
//				.eq(EmployeeElectronicSignatureEntity::getBizId, processId)
//				.orderByDesc(EmployeeElectronicSignatureEntity::getCreateTime));
//		if (CollectionUtils.isEmpty(signList)){
//			return new EmployeeElectronicSignatureEntity();
//		}
//		return  signList.get(0);
//	}
//}
