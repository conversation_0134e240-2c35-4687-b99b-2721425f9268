package com.natergy.ni.emhire.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.natergy.ni.emhire.entity.EmployeeResumeInfoEntity;
import com.natergy.ni.emhire.enums.ResumeStatus;
import com.natergy.ni.emhire.service.IEmployeeResumeInfoService;
import com.natergy.ni.emhire.vo.ParseResumeResultVO;
import com.natergy.ni.pa.util.RestTemplateUtil;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.modules.desk.service.IUserNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.text.MessageFormat;
import java.util.*;

/**
 * 简历上传完成事件监听器
 * <AUTHOR>
 */
@Component
@Slf4j
public class EmployeeResumeUploadEventListener {



	@Value("${ni.resume.aiParserUrl}")
	private String aiParserUrl;
	@Autowired
	private IEmployeeResumeInfoService employeeResumeInfoService;
	@Autowired
	private IUserNoticeService userNoticeService;

	/**
	 * AI响应返回值
	 */
	@Data
	@ToString
	public static class AiParserResponse {
		private boolean success;
		private String error;
		private Long  timestamp;
		private Map<String,Object> data;
	}

	/**
	 * 简历上传完成事件
	 * 在事物提交后触发
	 * @param event
	 */
	@TransactionalEventListener
	@Async
	public void onResumeUpload(EmployeeResumeUploadEvent event) {
		log.info("简历上传事件监听器 Received :{}", JsonUtil.toJson(event));
		try {
			String result = RestTemplateUtil.postForJSON(aiParserUrl, event, String.class);
			log.info("简历上传事件监听器 发送简历分析 结果:{}", result);
			AiParserResponse resp = JsonUtil.parse(result, AiParserResponse.class);
			EmployeeResumeInfoEntity obj = employeeResumeInfoService.getOne(new LambdaQueryWrapper<EmployeeResumeInfoEntity>().eq(EmployeeResumeInfoEntity::getResumeId, event.getResumeId()),false);
			obj.setParseTime(new Date());
			if (resp.success){
				String source = JsonUtil.toJson(resp.data);
				ParseResumeResultVO vo = JsonUtil.parse(source, ParseResumeResultVO.class);
				ParseResumeResultVO.ResumeInfo resumeInfo = vo.getResumeInfo();
				obj.setName(resumeInfo.getName());
				obj.setEducation(resumeInfo.getEducation());
				obj.setPhone(resumeInfo.getPhone());
				if (NumberUtils.isCreatable(resumeInfo.getAge())){
					obj.setAge(Integer.parseInt(resumeInfo.getAge()));
				}
				if (StringUtils.isNotBlank(resumeInfo.getGender())){
					obj.setGender(Objects.equals(resumeInfo.getGender(),"男") ? 1 : 2);
				}
				obj.setRemend(vo.getRemend());
				obj.setResumeStatus(ResumeStatus.ParsingSuccess.code);
				obj.setAiResult(source);
			}else{
				obj.setResumeStatus(ResumeStatus.ParsingFailed.code);
			}
			employeeResumeInfoService.updateById(obj);
			sendNotice(obj.getId(),obj.getName());
			log.info("简历上传事件监听器 更新简历:{} 详情:{}",event.getResumeId(), JsonUtil.toJson(obj));
		}catch (Exception e) {
			EmployeeResumeInfoEntity obj = new EmployeeResumeInfoEntity();
			obj.setParseTime(new Date());
			obj.setResumeStatus(ResumeStatus.ParsingFailed.code);
			obj.setAiResult(e.getMessage());
			employeeResumeInfoService.update(obj,new LambdaQueryWrapper<EmployeeResumeInfoEntity>().eq(EmployeeResumeInfoEntity::getResumeId, event.getResumeId()));
			e.printStackTrace();
			log.error("简历上传事件监听器 更新简历 解析失败:{}", event.getResumeId(), e);
		}

	}

	/**
	 * 推送消息到各个部门
	 * @param id
	 * @param name
	 */
	private void sendNotice(Long id, String name) {
		// 推送消息 到 部门主管 并附带跳转连接
		try {
			String jumpUrl="/ni/pa/emhire/niEmployeeResumeInfoAvue";
			List<Long> list = employeeResumeInfoService.findResumeInfoRelDeptLeader(id);
			for (Long deptLeaderId : list) {
				userNoticeService.pushNotice("notice", jumpUrl,"简历推送提醒", MessageFormat.format("您收到一份关于 {0} 的简历推送，请及时查看。",name),
					Collections.singletonList(deptLeaderId));
			}
		}catch (Exception e) {
			log.error("简历上传事件监听器 推送消息失败");
		}
	}

}
