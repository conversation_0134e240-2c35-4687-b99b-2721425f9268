package com.natergy.ni.emhire.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 简历上传完成事件
 * <AUTHOR>
 */
@Getter
@Setter
public class EmployeeResumeUploadEvent extends ApplicationEvent {
	/**
	 * 简历ID
	 */
	private String resumeId;
	/**
	 * AI提示词
	 */
	private String aiPrompt;
	/**
	 * 下载路径
	 */
	private String downloadUrl;
	/**
	 * 部门
	 */
	private List<String> deptNames;

	public EmployeeResumeUploadEvent(Object source) {
		super(source);
	}

	public EmployeeResumeUploadEvent(String resumeId, String downloadUrl, String aiPrompt, List<String> deptNames) {
		super(resumeId);
		this.resumeId = resumeId;
		this.aiPrompt = aiPrompt;
		this.downloadUrl = downloadUrl;
		this.deptNames = deptNames;
	}
}
