/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 简历表 实体类
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@TableName("ni_employee_resume_info")
@ApiModel(value = "EmployeeResumeInfo对象", description = "简历表")
@EqualsAndHashCode(callSuper = true)
public class EmployeeResumeInfoEntity extends TenantEntity {

	/**
	 * 简历ID
	 */
	@ApiModelProperty(value = "简历ID")
	private String resumeId;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 意向岗位
	 */
	@ApiModelProperty(value = "意向岗位")
	private String position;
	/**
	 * 学历
	 */
	@ApiModelProperty(value = "学历")
	private String education;
	/**
	 * 年龄
	 */
	@ApiModelProperty(value = "年龄")
	private Integer age;
	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private Integer gender;
	/**
	 * 联系电话
	 */
	@ApiModelProperty(value = "联系电话")
	private String phone;
	/**
	 * 身份证号
	 */
	@ApiModelProperty(value = "身份证号")
	private String idCard;
	/**
	 * 招聘渠道 0社招 1校招
	 */
	@ApiModelProperty(value = "招聘渠道 0社招 1校招")
	private Integer recruitmentChannels;
	/**
	 * AI分析提示词
	 */
	@ApiModelProperty(value = "AI分析提示词")
	private String aiPrompt;
	/**
	 * AI分析结果(json)
	 */
	@ApiModelProperty(value = "AI分析结果(json)")
	private String aiResult;
	/**
	 * 推荐值
	 */
	@ApiModelProperty(value = "推荐值")
	private Integer remend;
	/**
	 * 附件ID
	 */
	@ApiModelProperty(value = "附件ID")
	private Long attachmentId;
	/**
	 * 简历下载地址
	 */
	@ApiModelProperty(value = "简历下载地址")
	private String downloadUrl;
	/**
	 * 是否发送offer
	 */
	@ApiModelProperty(value = "是否发送offer")
	private Integer sendOffer;
	/**
	 * 是否跳过面试流程
	 */
	@ApiModelProperty(value = "是否跳过面试流程")
	private Integer skipExamination;
	/**
	 * 是否最新版本
	 */
	@ApiModelProperty(value = "是否最新版本")
	private Integer latest;
	/**
	 * 版本号
	 */
	@ApiModelProperty(value = "版本号")
	private Integer version;
	/**
	 * 上传时间
	 */
	@ApiModelProperty(value = "上传时间")
	private Date uploadTime;
	/**
	 * 解析完成时间
	 */
	@ApiModelProperty(value = "解析完成时间")
	private Date parseTime;

	@ApiModelProperty(value = "简历状态")
	private Integer resumeStatus;


}
