/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 员工家庭成员表 实体类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@TableName("ni_hr_employee_family_members")
@ApiModel(value = "EmployeeFamilyMembers对象", description = "员工家庭成员表")
@EqualsAndHashCode(callSuper = true)
public class EmployeeFamilyMembersEntity extends BaseEntity {

	/**
	 * 家庭成员名称
	 */
	@ApiModelProperty(value = "家庭成员名称")
	private Integer name;
	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String phone;
	/**
	 * 家属关系
	 */
	@ApiModelProperty(value = "家属关系")
	private Integer relationship;
	/**
	 * 工作地址
	 */
	@ApiModelProperty(value = "工作地址")
	private String workAddress;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private Long employeeId;

}
