/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 员工学历信息表 实体类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@TableName("ni_hr_employee_education")
@ApiModel(value = "EmployeeEducation对象", description = "员工学历信息表")
@EqualsAndHashCode(callSuper = true)
public class EmployeeEducationEntity extends BaseEntity {

	@ApiModelProperty("起始时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate startDate;
	@ApiModelProperty("截止时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate endDate;
	/**
	 * 学历类型
	 */
	@ApiModelProperty(value = "学历类型")
	private Integer type;
	/**
	 * 学校
	 */
	@ApiModelProperty(value = "学校")
	private String school;
	/**
	 * 专业
	 */
	@ApiModelProperty(value = "专业")
	private String major;
	/**
	 * 学习时长
	 */
	@ApiModelProperty(value = "学习时长")
	private Integer year;
	/**
	 * 学历级别
	 */
	@ApiModelProperty(value = "学历级别")
	private String education;
	/**
	 * 学位
	 */
	@ApiModelProperty(value = "学位")
	private String degree;
	/**
	 * 学位附件ID
	 */
	@ApiModelProperty(value = "学位附件ID")
	private Long degreeFileId;
	/**
	 * 毕业证
	 */
	@ApiModelProperty(value = "毕业证")
	private String graduation;
	/**
	 * 毕业证附件ID
	 */
	@ApiModelProperty(value = "毕业证附件ID")
	private Long graduationFileId;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private Long employeeId;

}
