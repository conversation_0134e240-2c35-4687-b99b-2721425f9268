/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 简历部门中间表 实体类
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@TableName("ni_employee_resume_dept_rel")
@ApiModel(value = "EmployeeResumeDeptRel对象", description = "简历部门中间表")
public class EmployeeResumeDeptRelEntity  extends BaseEntity {

	private Long deptId;
	private String resumeId;

	public EmployeeResumeDeptRelEntity() {}
	public EmployeeResumeDeptRelEntity(Long deptId, String resumeId) {
		this.deptId = deptId;
		this.resumeId = resumeId;
	}
}
