/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 员工档案附件表 实体类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@TableName("ni_hr_employee_file")
@ApiModel(value = "EmployeeFile对象", description = "员工档案附件表")
@EqualsAndHashCode(callSuper = true)
public class EmployeeFileEntity extends BaseEntity {

	/**
	 * 上传时间
	 */
	@ApiModelProperty(value = "上传时间")
	private Date updateDate;
	/**
	 * 文件类型
	 */
	@ApiModelProperty(value = "文件类型")
	private Integer type;
	/**
	 * 文件名
	 */
	@ApiModelProperty(value = "文件名")
	private String name;
	/**
	 * 扩展
	 */
	@ApiModelProperty(value = "扩展")
	private String ext;
	/**
	 * 文件url
	 */
	@ApiModelProperty(value = "文件url")
	private String fileUrl;
	/**
	 * 文件id
	 */
	@ApiModelProperty(value = "文件id")
	private Long fileId;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private Long employeeId;

}
