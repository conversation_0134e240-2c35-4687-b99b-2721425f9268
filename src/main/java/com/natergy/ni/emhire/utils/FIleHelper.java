package com.natergy.ni.emhire.utils;

import com.natergy.ni.pa.util.RestTemplateUtil;
import org.springblade.common.utils.HttpUtils;
import org.springblade.core.http.util.HttpUtil;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springblade.modules.resource.builder.oss.OssBuilder;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.entity.Oss;
import org.springblade.modules.resource.service.IAttachService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;

import static org.springblade.core.tool.utils.FileUtil.getFileExtension;

/**
 * 文件辅助类
 * 用于处理下载 上传
 */
@Component
public class FIleHelper {

	@Autowired
	private  OssBuilder ossBuilder;
 	@Autowired
	private  IAttachService attachService;


	public byte[] downloadFile(String fileUrl) {
		return RestTemplateUtil.download(fileUrl);
	}

	public Attach saveFile(String fileName,String businessKey,byte[] fileContent) {
		BladeFile bladeFile = ossBuilder.template().putFile(fileName, new ByteArrayInputStream(fileContent));
		Oss oss = ossBuilder.getOss(TokenUtil.DEFAULT_TENANT_ID, null);
		String fileExtension = getFileExtension(fileName);
		Attach attach = new Attach();
		attach.setDomainUrl(bladeFile.getDomain());
		attach.setLink(bladeFile.getLink());
		attach.setName(bladeFile.getName());
		attach.setOriginalName(bladeFile.getOriginalName());
		attach.setAttachSize((long) fileContent.length);
		attach.setExtension(fileExtension);
		attach.setBusinessName(fileName);
		attach.setBusinessKey(businessKey);
		attach.setOssCode(oss.getOssCode());
		attachService.save(attach);
		return attach;
	}

	public Attach downloadAndSaveAttach(String fileName,String bizKey,String documentUrl) {
		byte[] fs = downloadFile(documentUrl);
		return saveFile(fileName, bizKey, fs);
	}
}
