/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.mapper;

import com.natergy.ni.emhire.entity.EmployeeResumeInterviewEntity;
import com.natergy.ni.emhire.vo.EmployeeResumeInterviewVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 简历面试记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface EmployeeResumeInterviewMapper extends BaseMapper<EmployeeResumeInterviewEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param niEmployeeResumeInterview
	 * @return
	 */
	List<EmployeeResumeInterviewVO> selectEmployeeResumeInterviewPage(IPage page, EmployeeResumeInterviewVO niEmployeeResumeInterview);


}
