<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.emhire.mapper.EmployeeFamilyMembersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="niHrEmployeeFamilyMembersResultMap" type="com.natergy.ni.emhire.entity.EmployeeFamilyMembersEntity">
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="relationship" property="relationship"/>
        <result column="work_address" property="workAddress"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="employee_id" property="employeeId"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectEmployeeFamilyMembersPage" resultMap="niHrEmployeeFamilyMembersResultMap">
        select * from ni_hr_employee_family_members where is_deleted = 0
    </select>


</mapper>
