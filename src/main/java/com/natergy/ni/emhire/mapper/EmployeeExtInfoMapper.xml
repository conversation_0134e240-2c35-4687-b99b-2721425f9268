<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.emhire.mapper.EmployeeExtInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="niHrEmployeeExtInfoResultMap" type="com.natergy.ni.emhire.entity.EmployeeExtInfoEntity">
        <result column="employee_id" property="employeeId"/>
        <result column="household_register" property="householdRegister"/>
        <result column="household_register_code" property="householdRegisterCode"/>
        <result column="marriage" property="marriage"/>
        <result column="politic" property="politic"/>
        <result column="religion" property="religion"/>
        <result column="current_address" property="currentAddress"/>
        <result column="current_address_code" property="currentAddressCode"/>
        <result column="professional_title" property="professionalTitle"/>
        <result column="driver_license" property="driverLicense"/>
        <result column="driving_experience_years" property="drivingExperienceYears"/>
        <result column="hobbies" property="hobbies"/>
        <result column="english_proficiency" property="englishProficiency"/>
        <result column="has_intellectual_property " property="hasIntellectualProperty "/>
        <result column="has_competition_restriction" property="hasCompetitionRestriction"/>
        <result column="research_field" property="researchField"/>
        <result column="family_address" property="familyAddress"/>
        <result column="family_address_code" property="familyAddressCode"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectEmployeeExtInfoPage" resultMap="niHrEmployeeExtInfoResultMap">
        select * from ni_hr_employee_ext_info where is_deleted = 0
    </select>


</mapper>
