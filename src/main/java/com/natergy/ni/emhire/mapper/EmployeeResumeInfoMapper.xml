<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.emhire.mapper.EmployeeResumeInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="niEmployeeResumeInfoResultMap" type="com.natergy.ni.emhire.entity.EmployeeResumeInfoEntity">
        <result column="resume_id" property="resumeId"/>
        <result column="name" property="name"/>
        <result column="position" property="position"/>
        <result column="education" property="education"/>
        <result column="age" property="age"/>
        <result column="gender" property="gender"/>
        <result column="phone" property="phone"/>
        <result column="id_card" property="idCard"/>
        <result column="recruitment_channels" property="recruitmentChannels"/>
        <result column="ai_prompt" property="aiPrompt"/>
        <result column="ai_result" property="aiResult"/>
        <result column="status" property="status"/>
        <result column="remend" property="remend"/>
        <result column="attachment_id" property="attachmentId"/>
        <result column="download_url" property="downloadUrl"/>
        <result column="send_offer" property="sendOffer"/>
        <result column="skip_examination" property="skipExamination"/>
        <result column="latest" property="latest"/>
        <result column="version" property="version"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="parse_time" property="parseTime"/>
        <result column="resume_status" property="resumeStatus"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectEmployeeResumeInfoPage" resultMap="niEmployeeResumeInfoResultMap">
        select * from ni_employee_resume_info where is_deleted = 0
    </select>

    <select id="pageResumeByDeptId" resultMap="niEmployeeResumeInfoResultMap">
        select
            ni_employee_resume_info.*
        from
            ni_employee_resume_info ni_employee_resume_info
        join ni_employee_resume_dept_rel ni_employee_resume_dept_rel
        on ni_employee_resume_info.resume_id = ni_employee_resume_dept_rel.resume_id
        where ni_employee_resume_info.is_deleted = 0
          and ni_employee_resume_dept_rel.dept_id = #{deptId}
          and ni_employee_resume_info.upload_time > #{period}
          and ni_employee_resume_info.latest = 1
          and ni_employee_resume_info.resume_status in (3,4,5,6,7)
        order by ni_employee_resume_info.remend
    </select>


</mapper>
