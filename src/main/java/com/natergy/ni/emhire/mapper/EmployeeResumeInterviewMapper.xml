<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.emhire.mapper.EmployeeResumeInterviewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="niEmployeeResumeInterviewResultMap" type="com.natergy.ni.emhire.entity.EmployeeResumeInterviewEntity">
        <result column="resume_id" property="resumeId"/>
        <result column="dept_id" property="deptId"/>
        <result column="position" property="position"/>
        <result column="interviewer" property="interviewer"/>
        <result column="interviewer_id" property="interviewerId"/>
        <result column="interview_time" property="interviewTime"/>
        <result column="interview_result" property="interviewResult"/>
        <result column="interview_summary" property="interviewSummary"/>
        <result column="attachment_id" property="attachmentId"/>
        <result column="attachment_url" property="attachmentUrl"/>
        <result column="is_deleted" property="is_deleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectEmployeeResumeInterviewPage" resultMap="niEmployeeResumeInterviewResultMap">
        select * from ni_employee_resume_interview where is_deleted = 0
    </select>


</mapper>
