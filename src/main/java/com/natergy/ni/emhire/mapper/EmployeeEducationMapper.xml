<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.emhire.mapper.EmployeeEducationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="niHrEmployeeEducationResultMap" type="com.natergy.ni.emhire.entity.EmployeeEducationEntity">
        <result column="type" property="type"/>
        <result column="school" property="school"/>
        <result column="major" property="major"/>
        <result column="year" property="year"/>
        <result column="education" property="education"/>
        <result column="degree" property="degree"/>
        <result column="degree_file_id" property="degreeFileId"/>
        <result column="graduation" property="graduation"/>
        <result column="graduation_file_id" property="graduationFileId"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="employee_id" property="employeeId"/>
        <result column="id" property="id"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_user" property="createUser"/>
    </resultMap>


    <select id="selectEmployeeEducationPage" resultMap="niHrEmployeeEducationResultMap">
        select * from ni_hr_employee_education where is_deleted = 0
    </select>


</mapper>
