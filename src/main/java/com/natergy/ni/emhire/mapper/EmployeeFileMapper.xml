<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.emhire.mapper.EmployeeFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="niHrEmployeeFileResultMap" type="com.natergy.ni.emhire.entity.EmployeeFileEntity">
        <result column="update_date" property="updateDate"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="ext" property="ext"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_id" property="fileId"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_user" property="createUser"/>
        <result column="employee_id" property="employeeId"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectEmployeeFilePage" resultMap="niHrEmployeeFileResultMap">
        select * from ni_hr_employee_file where is_deleted = 0
    </select>


</mapper>
