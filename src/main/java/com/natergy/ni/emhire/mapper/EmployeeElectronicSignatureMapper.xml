<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.emhire.mapper.EmployeeElectronicSignatureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="niHrEmployeeElectronicSignatureResultMap" type="com.natergy.ni.emhire.entity.EmployeeElectronicSignatureEntity">
        <result column="employee_id" property="employeeId"/>

        <result column="template_id" property="templateId"/>
        <result column="flow_id" property="flowId"/>
        <result column="flow_name" property="flowName"/>
        <result column="biz_id" property="bizId"/>

        <result column="type" property="type"/>
        <result column="document_id" property="documentId"/>
        <result column="flow_status" property="flowStatus"/>
        <result column="document_url" property="documentUrl"/>
        <result column="resource_url" property="resourceUrl"/>
        <result column="callback" property="callback"/>
        <result column="callback_date" property="callbackDate"/>

        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectEmployeeExtInfoPage" resultMap="niHrEmployeeElectronicSignatureResultMap">
        select * from ni_hr_employee_electronic_signature where is_deleted = 0
    </select>


</mapper>
