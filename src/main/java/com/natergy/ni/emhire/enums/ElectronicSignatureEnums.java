package com.natergy.ni.emhire.enums;

public class ElectronicSignatureEnums {
	public enum Type {
		EmployeeOnboarding(1);
		public Integer code;
		Type(int i) {
			this.code = i;
		}


	}

	/**
	 * 回调状态
	 */
	public enum Status {
		INIT(1,"合同创建",false),
		PART(2,"合同签署中",false),
		REJECT(3,"合同拒签",true),
		ALL(4,"合同签署完成",true),
		DEADLINE(5,"合同流签(合同过期)",true),
		CANCEL(6,"合同撤回",true),
		WAIT(8,"合同待填写",false),
		RELIEVED(21,"已解除",true),
		INVALID(16,"合同已失效",false),
		UNKNOW(99,"未知状态",false);

		public Integer code;
		public Boolean isFinish;
		Status(int code, String desc, boolean finished) {
			this.code = code;
			this.isFinish = finished;
		}
		public static Status findByCode(Integer status) {
			for (Status s : Status.values()) {
				if (s.code.equals(status)) {
					return s;
				}
			}
			return UNKNOW;
		}
		public static Status findByName(String name){
			for (Status s : Status.values()) {
				if (s.name().equals(name)) {
					return s;
				}
			}
			return UNKNOW;
		}
	}
	public enum Operate{
		start(),
		sign(),
		reject(),
		cancel(),
		finish(),
		deadline(),
		invalid(),
		fill();
	}
}
