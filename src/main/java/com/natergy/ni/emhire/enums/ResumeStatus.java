package com.natergy.ni.emhire.enums;

/**
 * 简历状态枚举
 */
public enum ResumeStatus {
	/**
	 * 刚刚上传
	 */
	Upload(1),
	/**
	 * 解析失败
	 */
	ParsingFailed(2),
	/**
	 * 解析成功
	 */
	ParsingSuccess(3),
	/**
	 * 约面试
	 * 用人部门发起邀约
	 */
	Invite(4),
	/**
	 * 面试
	 * 由 HR 发起邀约后
	 */
	Interview(5),
	/**
	 * 已评价
	 * 面试后上传完面评
	 */
	Evaluated(6),
	/**
	 * 面试通过
	 */
	Pass(7),
	/**
	 * 入职
	 */
	Onboarding(8);

	public Integer code;
	ResumeStatus(int code) {
		this.code=code;
	}
}
