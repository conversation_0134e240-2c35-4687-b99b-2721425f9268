/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.emhire.entity.EmployeeResumeInterviewEntity;
import com.natergy.ni.emhire.vo.EmployeeResumeInterviewVO;
import com.natergy.ni.emhire.wrapper.EmployeeResumeInterviewWrapper;
import com.natergy.ni.emhire.service.IEmployeeResumeInterviewService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 简历面试记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("EmployeeResumeInterview/niEmployeeResumeInterview")
@Api(value = "简历面试记录表", tags = "简历面试记录表接口")
public class EmployeeResumeInterviewController extends BladeController {

	private final IEmployeeResumeInterviewService niEmployeeResumeInterviewService;

	/**
	 * 简历面试记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入niEmployeeResumeInterview")
	public R<EmployeeResumeInterviewVO> detail(EmployeeResumeInterviewEntity niEmployeeResumeInterview) {
		EmployeeResumeInterviewEntity detail = niEmployeeResumeInterviewService.getOne(Condition.getQueryWrapper(niEmployeeResumeInterview));
		return R.data(EmployeeResumeInterviewWrapper.build().entityVO(detail));
	}
	/**
	 * 简历面试记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入niEmployeeResumeInterview")
	public R<IPage<EmployeeResumeInterviewVO>> list(EmployeeResumeInterviewEntity niEmployeeResumeInterview, Query query) {
		IPage<EmployeeResumeInterviewEntity> pages = niEmployeeResumeInterviewService.page(Condition.getPage(query), Condition.getQueryWrapper(niEmployeeResumeInterview));
		return R.data(EmployeeResumeInterviewWrapper.build().pageVO(pages));
	}

	/**
	 * 简历面试记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入niEmployeeResumeInterview")
	public R<IPage<EmployeeResumeInterviewVO>> page(EmployeeResumeInterviewVO niEmployeeResumeInterview, Query query) {
		IPage<EmployeeResumeInterviewVO> pages = niEmployeeResumeInterviewService.selectEmployeeResumeInterviewPage(Condition.getPage(query), niEmployeeResumeInterview);
		return R.data(pages);
	}

	/**
	 * 简历面试记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入niEmployeeResumeInterview")
	public R save(@Valid @RequestBody EmployeeResumeInterviewEntity niEmployeeResumeInterview) {
		return R.status(niEmployeeResumeInterviewService.save(niEmployeeResumeInterview));
	}

	/**
	 * 简历面试记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入niEmployeeResumeInterview")
	public R update(@Valid @RequestBody EmployeeResumeInterviewEntity niEmployeeResumeInterview) {
		return R.status(niEmployeeResumeInterviewService.updateById(niEmployeeResumeInterview));
	}

	/**
	 * 简历面试记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入niEmployeeResumeInterview")
	public R submit(@Valid @RequestBody EmployeeResumeInterviewEntity niEmployeeResumeInterview) {
		return R.status(niEmployeeResumeInterviewService.saveOrUpdate(niEmployeeResumeInterview));
	}

	/**
	 * 简历面试记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(niEmployeeResumeInterviewService.deleteLogic(Func.toLongList(ids)));
	}


}
