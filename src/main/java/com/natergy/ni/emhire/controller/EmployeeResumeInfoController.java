/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.emhire.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.natergy.ni.base.constant.PropertyConst;
import com.natergy.ni.emhire.vo.EmployeeResumeInterviewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.emhire.entity.EmployeeResumeInfoEntity;
import com.natergy.ni.emhire.vo.EmployeeResumeInfoVO;
import com.natergy.ni.emhire.wrapper.EmployeeResumeInfoWrapper;
import com.natergy.ni.emhire.service.IEmployeeResumeInfoService;
import org.springblade.core.boot.ctrl.BladeController;

import java.math.BigInteger;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 简历表 控制器
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("EmployeeResumeInfo/niEmployeeResumeInfo")
@Api(value = "简历表", tags = "简历表接口")
public class EmployeeResumeInfoController extends BladeController {

	private final IEmployeeResumeInfoService employeeResumeInfoService;

	/**
	 * 批量上传简历
	 * @param saveInfoVO
	 * @return
	 */
	@PostMapping("/batch")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "批量上传简历", notes = "传入niEmployeeResumeInfo")
	public R<EmployeeResumeInfoVO> batchUpload(@RequestBody EmployeeResumeInfoVO.BatchSaveInfoVO saveInfoVO) {
		if (CollectionUtil.isEmpty(saveInfoVO.getFiles())){
			return R.fail("缺少简历");
		}
		if (CollectionUtil.isEmpty(saveInfoVO.getDepartments())){
			return R.fail("缺少部门");
		}
		employeeResumeInfoService.batch(saveInfoVO);
		return R.status(Boolean.TRUE);
	}

	/**
	 * 重新分析
	 * @param id
	 * @param prompt
	 * @return
	 */
	@PostMapping("reanalysis")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "重新分析",notes = "传ID与提示词")
	public R<EmployeeResumeInfoVO> reanalysis(@RequestParam Long id,String prompt) {
		return R.data(employeeResumeInfoService.reanalysis(id,prompt));
	}

	/**
	 * 用人部门感兴趣
	 * 产生面试记录
	 * @param id
	 * @return
	 */
	@PostMapping("interested")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "用人部门感兴趣",notes = "传递简历ID")
	public R<EmployeeResumeInfoVO> interested(@RequestParam Long id,String position, @DateTimeFormat(pattern = "yyyy-MM-dd") Date expectedTime) {
		String mes = employeeResumeInfoService.inviteOnDept(id,position,expectedTime);
		return R.success(mes);
	}

	/**
	 * 查询简历面试邀请记录
	 * @param id
	 * @return
	 */
	@GetMapping("inviteRecord")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "查询简历面试邀请记录")
	public R<List<EmployeeResumeInterviewVO>> inviteRecord(@RequestParam Long id) {
		List<EmployeeResumeInterviewVO> result = employeeResumeInfoService.listInviteRecord(id);
		return R.data(result);
	}
	/**
	 * HR填写面试邀请信息
	 * @param id
	 * @return
	 */
	@PostMapping("invite")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "HR填写邀请面试信息",notes = "传递简历ID,面试官及面试时间")
	public R<EmployeeResumeInfoVO> invite(@RequestParam Long id,Long recordId,Long interviewer,String position, @DateTimeFormat(pattern = "yyyy-MM-dd") Date interviewTime) {
		String mes = employeeResumeInfoService.invite(id,recordId,position,interviewer,interviewTime);
		return R.success(mes);
	}
	/**
	 * 简历表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "详情", notes = "传入niEmployeeResumeInfo")
	public R<EmployeeResumeInfoVO> detail(Long id) {
		EmployeeResumeInfoVO detail = employeeResumeInfoService.details(id);
		return R.data(detail);
	}

	/**
	 * 录入面评
	 * @return
	 */
	@PostMapping("evaluation")
	@ApiOperation(value = "录入面评",notes = "录入面评")
	public R<String> evaluation(@RequestBody EmployeeResumeInfoVO.EvaluationInfoVO evaluationInfoVO){
		employeeResumeInfoService.evaluation(evaluationInfoVO);
		return R.success("操作成功");
	}

	/**
	 * 简历表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "分页", notes = "传入niEmployeeResumeInfo")
	public R<IPage<EmployeeResumeInfoVO>> list(EmployeeResumeInfoEntity niEmployeeResumeInfo, Query query) {
		IPage<EmployeeResumeInfoEntity> pages = employeeResumeInfoService.page(Condition.getPage(query),Condition.getQueryWrapper(niEmployeeResumeInfo));
		return R.data(EmployeeResumeInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 简历表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "分页", notes = "传入niEmployeeResumeInfo")
	public R<IPage<EmployeeResumeInfoVO>> page(EmployeeResumeInfoVO niEmployeeResumeInfo, Query query) {

		IPage<EmployeeResumeInfoVO> pages = employeeResumeInfoService.selectEmployeeResumeInfoPage(Condition.getPage(query),niEmployeeResumeInfo);
		return R.data(pages);
	}

	/**
	 * 简历表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(employeeResumeInfoService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 办理入职
	 * @return
	 */
	@ApiOperation("办理入职")
	@PostMapping("onboarding")
	public R onboarding(Long id,String phone){
		return R.success(employeeResumeInfoService.onboarding(id,phone));
	}

}
