package com.natergy.ni.sd.util;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSpacing;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.core.io.Resource;

import java.io.*;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Component
public class ReportGenerator {
	private static ResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();

	public ReportGenerator(ResourcePatternResolver resourceResolver) {
		this.resourceResolver = resourceResolver;
	}

	public static ByteArrayInputStream generateMain(Map<String, Object> inputJson) {
//        try (XWPFDocument doc = new XWPFDocument(new FileInputStream("src/main/resources/templates/word/ni/industrial_quality_feedback_report.docx"))) {
//		try (InputStream is = ReportGenerator.class.getResourceAsStream("/templates/word/ni/industrial_quality_feedback_report.docx");
		try  {
			Resource[] resources = resourceResolver.getResources("classpath*:/templates/word/ni/industrial_quality_feedback_report.docx");
			if (resources == null || resources.length == 0) {
				Assert.isTrue(false, "/templates/word/ni1/industrial_quality_feedback_report.docx 路径错误");
			}
			if (!resources[0].exists()) {
				Assert.isTrue(false, "industrial_quality_feedback_report.docx 模版文件不存在。");
			}

			InputStream is = resources[0].getInputStream();
			XWPFDocument doc = new XWPFDocument(is);
			List<XWPFTable> tables = doc.getTables();
            if (tables.size() > 0) {
                XWPFTable table = tables.get(0);
                updateTableCells(table, inputJson);
            }
            generateReply(inputJson, doc);
//            String outputName = inputJson.get("报告编号") + "_" + UUID.randomUUID() + ".docx";
//            try (FileOutputStream out = new FileOutputStream("./outputs/" + outputName)) {
//                doc.write(out);
//            }

			// 将文档内容写入 ByteArrayOutputStream
			ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
			doc.write(byteArrayOutputStream);

			// 将 ByteArrayOutputStream 转换为 ByteArrayInputStream
			return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

        } catch (IOException e) {
            e.printStackTrace();
			Assert.isTrue(false, "报告生成失败。" + e.getMessage());
        }
		return null;
	}

    private static void updateTableCells(XWPFTable table, Map<String, Object> inputJson) {
        table.getRow(0).getCell(3).setText(inputJson.get("报告编号").toString());
        table.getRow(1).getCell(3).setText(inputJson.get("报告日期").toString());
        table.getRow(2).getCell(1).setText(inputJson.get("客户名称").toString());

        XWPFTableCell cell = table.getRow(3).getCell(1);
        cell.setText("小组负责人：杨枕戈/总经理  郭传旭/生产\n小组成员：1.李春海/技术部 2.郭传旭/生产部 3.张雪/质量服务部 4.尹龙、" + inputJson.get("业务经理") + "/销售部");
//        String text = cell.getText();
//        text = text.replace("{{业务经理}}", inputJson.get("业务经理").toString());
//        cell.removeParagraph(0);// 移除原有的段落
//        cell.setText(text);
        table.getRow(5).getCell(1).setText(inputJson.get("问题描述").toString());

        int addNums = 0;
        int indexBase = 7 + addNums;
        List<String> tempMeasures = (List<String>) inputJson.get("临时措施");
        for (int i = 0; i < tempMeasures.size(); i++) {
            if (i > 3) {
                addRowOne(table, indexBase, i);
                addNums++;
                table.getRow(indexBase + i).getCell(3).setText(tempMeasures.get(i));
            }else{
                table.getRow(indexBase + i).getCell(2).setText(tempMeasures.get(i));
            }
//            table.getRow(indexBase + i).getCell(3).setText(tempMeasures.get(i));
        }

        indexBase = 12 + addNums;
        List<String> onSiteVerifications = (List<String>) inputJson.get("现场验证");
        for (int i = 0; i < onSiteVerifications.size(); i++) {
            if (i > 4) {
                addRowTwo(table, indexBase, i);
                addNums++;
            }
            table.getRow(indexBase + i).getCell(3).setText(onSiteVerifications.get(i));
        }

        indexBase = 17 + addNums;
        List<String> causeDeterminations = (List<String>) inputJson.get("原因判定");
        for (int i = 0; i < causeDeterminations.size(); i++) {
            if (i > 2) {
                addRowTwo(table, indexBase, i);
                addNums++;
            }
            table.getRow(indexBase + i).getCell(3).setText(causeDeterminations.get(i));
        }

        indexBase = 21 + addNums;
        List<String> permanentCorrectiveMeasures = (List<String>) inputJson.get("永久性纠正措施");
        for (int i = 0; i < permanentCorrectiveMeasures.size(); i++) {
            if (i > 3) {
                addRowOne(table, indexBase, i);
                addNums++;
                table.getRow(indexBase + i).getCell(3).setText(permanentCorrectiveMeasures.get(i));
            }else{
                table.getRow(indexBase + i).getCell(2).setText(permanentCorrectiveMeasures.get(i));
            }
//            }
//            table.getRow(indexBase + i).getCell(3).setText(permanentCorrectiveMeasures.get(i));
        }

        indexBase = 26 + addNums;
        List<String> correctiveMeasureVerifications = (List<String>) inputJson.get("纠正措施验证");
        for (int i = 0; i < correctiveMeasureVerifications.size(); i++) {
            if (i > 2) {
                addRowOne(table, indexBase, i);
                addNums++;
                table.getRow(indexBase + i).getCell(3).setText(correctiveMeasureVerifications.get(i));
            }else{
                table.getRow(indexBase + i).getCell(2).setText(correctiveMeasureVerifications.get(i));
            }
//            }
//            table.getRow(indexBase + i).getCell(3).setText(correctiveMeasureVerifications.get(i));
        }

        indexBase = 30 + addNums;
        List<String> technicalMeasures = (List<String>) inputJson.get("技术措施");
        for (int i = 0; i < technicalMeasures.size(); i++) {
            if (i > 2) {
                addRowOne(table, indexBase, i);
                addNums++;
                table.getRow(indexBase + i).getCell(3).setText(technicalMeasures.get(i));
            }else{
                table.getRow(indexBase + i).getCell(2).setText(technicalMeasures.get(i));
            }
//            }
//            table.getRow(indexBase + i).getCell(3).setText(technicalMeasures.get(i));
        }
    }

    private static void addRowOne(XWPFTable table, int indexBase, int i) {
        XWPFTableRow newRow = table.insertNewTableRow(indexBase + i);
//        for (int j = 0; j < table.getRow(indexBase + i - 1).getTableCells().size(); j++) {
//            newRow.createCell();
//        }
        for (int j = 0; j < 12; j++) {
            newRow.createCell();
        }
        mergeCellsHorizontal(newRow, 1, 2);
        mergeCellsHorizontal(newRow, 3, 7);
        mergeCellsHorizontal(newRow, 9, 10);
        mergeCellsVertical(table, indexBase + i - 1, 0, indexBase + i, 0);
        newRow.getCell(1).setText(String.valueOf(i + 1));
        setCellStyle(newRow.getCell(1));
//        newRow.getCell(2).setText(String.valueOf(i + 1));
//        setCellStyle(newRow.getCell(2));
    }

    private static void addRowTwo(XWPFTable table, int indexBase, int i) {
        XWPFTableRow newRow = table.insertNewTableRow(indexBase + i);
//        for (int j = 0; j < table.getRow(indexBase + i - 1).getTableCells().size(); j++) {
//            newRow.createCell();
//        }
        for (int j = 0; j < 12; j++) {
            newRow.createCell();
        }

        mergeCellsHorizontal(newRow, 3, 7);
        mergeCellsHorizontal(newRow, 9, 10);
        mergeCellsVertical(table, indexBase + i - 1, 0, indexBase + i, 0);
        mergeCellsVertical(table, indexBase + i - 1, 1, indexBase + i, 1);
        newRow.getCell(2).setText(String.valueOf(i + 1));
        setCellStyle(newRow.getCell(2));
    }

    private static void mergeCellsHorizontal(XWPFTableRow row, int startCol, int endCol) {
        row.getCell(startCol).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
        for (int i = startCol; i < endCol; i++) {
            row.getCell(i + 1).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
        }
    }

    private static void mergeCellsVertical(XWPFTable table, int startRow, int startCol, int endRow, int endCol) {
//        table.getRow(startRow).getCell(startCol).getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
        for (int i = startRow + 1; i <= endRow; i++) {
            table.getRow(i).getCell(endCol).getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
        }
    }

    private static void setCellStyle(XWPFTableCell cell) {
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            for (XWPFRun run : paragraph.getRuns()) {
                run.setFontFamily("宋体");
                run.setFontSize(10);
            }
        }
    }

    private static void generateReply(Map<String, Object> inputJson, XWPFDocument doc) {
        doc.createParagraph().createRun().addBreak(BreakType.PAGE);

        XWPFParagraph titleParagraph = doc.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("回复函");
        titleRun.setFontFamily("宋体");
        titleRun.setFontSize(16);
        titleRun.setBold(true);

        XWPFParagraph recipientParagraph = doc.createParagraph();
        XWPFRun recipientRun = recipientParagraph.createRun();
        recipientRun.setText("至" + inputJson.get("客户名称") + ":");
        recipientRun.setFontFamily("宋体");
        recipientRun.setFontSize(14);
        recipientRun.setBold(true);

        addTextParagraph(doc, "感谢贵司一直以来对我司工作的支持和认可！贵司关于我司“" + inputJson.get("问题描述") + "”,我司已收悉。现回复如下：");
        addTextParagraph(doc, "自我司接到调查结果后，马上成立了改善小组，查明问题根本原因系：通过我司技术人员现场验证查看结果为：");

        List<String> onSiteVerifications = (List<String>) inputJson.get("现场验证");
        for (int i = 0; i < onSiteVerifications.size(); i++) {
            String text = (i + 1) + "、" + onSiteVerifications.get(i);
            if (i < onSiteVerifications.size() - 1) {
                text += "；";
            } else {
                text += "。";
            }
            addTextParagraph(doc, text);
        }

        addTextParagraph(doc, "判定根本原因为：");

        List<String> causeDeterminations = (List<String>) inputJson.get("原因判定");
        for (int i = 0; i < causeDeterminations.size(); i++) {
            String text = (i + 1) + "、" + causeDeterminations.get(i);
            if (i < causeDeterminations.size() - 1) {
                text += "；";
            } else {
                text += "。";
            }
            addTextParagraph(doc, text);
        }

        addTextParagraph(doc, "查明原因后，我司立即发布了纠正措施，并安排小组人员落实验证。纠正措施如下：");

        List<String> permanentCorrectiveMeasures = (List<String>) inputJson.get("永久性纠正措施");
        for (int i = 0; i < permanentCorrectiveMeasures.size(); i++) {
            String text = (i + 1) + "、" + permanentCorrectiveMeasures.get(i);
            if (i < permanentCorrectiveMeasures.size() - 1) {
                text += "；";
            } else {
                text += "。";
            }
            addTextParagraph(doc, text);
        }

        addTextParagraph(doc, "通过以上整改措施，我司将加强质量管控，杜绝质量问题再次发生；贵司可将问题货物弃用，我司将冲减账目或补发相应货物。");
		addTextParagraph(doc, "给贵司带来不便敬请谅解！", 240*2*2);//48
		addTextParagraph(doc, "顺颂商祺！", 240*2*2);

        XWPFParagraph signatureParagraph = doc.createParagraph();
        XWPFRun signatureRun = signatureParagraph.createRun();
        signatureRun.setText("山东能特异能源科技有限公司");
        signatureRun.setFontFamily("宋体");
        signatureRun.setFontSize(12);
//        signatureParagraph.setIndentationFirstLine(360);
		// 设置段落右对齐
		signatureParagraph.setAlignment(ParagraphAlignment.RIGHT);
        setLineSpacing(signatureParagraph);

        XWPFParagraph dateParagraph = doc.createParagraph();
        XWPFRun dateRun = dateParagraph.createRun();
        dateRun.setText(inputJson.get("报告日期").toString());
        dateRun.setFontFamily("宋体");
        dateRun.setFontSize(12);
//        dateParagraph.setIndentationFirstLine(415);
		// 设置段落右对齐
		dateParagraph.setAlignment(ParagraphAlignment.RIGHT);
        setLineSpacing(dateParagraph);
    }

    private static void addTextParagraph(XWPFDocument doc, String text) {
        addTextParagraph(doc, text, 240*2);//24
    }

    private static void addTextParagraph(XWPFDocument doc, String text, int indent) {
        XWPFParagraph paragraph = doc.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setFontFamily("宋体");
        run.setFontSize(12);
        paragraph.setIndentationFirstLine(indent);
        setLineSpacing(paragraph);
    }

    private static void setLineSpacing(XWPFParagraph paragraph) {
        CTPPr pPr = paragraph.getCTP().isSetPPr() ? paragraph.getCTP().getPPr() : paragraph.getCTP().addNewPPr();
        CTSpacing spacing = pPr.isSetSpacing() ? pPr.getSpacing() : pPr.addNewSpacing();
        spacing.setLine(BigInteger.valueOf(480));
        spacing.setLineRule(STLineSpacingRule.EXACT);
    }
}
