package com.natergy.ni.thirlpartycall.controller;

import com.natergy.ni.thirlpartycall.params.MessageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.api.crypto.annotation.decrypt.ApiDecryptAes;
import org.springblade.core.api.crypto.annotation.encrypt.ApiEncryptAes;
import org.springblade.core.tool.api.R;
import org.springblade.modules.desk.service.IUserNoticeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 推送消息 控制器
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("message")
@Api(value = "推送消息", tags = "推送消息")
@Validated
public class PushMessageController {


	/**
	 * 消息通知service
	 */
	private final IUserNoticeService userNoticeService;

	@ApiDecryptAes
	@ApiEncryptAes
	@PostMapping("/pushMessage")
	@ApiOperation(value = "发送消息", notes = "发送消息")
	public R<String> pushNotice(@Valid @RequestBody MessageParams messageParams) {
		userNoticeService.pushNotice(messageParams.getType(), messageParams.getTitle(), messageParams.getContent(), messageParams.getReceiveUsers());
		return R.success("推送成功");
	}


}
