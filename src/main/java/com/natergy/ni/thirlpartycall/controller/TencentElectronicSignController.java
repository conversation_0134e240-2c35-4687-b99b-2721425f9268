//package com.natergy.ni.thirlpartycall.controller;
//
//import com.natergy.ni.emhire.enums.ElectronicSignatureEnums;
//import com.natergy.ni.emhire.service.IEmployeeElectronicSignatureService;
//import com.natergy.ni.emhire.utils.TencentElectronicSignatureHelper;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springblade.core.tool.jackson.JsonUtil;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Objects;
//
///**
// * 腾讯电子签回调
// */
//@RestController
//@Slf4j
//public class TencentElectronicSignController {
//
//	@Autowired
//	private TencentElectronicSignatureHelper helper;
//	@Autowired
//	private IEmployeeElectronicSignatureService electronicSignatureService;
//	/**
//	 * 腾讯电子签回调
//	 * @param body
//	 * @return
//	 */
//	@PostMapping("open/ni/tencent/e-sign/callback")
//	public String callBack(@RequestBody String body) {
//		log.info("腾讯电子签 回调 原始报文:{}",body);
//		try {
//
////			String original = body.contains("encrypt") ? JsonUtil.toMap(body).get("encrypt").toString() : body;
//
////			if (StringUtils.isBlank(original)){
////				log.warn("腾讯电子签 无载体 可能判定为 腾讯控制台修改回调地址时 测试回调");
////				return "success";
////			}
//			if (body.contains("encrypt")){
//				Map<String,Object> map = JsonUtil.toMap(body);
//				log.info("腾讯电子签 转换成 map :{}",map);
//
//				String  original =map.getOrDefault("encrypt","").toString();
//				log.info("腾讯电子签  encrypt 加密报文 :{}",original);
//				Map<String,Object> data = JsonUtil.toMap(new String(helper.aesDecrypt(original.getBytes())));
//				log.info("腾讯电子签 解密后报文 :{}",data);
//				log.info("腾讯电子签 获取操作类型:{}",data.getOrDefault("MsgType",""));
//				// 消息回调格式 V2 标准结构体
//				if (Objects.equals(data.getOrDefault("MsgType","").toString(),TencentElectronicSignatureHelper.MsgType.FlowStatusChange.name())) {
//					log.info("腾讯电子签 合同状态变更回调 :{}",JsonUtil.toJson(data));
//					// TODO 执行逻辑
//					electronicSignatureService.finishSign(data);
//				}
//				if (Objects.equals(data.getOrDefault("MsgType","").toString(),TencentElectronicSignatureHelper.MsgType.DocumentFill.name())) {
//					log.info("腾讯电子签 文档信息填充回调 :{}",JsonUtil.toJson(data));
//					// TODO 执行逻辑
//					electronicSignatureService.fillDocumentAfter(data);
//				}
//			}else{
//				byte[] data = helper.aesDecrypt(body.getBytes());
//				String str = new String(data);
//				Map<String, Object> map = JsonUtil.toMap(str);
//				String state = map.getOrDefault("FlowCallbackStatus", "99").toString();
//				if (ElectronicSignatureEnums.Status.findByCode(Integer.valueOf(state)).isFinish) {
//					Map<String,Object> wrapper = new HashMap<>();
//					wrapper.put("MsgData",map);
//					log.info("腾讯电子签 合同状态变更回调 V1 版本 :{}",JsonUtil.toJson(wrapper));
//					electronicSignatureService.finishSign(wrapper);
//				}
//			}
////			log.info("腾讯电子签 回调 解析后报文:{}",data);
//			// 兼容回调格式 V1
//			// TODO 腾讯电子签 其他回调事件分发
//		} catch (Exception e) {
//			log.error("腾讯电子签回调处理异常", e);
//			throw new RuntimeException(e);
//		}
//		return "success";
//	}
//
//
//}
