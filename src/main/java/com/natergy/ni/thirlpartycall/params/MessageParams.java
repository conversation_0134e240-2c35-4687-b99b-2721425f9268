package com.natergy.ni.thirlpartycall.params;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;


/**
 * 消息推送参数
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
public class MessageParams {

	@NotEmpty(message = "消息类型为必填项，有notice、birthdayNotice、contractNotice三种类型，普通消息使用notice类型")
	@ApiModelProperty(value = "消息类型")
	private String type;

	@NotEmpty(message = "消息标题为必填项")
	@ApiModelProperty(value = "消息标题")
	private String title;

	@NotEmpty(message = "消息内容为必填项")
	@ApiModelProperty(value = "消息内容")
	private String content;

	@NotNull(message = "推送员工列表集合为必填项")
	@Size(min = 1, max = 1000, message = "员工数量至少一个，最多1000个")
	@ApiModelProperty(value = "推送员工列表集合")
	private List<Long> receiveUsers;
}
