/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.ehs.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.ehs.dto.EhsInformationFunctionDTO;
import com.natergy.ni.ehs.excel.EhsExtinguisherExcel;
import com.natergy.ni.ehs.excel.EhsExtinguisherImporter;
import com.natergy.ni.ehs.excel.EhsInformationExcel;
import com.natergy.ni.ehs.excel.EhsInformationImporter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springblade.common.log.LogOptRecord;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.ehs.entity.EhsInformationFunctionEntity;
import com.natergy.ni.ehs.vo.EhsInformationFunctionVO;
import com.natergy.ni.ehs.wrapper.EhsInformationFunctionWrapper;
import com.natergy.ni.ehs.service.IEhsInformationFunctionService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 特种设备信息功能 控制器
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@RestController
@AllArgsConstructor
@RequestMapping("ni/ehs/ehsInformationFunction")
@Api(value = "特种设备信息功能", tags = "特种设备信息功能接口")
public class EhsInformationFunctionController extends BladeController {

	private final IEhsInformationFunctionService ehsInformationFunctionService;
	public static final String MODULE = "ni_ehs_information_function";
	/**
	 * 特种设备信息功能 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入ehsInformationFunction")
	public R<EhsInformationFunctionVO> detail(EhsInformationFunctionEntity ehsInformationFunction) {
		EhsInformationFunctionEntity detail = ehsInformationFunctionService.getOne(Condition.getQueryWrapper(ehsInformationFunction));
		return R.data(EhsInformationFunctionWrapper.build().entityVO(detail));
	}
	/**
	 * 特种设备信息功能 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入ehsInformationFunction")
	public R<IPage<EhsInformationFunctionVO>> list(EhsInformationFunctionEntity ehsInformationFunction, Query query) {
		IPage<EhsInformationFunctionEntity> pages = ehsInformationFunctionService.page(Condition.getPage(query), Condition.getQueryWrapper(ehsInformationFunction));
		return R.data(EhsInformationFunctionWrapper.build().pageVO(pages));
	}

	/**
	 * 特种设备信息功能 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入ehsInformationFunction")
	public R<IPage<EhsInformationFunctionVO>> page(EhsInformationFunctionVO ehsInformationFunction, Query query) {
		IPage<EhsInformationFunctionVO> pages = ehsInformationFunctionService.selectEhsInformationFunctionPage(Condition.getPage(query), ehsInformationFunction);
		return R.data(pages);
	}

	/**
	 * 特种设备信息功能 新增
	 */
	/**
	 * 特种设备信息功能 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入ehsInformationFunction")
	@LogOptRecord(module = MODULE, businessId = "{#ehsInformationFunction.id}", context = "新增特种设备信息:{#ehsInformationFunction}")
	public R save(@Valid @RequestBody EhsInformationFunctionEntity ehsInformationFunction) {

		// 查询数据库中有效的通知人信息
		LambdaQueryWrapper<EhsInformationFunctionEntity> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.select(EhsInformationFunctionEntity::getNotifyUser, EhsInformationFunctionEntity::getNotifyUserName)
			.isNotNull(EhsInformationFunctionEntity::getNotifyUser)
			.ne(EhsInformationFunctionEntity::getNotifyUser, "")
			.isNotNull(EhsInformationFunctionEntity::getNotifyUserName)
			.ne(EhsInformationFunctionEntity::getNotifyUserName, "");

		List<EhsInformationFunctionEntity> notifyUsers = ehsInformationFunctionService.list(queryWrapper);

		if (!notifyUsers.isEmpty()) {
			String defaultNotifyUser = notifyUsers.get(0).getNotifyUser();
			String defaultNotifyUserName = notifyUsers.get(0).getNotifyUserName();

			// 只有当字段为空时才自动填充
			if (defaultNotifyUser != null && ehsInformationFunction.getNotifyUser() == null) {
				ehsInformationFunction.setNotifyUser(defaultNotifyUser);
			}

			if (defaultNotifyUserName != null && ehsInformationFunction.getNotifyUserName() == null) {
				ehsInformationFunction.setNotifyUserName(defaultNotifyUserName);
			}
		} else {
			return R.fail("数据库中没有可用的通知人，请手动选择通知人");
		}

		boolean success = ehsInformationFunctionService.save(ehsInformationFunction);
		if (!success) {
			return R.fail("新增失败，请检查数据后重试");
		}

		return R.success("新增成功");
	}


	/**
	 * 特种设备信息功能 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入ehsInformationFunction")
	@LogOptRecord(module =  MODULE, businessId = "{#ehsInformationFunction.id}", context = "修改特种设备信息:{#ehsInformationFunction}")
	public R update(@Valid @RequestBody EhsInformationFunctionEntity ehsInformationFunction) {
		return R.status(ehsInformationFunctionService.updateById(ehsInformationFunction));
	}

	/**
	 * 特种设备信息功能 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入ehsInformationFunction")
	@LogOptRecord(module =  MODULE, businessId = "{#ehsInformationFunction.id}", context = "修改特种设备信息:{#ehsInformationFunction}")
	public R submit(@Valid @RequestBody EhsInformationFunctionEntity ehsInformationFunction) {

		return R.status(ehsInformationFunctionService.saveOrUpdate(ehsInformationFunction));
	}

	/**
	 * 特种设备信息功能 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@LogOptRecord(module =  MODULE, businessId = "{#ids}", context = "逻辑删除特种设备信息")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(ehsInformationFunctionService.deleteLogic(Func.toLongList(ids)));
	}


	@PostMapping("/batchUpdateNotifyUser")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "选择通知人", notes = "传入ids和notifyUser信息")
	@LogOptRecord(module = MODULE, businessId = "{#dto.ids}", context = "批量更新特种设备通知人")
	public R batchUpdateNotifyUser(@Valid @RequestBody @ApiParam("批量更新通知人参数") EhsInformationFunctionDTO dto) {
		boolean result = ehsInformationFunctionService.batchUpdateNotifyUser(dto.getIds(), dto.getNotifyUser(), dto.getNotifyUserName());
		return R.status(result);
	}


	/**
	 * 安环管理-灭火器管理 导入
	 */
	@PostMapping("import")
	@ApiOperation(value = "导入数据" , notes = "传入excel")
	public R importEhsInformationExcel(MultipartFile file){
		EhsInformationImporter informationImporter = new EhsInformationImporter(ehsInformationFunctionService);
		ExcelUtil.save(file,informationImporter,EhsInformationExcel.class);
		return R.success("操作成功");
	}


	/**
	 *
	 * @param response
	 * 导出模板
	 */
	@GetMapping("/export-template")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导入模板")
	public void exportTem(HttpServletResponse response){
		List<EhsInformationExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "特种设备信息导入模板", "特种设备信息数据", list, EhsInformationExcel.class);
	}
}
