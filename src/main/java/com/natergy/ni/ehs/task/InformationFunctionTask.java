package com.natergy.ni.ehs.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.natergy.ni.ehs.entity.EhsInformationFunctionEntity;
import com.natergy.ni.ehs.service.IEhsInformationFunctionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.ParamCache;
import org.springblade.modules.desk.service.IUserNoticeService;
import org.springblade.modules.system.entity.Role;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-05-24
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class InformationFunctionTask {


	private final IEhsInformationFunctionService ehsInformationFunctionService;

	// 消息通知 service
	private final IUserNoticeService userNoticeService;

	// 角色 service
	private final IRoleService roleService;

	// 用户 service
	private final IUserService userService;


	public static final String EQUIPMENT_INSPECTION_REMINDER = "equipment.inspection.reminder";//检查提醒

	public static  final String EQUIPMENT_SCRAPPING_REMINDER = "equipment.scrapping.reminder";//报废提醒




	/**
	 * 发送检验提醒（提前X天 + 到期当日）
	 */
	@Scheduled(cron = "0 41 9 * * ?")
	public void sendVerificationReminders() {
		LocalDate today = LocalDate.now();

		// 获取参数管理中的提前天数（例如："30,15,7"）
		String depotCode = ParamCache.getValue(EQUIPMENT_INSPECTION_REMINDER);

		// 将字符串转为整型集合
		List<Integer> remindDays = Arrays.stream(depotCode.split(","))
			.map(String::trim)
			.map(Integer::parseInt)
			.collect(Collectors.toList());

		// 获取所有未删除且有下次检验日期的设备
		List<EhsInformationFunctionEntity> devices =ehsInformationFunctionService.list(
			new LambdaQueryWrapper<EhsInformationFunctionEntity>()
				.eq(EhsInformationFunctionEntity::getIsDeleted, false)
				.isNotNull(EhsInformationFunctionEntity::getInspectionDate)
				.isNotNull(EhsInformationFunctionEntity::getNotifyUser)
		);

		if (devices == null || devices.isEmpty()) {
			log.warn("未找到符合条件的设备");
			return;
		}


		StringBuilder content = new StringBuilder();

		for (EhsInformationFunctionEntity device : devices) {
			// 转换 java.util.Date -> java.time.LocalDate
			LocalDate inspectionDate = device.getInspectionDate().toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();

			long daysUntil = ChronoUnit.DAYS.between(today, inspectionDate);

			// 到期当日提醒
			if (daysUntil == 0 && device.getStatus()==1) {
				content.append(String.format("您的设备【%s】今日需进行检验，请及时处理！", device.getDeviceName())).append("<br>");
			}
			// 提前N天提醒（支持多个天数，如 30/15/7）
			else if (remindDays.contains((int) daysUntil) && device.getStatus()==1) {
				content.append(String.format("您的设备【%s】将在%s天后需要检验，请注意准备！", device.getDeviceName(), daysUntil)).append("<br>");
				if(device.getStatus()==1){
					device.setStatus(2);
				}
			}
			// 超期提醒
			else if (daysUntil < 0 && device.getStatus()==1) {
				content.append(String.format("您的设备【%s】已逾期未检验，请尽快完成！", device.getDeviceName())).append("<br>");
				if(device.getStatus()==2||device.getStatus()==1){
					device.setStatus(3);
				}
			}
			if (content.length() > 0) {
				List<Long> notifyUsers = Arrays.stream(device.getNotifyUser().split(","))
					.map(String::trim)
					.map(Long::valueOf) // 或者 parseLong
					.collect(Collectors.toList());
				for (Long notifyUser : notifyUsers) {
					userNoticeService.pushNotice("notice", "设备检验提醒", content.toString(), Collections.singletonList(notifyUser));
				}
				content.setLength(0);
			}
			if (device.getStatus() == 2) {
				ehsInformationFunctionService.updateById(device); // 单条更新
			}
			if (device.getStatus() == 3) {
				ehsInformationFunctionService.updateById(device); // 单条更新
			}
		}

//		userNoticeService.pushNotice("notice", "设备检验提醒","检验测试2", receiveUsers);
	}


	/**
	 * 发送报废提醒（提前Y天 + 到期当日）
	 */
	@Scheduled(cron = "30 41 9 * * ?")
	public void sendScrapReminders() {
		LocalDate today = LocalDate.now();

		// 获取参数管理中的提前天数（例如："90,60,30"）
		String scrapReminderDaysStr = ParamCache.getValue(EQUIPMENT_SCRAPPING_REMINDER);

		// 将字符串转为整型集合
		List<Integer> remindDays = Arrays.stream(scrapReminderDaysStr.split(","))
			.map(String::trim)
			.map(Integer::parseInt)
			.collect(Collectors.toList());

		// 获取所有未删除且有预期报废日期的设备

		List<EhsInformationFunctionEntity> devices = ehsInformationFunctionService.list(
			new LambdaQueryWrapper<EhsInformationFunctionEntity>()
				.eq(EhsInformationFunctionEntity::getIsDeleted, false)
				.isNotNull(EhsInformationFunctionEntity::getScrapDate)
				.isNotNull(EhsInformationFunctionEntity::getNotifyUser)
		);

		if (devices == null || devices.isEmpty()) {
			log.warn("未找到符合条件的设备");
			return;
		}

//		Role roleData = roleService.getOne(new LambdaQueryWrapper<Role>().eq(Role::getRoleName, "超级管理员"));
//		List<Long> receiveUsers = userService.getUserIdByRoleId(roleData.getId());

		StringBuilder content = new StringBuilder();

		for (EhsInformationFunctionEntity device : devices) {
			// 转换 java.util.Date -> java.time.LocalDate
			LocalDate expectedScrapDate = device.getScrapDate().toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();

			long daysUntil = ChronoUnit.DAYS.between(today, expectedScrapDate);

			// 到期当日提醒
			if (daysUntil == 0 && device.getStatus()==1) {
				content.append(String.format("您的设备【%s】今日正式报废，请及时处理！", device.getDeviceName())).append("<br>");
				device.setStatus(2); // 标记已报废
			}
			// 提前N天提醒（支持多个天数，如 90/60/30）
			else if (remindDays.contains((int) daysUntil)&& device.getStatus()==1) {
				content.append(String.format("您的设备【%s】将在%s天后报废，请注意准备！", device.getDeviceName(), daysUntil)).append("<br>");
			}
			// 超期提醒
			else if (daysUntil < 0) {
				content.append(String.format("您的设备【%s】已逾期未报废，请尽快完成处理！", device.getDeviceName())).append("<br>");

			}
			if (content.length() > 0) {
				List<Long> notifyUsers = Arrays.stream(device.getNotifyUser().split(","))
					.map(String::trim)
					.map(Long::valueOf) // 或者 parseLong
					.collect(Collectors.toList());
				for (Long notifyUser : notifyUsers){
					userNoticeService.pushNotice("notice", "设备报废提醒", content.toString(), Collections.singletonList(notifyUser));
				}
				content.setLength(0);
			}

		}

//		userNoticeService.pushNotice("notice", "设备报废提醒", "报废测试1", receiveUsers);

	}

}
