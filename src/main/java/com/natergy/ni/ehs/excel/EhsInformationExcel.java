package com.natergy.ni.ehs.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class EhsInformationExcel implements Serializable {

	@ColumnWidth(20)
	@ExcelProperty("设备名称")
	private String deviceName;

	@ColumnWidth(20)
	@ExcelProperty("规格型号")
	private String specification;

	@ColumnWidth(20)
	@ExcelProperty("位号(内部编号)")
	private String positionNumber;

	@ColumnWidth(20)
	@ExcelProperty("使用证号")
	private String certificateNumber;

	@ColumnWidth(20)
	@ExcelProperty("注册代码")
	private String registrationCode;

	@ColumnWidth(20)
	@ExcelProperty("设计(工作)压力")
	private String designPressure;

	@ColumnWidth(20)
	@ExcelProperty("设计(工作)温度")
	private String designTemperature;

	@ColumnWidth(20)
	@ExcelProperty("介质")
	private String medium;

	@ColumnWidth(20)
	@ExcelProperty("主体材质")
	private String material;

	@ColumnWidth(20)
	@ExcelProperty("制造单位")
	private String manufactureCompany;

	@ColumnWidth(20)
	@ExcelProperty("出厂编号")
	private String startNumber;

	@ColumnWidth(20)
	@ExcelProperty("投用时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date usageTime;

	@ColumnWidth(20)
	@ExcelProperty("检验日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date inspectionDate;

	@ColumnWidth(20)
	@ExcelProperty("检验报告编号")
	private String inspectionReportNumber;

	@ColumnWidth(20)
	@ExcelProperty("安全状态等级")
	private String safetyStatusLevel;

	@ColumnWidth(20)
	@ExcelProperty("下次校验日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date nextVerificationDate;

	@ColumnWidth(20)
	@ExcelProperty("备注")
	private String note;

	@ColumnWidth(20)
	@ExcelProperty("出厂日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date appearanceDate;

	@ColumnWidth(20)
	@ExcelProperty("报废日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date scrapDate;

	@ColumnWidth(20)
	@ExcelProperty("公称压力")
	private String nominalPressure;

	@ColumnWidth(20)
	@ExcelProperty("安装位置")
	private String installationPosition;

	@ColumnWidth(20)
	@ExcelProperty("安装位置工作压力")
	private String installationPositionPressure;

	@ColumnWidth(20)
	@ExcelProperty("整定(爆破)压力")
	private String setPressure;

	@ColumnWidth(20)
	@ExcelProperty("校验单位")
	private String verificationUnit;

	@ColumnWidth(20)
	@ExcelProperty("使用状态")
	private Integer userStatus;

	@ColumnWidth(20)
	@ExcelProperty("特征参数")
	private String characteristicParameter;

	@ColumnWidth(20)
	@ExcelProperty("检验报告")
	private String inspectionReport;

	@ColumnWidth(20)
	@ExcelProperty("检验结论")
	private String inspectionConclusion;

	@ColumnWidth(20)
	@ExcelProperty("使用登记编号")
	private String useRegistrationNumber;

	@ColumnWidth(20)
	@ExcelProperty("数量")
	private Integer number;

	@ColumnWidth(20)
	@ExcelProperty("单位")
	private String unit;

	@ColumnWidth(20)
	@ExcelProperty("存放位置")
	private String position;

	@ColumnWidth(20)
	@ExcelProperty("公司")
	private String brand;

	@ColumnWidth(20)
	@ExcelProperty("设备种类")
	private String equipmentCategory;

}
