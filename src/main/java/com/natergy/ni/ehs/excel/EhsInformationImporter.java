package com.natergy.ni.ehs.excel;

import com.natergy.ni.ehs.service.IEhsExtinguisherService;
import com.natergy.ni.ehs.service.IEhsInformationFunctionService;
import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;

import java.util.List;

@RequiredArgsConstructor
public class EhsInformationImporter implements ExcelImporter<EhsInformationExcel> {
	private final IEhsInformationFunctionService informationFunctionService;

	@Override
	public void save(List<EhsInformationExcel> data) {
		informationFunctionService.importEhsInformationExcel(data);
	}
}
