package com.natergy.ni.open.service;

import com.natergy.ni.open.dto.*;

import java.util.List;

/**
 * 拆箱上位机同步服务
 */
public interface ISyncService {

    /**
     * 获取产品规格
     */
    List<ProductSpecificationDTO> getProductSpecification();

    /**
     * 获取产品内包装
     */
    List<ProductInnerPackagingDTO> getProductInnerPackaging();

    /**
     * 获取产品外包装
     */
    List<ProductOuterPackagingDTO> getProductOuterPackaging();

    /**
     * 获取产品品牌
     */
    List<ProductBrandDTO> getProductBrand();

    /**
     * 获取产品质量
     */
    List<ProductQualityDTO> getProductQuality();
}
