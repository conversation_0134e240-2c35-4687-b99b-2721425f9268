package com.natergy.ni.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.natergy.ni.base.entity.DemandEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.vo.DemandVO;

import java.util.List;

/**
 * 提工单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
public interface DemandMapper extends BaseMapper<DemandEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param Demand
	 * @return
	 */
	List<DemandVO> selectDemandPage(IPage page, DemandVO Demand);


}
