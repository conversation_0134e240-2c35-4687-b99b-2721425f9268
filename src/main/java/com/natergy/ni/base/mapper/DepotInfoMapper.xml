<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.DepotInfoMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="depotInfoResultMap" type="com.natergy.ni.base.entity.DepotInfo">
    <result column="id" property="id"/>
    <result column="create_user" property="createUser"/>
    <result column="create_dept" property="createDept"/>
    <result column="create_time" property="createTime"/>
    <result column="update_user" property="updateUser"/>
    <result column="update_time" property="updateTime"/>
    <result column="status" property="status"/>
    <result column="is_deleted" property="isDeleted"/>
    <result column="name" property="name"/>
    <result column="code" property="code"/>
    <result column="type" property="type"/>
    <result column="address" property="address"/>
    <result column="principal_id" property="principalId"/>
    <result column="remark" property="remark"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="distance" property="distance"/>
  </resultMap>


  <select id="selectListOrderByDistance" resultMap="depotInfoResultMap">
    select *,SQRT(
    POWER(SIN((#{depotInfo.latitude,jdbcType=DECIMAL} - ISNULL(Latitude,0)) * PI()/180 / 2), 2)
    + COS(#{depotInfo.latitude,jdbcType=DECIMAL} * PI()/180)
    * COS(Latitude * PI()/180)
    * POWER(SIN((#{depotInfo.longitude} - ISNULL(Longitude,0)) * PI()/180 / 2), 2)
    ) * 6371 AS distance from ni_base_depot where is_deleted = 0
    <if test="depotInfo.name != null and depotInfo.name != ''">
      <bind name="nameLike" value="'%' + depotInfo.name + '%'"/>
      and name like #{nameLike}
    </if>
    <if test="depotInfo.code != null and depotInfo.code != ''">
      <bind name="codeLike" value="'%' + depotInfo.code + '%'"/>
      and code like #{codeLike}
    </if>
    <if test="depotInfo.type != null and depotInfo.type != ''">
      and type = #{depotInfo.type}
    </if>
    order by distance
  </select>

  <select id="selectDepotLocationPage" resultType="com.natergy.ni.base.dto.DepotLocationDTO">
    select * from v_ni_depot_location
    <where>
      <if test="depotId != null">
        and depot_id = #{depotId}
      </if>
      <if test="depotLocation != null and depotLocation != ''">
        <bind name="depotLocationLike" value="'%' + depotLocation + '%'"/>
        and depot_location like #{depotLocationLike}
      </if>
    </where>
  </select>
</mapper>
