<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.SerialNoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="serialNoResultMap" type="com.natergy.ni.base.entity.SerialNo">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="prefix" property="prefix"/>
        <result column="date_format" property="dateFormat"/>
        <result column="suffix_length" property="suffixLength"/>
        <result column="initial_value" property="initialValue"/>
        <result column="connector" property="connector"/>
        <result column="reset_cycle" property="resetCycle"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectSerialNoPage" resultMap="serialNoResultMap">
        select * from ni_base_serial_no where is_deleted = 0
    </select>

</mapper>
