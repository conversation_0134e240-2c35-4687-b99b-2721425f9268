<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.ToolManagementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="niBaseToolResultMap" type="com.natergy.ni.base.vo.ToolManagementVO">
    </resultMap>

    <resultMap id="toolUserResultMap" type="org.springblade.modules.system.entity.User">
    </resultMap>

    <select id="selectBaseToolPage" resultMap="niBaseToolResultMap">
        select * from ni_base_tool nbt
        <where>
            and nbt.is_deleted = 0
            <if test="param2.row != null and param2.row!= ''">
                and nbt.row = #{param2.row}
            </if>

            <if test="param2.keeperDeptId != null and param2.keeperDeptId!= ''">
                and nbt.keeper_dept_id = #{param2.keeperDeptId}
            </if>

            <if test="param2.materialCode != null and param2.materialCode!= ''">
                <bind name="materialCodeLikeValue" value="'%' + param2.materialCode + '%'"/>
                and nbt.material_code like #{materialCodeLikeValue}
            </if>


            <if test="param2.materialName != null and param2.materialName!= ''">
                <bind name="materialNameLikeValue" value="'%' + param2.materialName + '%'"/>
                and nbt.material_name like #{materialNameLikeValue}
            </if>


            <if test="param2.specification != null and param2.specification!= ''">
                <bind name="specificationLikeValue" value="'%' + param2.specification + '%'"/>
                and nbt.specification like #{specificationLikeValue}
            </if>

            <if test="param2.num != null and param2.num!= ''">
                and nbt.num = #{param2.num}
            </if>

            <if test="param2.unit != null and param2.unit!= ''">
                and nbt.unit = #{param2.unit}
            </if>

            <if test="param2.submit != null">
                and nbt.submit = #{param2.submit}
            </if>

            <if test="param2.submitDate != null and param2.submitDate!= ''">
                and nbt.submit_date = #{param2.submitDate,jdbcType=TIMESTAMP}
            </if>

            <if test="param2.keeperId != null and param2.keeperId!= ''">
                and nbt.keeper_id = #{param2.keeperId}
            </if>

            <if test="param2.lendDate != null and param2.lendDate!= ''">
                and nbt.lend_date = #{param2.lendDate,jdbcType=TIMESTAMP}
            </if>

            <if test="param2.factory != null and param2.factory!= ''">
                and nbt.factory = #{param2.factory}
            </if>

            <if test="param2.itemStatus != null and param2.itemStatus!= ''">
                and nbt.item_status = #{param2.itemStatus}
            </if>

            <if test="param2.userStatus != null and param2.userStatus == 0">
                and nbt.keeper_id IN (
                SELECT bu.id
                FROM blade_user bu
                JOIN ni_base_tool bt ON nbt.keeper_id = bu.id
                WHERE
                (bu.is_deleted = 1 OR bu.status != 1)
                )
            </if>

            <if test="param2.userStatus != null and param2.userStatus == 1">
                and nbt.keeper_id NOT IN (
                SELECT bu.id
                FROM blade_user bu
                JOIN ni_base_tool bt ON bt.keeper_id = bu.id
                WHERE
                (bu.is_deleted = 1 OR bu.status != 1)
                )
            </if>

            <if test="param2.useObject != null and param2.useObject!= ''">
                and nbt.use_object = #{param2.useObject}
            </if>

        </where>
    </select>

    <select id="selectUserInfo" resultMap="toolUserResultMap">
        select * from blade_user bu
        where bu.id = #{userId}
    </select>


</mapper>
