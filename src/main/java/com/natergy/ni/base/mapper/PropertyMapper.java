/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.mapper;

import com.natergy.ni.base.entity.PropertyEntity;
import com.natergy.ni.base.vo.PropertyVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 动态属性 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
public interface PropertyMapper extends BaseMapper<PropertyEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param property
	 * @return
	 */
	List<PropertyVO> selectPropertyPage(IPage page, PropertyVO property);


}
