
package com.natergy.ni.base.mapper;

import com.natergy.ni.base.entity.ToolManagementEntity;
import com.natergy.ni.base.vo.ToolManagementVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.system.entity.User;

import java.util.List;

/**
 * 基本信息-工具管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public interface ToolManagementMapper extends BaseMapper<ToolManagementEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param niBaseTool
	 * @return
	 */
	List<ToolManagementVO> selectBaseToolPage(IPage page, ToolManagementVO niBaseTool);

	User selectUserInfo(Long userId);

}
