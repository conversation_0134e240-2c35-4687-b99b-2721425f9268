<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.ArticleSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="articleSummaryResultMap" type="com.natergy.ni.base.entity.ArticleSummaryEntity">
    </resultMap>


    <select id="selectArticleSummaryPage" resultMap="articleSummaryResultMap">
        SELECT *
        FROM article_summary a_s
--         WHERE
--             a_s.date IN (
--                CONVERT(VARCHAR(10), GETDATE(), 120),                   -- 今天
--                CONVERT(VARCHAR(10), DATEADD(DAY, -1, GETDATE()), 120)  -- 昨天
--             )
        ORDER BY a_s.date DESC, a_s.weight DESC;
    </select>

    <select id="selectArticleSummaryList" resultMap="articleSummaryResultMap">
        SELECT *
        FROM article_summary a_s
        where 1 = 1
        <if test="param2.title != null and param2.title != ''">
            <bind name="titleLikeValue" value="'%' + param2.title + '%'"/>
            and a_s.title LIKE #{ titleLikeValue }
        </if>
        <if test="param2.summary != null and param2.summary != ''">
            <bind name="summaryLikeValue" value="'%' + param2.summary + '%'"/>
            and a_s.summary LIKE #{ summaryLikeValue }
        </if>
        <if test="param2.author != null and param2.author != ''">
            <bind name="authorLikeValue" value="'%' + param2.author + '%'"/>
            and a_s.author LIKE #{ authorLikeValue }
        </if>
        <if test="param2.source != null and param2.source != ''">
            and a_s.source = #{ param2.source }
        </if>
        <if test="param2.startTime != null and param2.endTime != null">
            <![CDATA[
            AND a_s.date >= #{param2.startTime}
            AND a_s.date <= #{param2.endTime}
        ]]>
        </if>
        ORDER BY a_s.date DESC, a_s.weight DESC;

    </select>


</mapper>
