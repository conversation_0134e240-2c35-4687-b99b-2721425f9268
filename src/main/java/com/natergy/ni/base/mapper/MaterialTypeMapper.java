/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.entity.MaterialType;
import com.natergy.ni.base.vo.MaterialTypeVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface MaterialTypeMapper extends BaseMapper<MaterialType> {

  /**
   * 自定义分页
   *
   * @param page
   * @param materialType
   * @return
   */
  List<MaterialTypeVO> selectMaterialTypePage(IPage page, MaterialTypeVO materialType);

  /**
   * @param wrapper
   * @return
   */
  List<MaterialTypeVO> tree(@Param("ew") QueryWrapper<MaterialType> wrapper);

  List<MaterialType> getUnSyncList(List<String> sequenceList);

  void updateParentId();

  void updateAncestors();

  List<MaterialTypeVO> lazyList(@Param("parentId") Long parentId,
      @Param("materialType") MaterialTypeVO materialType);

  List<MaterialType> getMaterialTypeChild(@Param("typeId") Long typeId,
      @Param("withRoot") boolean withRoot);
}
