<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.PropertyExtendedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="propertyExtendedResultMap" type="com.natergy.ni.base.entity.PropertyExtendedEntity">
        <result column="property_id" property="propertyId"/>
        <result column="extended_property_name" property="extendedPropertyName"/>
        <result column="value_type" property="valueType"/>
        <result column="tip" property="tip"/>
        <result column="required" property="required"/>
        <result column="ordered" property="ordered"/>
        <result column="is_active" property="active"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectPropertyExtendedPage" resultMap="propertyExtendedResultMap">
        select * from ni_base_property_extended where is_deleted = 0
    </select>


</mapper>
