/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.entity.SupplierInfo;
import com.natergy.ni.base.vo.SupplierInfoVO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
public interface SupplierInfoMapper extends BaseMapper<SupplierInfo> {

  /**
   * 自定义分页
   *
   * @param page
   * @param supplierInfo
   * @return
   */
  List<SupplierInfoVO> selectSupplierInfoPage(IPage page, SupplierInfoVO supplierInfo);

  /**
   * 物流供应商列表
   *
   * @param query
   * @return
   */
  List<SupplierInfoVO> logisticsList(@Param("query") String query);

  BigDecimal bopTotal(@Param("startDate") LocalDate startDate);

  List<SupplierInfo> getUnSyncList(List<String> sequenceList);
}
