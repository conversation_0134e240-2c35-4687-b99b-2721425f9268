<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.ContractMapper">
  <resultMap id="BaseResultMap" type="com.natergy.ni.base.vo.ContractVO">
    <result column="a_pic" property="aaPic"/>
    <result column="b_pic" property="bbPic"/>
    <result column="order_amount" property="orderAmount"/>
    <result column="un_pay_amount" property="unPayAmount"/>
      <result column="has_children" property="hasChildren" />
      <result column="parent_serial_no" property="parentSerialNo"/>
  </resultMap>
  <select id="selectContractPage" resultMap="BaseResultMap">
      SELECT nbc.*,
      IIF(child_contracts.pid IS NOT NULL, 1, 0) AS has_children,
      IIF(nbc.type IN ('1,12', '1,13'), order_sum.amount, NULL) AS order_amount,
      IIF(nbc.type IN ('1,12', '1,13'), un_pay_sum.amount, NULL) AS un_pay_amount,-- 排除预付款/支付宝
      parent.serial_no AS parent_serial_no -- 主合同编号
      FROM ni_base_contract nbc
      LEFT JOIN (
      SELECT DISTINCT parent_id AS pid
      FROM ni_base_contract c
      WHERE c.type = '4' and c.is_deleted=0
      ) child_contracts ON nbc.id = child_contracts.pid
      LEFT JOIN ni_base_contract parent ON nbc.parent_id = parent.id and nbc.type='4' and
      parent.is_deleted=0 -- 关联主合同
      LEFT JOIN (SELECT npo.contract_id, SUM(npoi.amount) AS amount
      FROM ni_por_order npo
      LEFT JOIN ni_por_order_item npoi
      ON npo.id = npoi.order_id AND npoi.is_deleted = 0
      WHERE npo.is_deleted = 0
      AND npo.status = 2
      GROUP BY npo.contract_id) order_sum ON order_sum.contract_id = nbc.id
      LEFT JOIN (SELECT npo.contract_id, SUM(npoi.amount) AS amount
      FROM ni_por_order npo
      LEFT JOIN ni_por_order_item npoi
      ON npo.id = npoi.order_id AND npoi.is_deleted = 0
      WHERE npo.is_deleted = 0
      AND npo.status = 2
      AND IIF(ISNULL(npoi.pay_type, '') = '', npo.pay_type, npoi.pay_type) =
      '4' -- 排除预付款/支付宝
      GROUP BY npo.contract_id) un_pay_sum ON un_pay_sum.contract_id = nbc.id
      <where>
          and nbc.is_deleted = 0
          <if test="param2.parentId!=null">
              and nbc.parent_id = #{param2.parentId}
          </if>
          <if test="param2.brand != null and param2.brand != ''">
              AND nbc.brand IN
              <foreach collection="param2.brand.split(',')" item="item" index="index" separator=","
                  open="(" close=")">
                  #{item}
              </foreach>
          </if>
          <if test="param2.type != null and param2.type != ''">
              AND nbc.type = #{param2.type,jdbcType=VARCHAR}
          </if>
          <if test="param2.serialNo != null and param2.serialNo != ''">
              <bind name="serialNolikeValue" value="'%' + param2.serialNo + '%'"/>
              AND nbc.serial_no LIKE #{serialNolikeValue}
          </if>
          <if test="param2.name != null and param2.name != ''">
              <bind name="namelikevalue" value="'%' + param2.name + '%'"/>
              AND nbc.name LIKE #{namelikevalue}
          </if>
          <if test="param2.a != null">
              AND nbc.a = #{param2.a}
          </if>
          <if test="param2.b != null">
              AND nbc.b = #{param2.b}
          </if>
          <if test="param2.aaPic != null and param2.aaPic != ''">
              <bind name="aPiclikevalue" value="'%' + param2.aaPic + '%'"/>
              AND nbc.a_pic LIKE #{aPiclikevalue}
          </if>
          <if test="param2.bbPic != null and param2.bbPic != ''">
              <bind name="bPiclikevalue" value="'%' + param2.bbPic + '%'"/>
              AND nbc.b_pic LIKE #{bPiclikevalue}
          </if>
          <if test="param2.contractState != null and param2.contractState != ''">
              AND nbc.contract_state = #{param2.contractState}
          </if>
          <if test="param2.startContractDate != null">
              AND nbc.contract_date >= #{param2.startContractDate}
          </if>
          <if test="param2.endContractDate != null">
              <![CDATA[
        AND nbc.contract_date <= #{param2.endContractDate}
        ]]>
          </if>
          <if test="param2.deptId != null">
              AND nbc.dept_id = #{param2.deptId}
          </if>
          <if test="param2.payType != null and param2.payType != ''">
              AND nbc.pay_type = #{param2.payType}
          </if>
          <if test="param2.aaType != null and param2.aaType != ''">
              AND nbc.aa_type = #{param2.aaType}
          </if>
          <if test="param2.bbType != null and param2.bbType != ''">
              AND nbc.bb_type = #{param2.bbType}
          </if>
          <if test="param2.id != null">
              AND nbc.id = #{param2.id}
          </if>
          <if test="param2.createUser != null">
              AND nbc.create_user = #{param2.createUser}
          </if>
          <if test="param2.createDept != null">
              AND nbc.create_dept = #{param2.createDept}
          </if>
          <if test="param2.status != null">
              AND nbc.status = #{param2.status}
          </if>
          <if test="param2.keyword != null and param2.keyword != ''">
              <bind name="keywordlikevalue" value="'%' + param2.keyword + '%'"/>
              AND (nbc.name LIKE #{keywordlikevalue} OR nbc.serial_no LIKE #{keywordlikevalue}
              OR nbc.id = TRY_CAST(#{param2.keyword} AS BIGINT)
              )
          </if>
          <if test="param2.archiveSerialNo != null and param2.archiveSerialNo != ''">
              <bind name="archiveSerialNolikevalue" value="'%' + param2.archiveSerialNo + '%'"/>
              AND nbc.archive_serial_no LIKE #{archiveSerialNolikevalue}
          </if>
          <if test="param2.archive == 1">
              AND nbc.archive = #{param2.archive,jdbcType = BIT}
          </if>
          <if test="param2.archive == 0">
              AND nbc.archive = #{param2.archive,jdbcType = BIT}
          </if>
          <if test="param2.archive == 2">
              and nbc.archive is null
          </if>
          <if test="param2.payApplyState != null">
              AND nbc.pay_apply_state = #{param2.payApplyState}
          </if>
          <if test="param2.unStamp">
              AND (nbc.archive = 2 or nbc.archive is null)
          </if>
      </where>
      order by nbc.id desc
  </select>

  <select id="getLastNoByMonth" resultType="java.lang.String">
  </select>
</mapper>
