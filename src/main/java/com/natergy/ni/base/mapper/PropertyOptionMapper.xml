<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.PropertyOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="propertyOptionResultMap" type="com.natergy.ni.base.entity.PropertyOptionEntity">
        <result column="property_id" property="propertyId"/>
        <result column="option_name" property="optionName"/>
        <result column="ordered" property="ordered"/>
        <result column="is_active" property="active"/>
        <result column="remark" property="remark"/>
        <result column="id" property="id"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_user" property="createUser"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectPropertyOptionPage" resultMap="propertyOptionResultMap">
        select * from ni_base_property_option where is_deleted = 0
    </select>


</mapper>
