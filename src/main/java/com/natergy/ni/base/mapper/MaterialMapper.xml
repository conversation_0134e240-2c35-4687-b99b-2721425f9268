<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.MaterialMapper">
    <select id="selectMaterialInfoPage" resultType="com.natergy.ni.base.entity.Material">
      SELECT nbm.*
      FROM ni_base_material nbm
      WHERE nbm.is_deleted = 0
      <if test="material.keyword!=null and material.keyword!=''">
        <bind name="keywordLike" value="'%' + material.keyword + '%'"/>
          AND (nbm.name like #{keywordLike}
          or nbm.code like #{keywordLike}
          or nbm.manufacturer like #{keywordLike}
          or nbm.specification like #{keywordLike}
          or nbm.quality like #{keywordLike}
          or nbm.remark like #{keywordLike}
          or nbm.type_id in (select id from ni_base_material_type where is_deleted=0 and name like
          #{keywordLike} OR EXISTS
          (
          SELECT 1
          FROM STRING_SPLIT(ancestors, ',')
          WHERE value IN
          (
          SELECT id
          FROM dbo.ni_base_material_type
          WHERE is_deleted = 0
          AND name LIKE #{keywordLike}
          )
          )
          ))
      </if>
    </select>
    <select id="pageWithStock" resultType="com.natergy.ni.base.vo.MaterialWithStockVO">
        SELECT
        isnull(s1.num,0) as num,
        m1.*
        FROM
        ni_base_material m1
        LEFT JOIN
        (SELECT material_id, SUM(isnull(num,0)) AS num FROM ni_depot_material_stock WHERE
        is_deleted = 0
        GROUP BY material_id ) s1
        ON m1.id = s1.material_id
        <where>
            AND m1.is_deleted = 0
            <if test="material.name!=null and material.name!=''">
                and (m1.name like concat('%', #{material.name}, '%') or m1.code like
                concat('%', #{material.name}, '%')
                or m1.manufacturer like concat('%', #{material.name}, '%') or
                m1.specification
                like
                concat('%', #{material.name}, '%')
                or m1.quality like concat('%', #{material.name}, '%') or m1.color like
                concat('%', #{material.name}, '%')
                or m1.remark like concat('%', #{material.name}, '%'))
            </if>
            <if test="material.code!=null and material.code!=''">
                and (m1.code like concat('%', #{material.code}, '%')
            </if>
            <if test="material.typeId!=null">
                and m1.type_id in (select id from ni_base_material_type where is_deleted=0 and
                PATINDEX('%,'
                + RTRIM(#{material.typeId}) + ',%',',' + ancestors + ',')>0)
            </if>
            <if test="material.manufacturer!=null and material.manufacturer!=''">
                and m1.manufacturer like concat('%',#{material.manufacturer},'%')
            </if>
            <if test="material.specification!=null and material.specification!=''">
                and m1.specification like concat('%',#{material.specification},'%')
            </if>
            <if test="material.quality!=null and material.quality!=''">
                and m1.quality like concat('%',#{material.quality},'%')
            </if>
            <if test="material.unit!=null">
                and m1.unit = #{material.unit}
            </if>
            <if test="material.query!=null and material.query!=''">
                and (m1.name like concat('%', #{material.query}, '%') or m1.code like
                concat('%', #{material.query}, '%')
                or m1.manufacturer like concat('%', #{material.query}, '%') or
                m1.specification
                like
                concat('%', #{material.query}, '%')
                or m1.quality like concat('%', #{material.query}, '%') or m1.color like
                concat('%', #{material.query}, '%')
                or m1.remark like concat('%', #{material.query}, '%'))
            </if>
            <if test="material.sell!=null">
                and m1.sell = #{material.sell}
            </if>
            <if test="material.weight!=null">
                and m1.weight = #{material.weight}
            </if>
            <if test="material.innerPackaging!=null and material.innerPackaging!=''">
                and m1.inner_packaging like concat('%', #{material.innerPackaging}, '%')
            </if>
            <if test="material.outerPackaging!=null and material.outerPackaging!=''">
                and m1.outer_packaging like concat('%', #{material.outerPackaging}, '%')
            </if>
        </where>
    </select>

    <select id="getUnSyncList" resultType="com.natergy.ni.base.entity.Material">
      SELECT  nbm.*
      FROM ni_base_material nbm
      WHERE nbm.is_deleted = 0
        AND nbm.status = 9
        AND ISNULL(nbm.sync, 0) = 1
        AND (nbm.yonyou_sync IS NULL OR
             (nbm.yonyou_sync LIKE '%fail%' AND nbm.yonyou_sync NOT LIKE '%存货编码已经存在%'))
    </select>
</mapper>
