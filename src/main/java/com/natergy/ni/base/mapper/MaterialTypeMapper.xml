<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.MaterialTypeMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="materialTypeResultMap" type="com.natergy.ni.base.entity.MaterialType">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="sn" property="sn"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="com.natergy.ni.base.vo.MaterialTypeVO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
        <result column="has_children" property="hasChildren"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="ancestors" property="ancestors"/>
        <result column="sn" property="sn"/>
        <result column="remark" property="remark"/>
        <result column="yonyou_sync"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"
            property="yonyouSync"/>
    </resultMap>
    <select id="selectMaterialTypePage" resultType="com.natergy.ni.base.vo.MaterialTypeVO">
        SELECT *,
        (
        SELECT
        CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
        FROM
        blade_dept
        WHERE
        parent_id = ni_base_material_type.id and is_deleted = 0
        ) AS "has_children"
        FROM ni_base_material_type
        WHERE is_deleted = 0
        <if test="param2.code!=null and param2.code!=''">
            AND code = #{param2.code,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="tree" resultType="com.natergy.ni.base.vo.MaterialTypeVO">
        SELECT *, name + '(' + code + ')' AS title, id AS "value", id AS "key"
        FROM ni_base_material_type ${ew.customSqlSegment}
    </select>

    <select id="getUnSyncList" resultType="com.natergy.ni.base.entity.MaterialType">
      SELECT *
      FROM ni_base_material_type
      WHERE is_deleted = 0
      AND status = 9
      AND isnull(sync, 0) = 1
      AND isnull(JSON_VALUE(yonyou_sync,'$.value'),'un') in ('un','fail')
      and (ISNULL(JSON_VALUE(yonyou_sync,'$.sequence'), '') = '' or
      JSON_VALUE(yonyou_sync,'$.sequence') IN
      <foreach collection="sequenceList" item="value" separator="," open="(" close=")">
        #{value}
      </foreach>)
      order by code
    </select>
    <update id="updateParentId">
        UPDATE nfa
        SET parent_id =ISNULL(nfa1.id, 0)
        FROM ni_base_material_type nfa
                 LEFT JOIN ni_base_material_type nfa1
                           ON nfa.code LIKE nfa1.code + '%'
                               AND LEN(nfa.code) - LEN(nfa1.code) = 2
                               AND nfa1.is_deleted = 0

        WHERE nfa.is_deleted = 0
    </update>

    <update id="updateAncestors">
        UPDATE nfa
        SET ancestors =ISNULL('0,' +
                              STUFF(
                                  (SELECT ',' + CAST(a.id AS VARCHAR(200))
                                   FROM ni_base_material_type a
                                   WHERE a.is_deleted = 0
                                     AND nfa.code LIKE a.code + '%'
                                     AND LEN(nfa.code) > LEN(a.code)
                                   FOR XML PATH ( '' )),
                                  1,
                                  1,
                                  ''
                                  ), '0')
        FROM ni_base_material_type nfa
        WHERE nfa.is_deleted = 0
    </update>

    <select id="lazyList" resultMap="treeNodeResultMap">
      SELECT
      dept.* ,
      (
      SELECT
      CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
      FROM
      ni_base_material_type
      WHERE
      parent_id = dept.id and is_deleted = 0
      ) AS "has_children"
      FROM
      ni_base_material_type dept
      WHERE dept.is_deleted = 0
      <if test="parentId!=null">
        and dept.parent_id = #{parentId}
      </if>
      <if test="param2.type!=null and param2.type!=''">
        and dept.type=#{param2.type}
      </if>
      <if test="materialType.name!=null and materialType.name!=''">
        and (dept.name like concat(concat('%', #{materialType.name}),'%') or dept.id in
        (SELECT
        value
        FROM
        ni_base_material_type a cross apply STRING_SPLIT(a.ancestors,',')
        WHERE
        a.is_deleted= 0
        AND a.name LIKE concat ( concat ( '%', #{materialType.name} ), '%' )
        ) )
      </if>
      <if test="materialType.codeLike!=null and materialType.codeLike!=''">
        and dept.code like concat(#{materialType.codeLike},'%')
      </if>
      <if test="materialType.code!=null and materialType.code!=''">
        and dept.code = #{materialType.code}
      </if>
      <if test="materialType.ids!=null and materialType.ids.size()>0">
        and dept.id in
        <foreach collection="materialType.ids" item="value" separator="," open="(" close=")">
          #{value}
        </foreach>
      </if>
      ORDER BY dept.sn
    </select>

    <select id="getMaterialTypeChild" resultType="com.natergy.ni.base.entity.MaterialType">
        SELECT *
        FROM ni_base_material_type
        WHERE is_deleted = 0
          AND ancestors LIKE CONCAT(CONCAT('%', #{typeId}), '%')
    </select>
</mapper>
