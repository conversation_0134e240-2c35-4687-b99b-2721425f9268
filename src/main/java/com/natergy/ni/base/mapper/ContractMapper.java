/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.mapper;

import com.natergy.ni.base.entity.ContractEntity;
import com.natergy.ni.base.vo.ContractVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.time.LocalDate;
import java.util.List;

/**
 * 基本信息-合同管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
public interface ContractMapper extends BaseMapper<ContractEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param baseContract
	 * @return
	 */
	List<ContractVO> selectContractPage(IPage page, ContractVO baseContract);


    String getLastNoByMonth(LocalDate now);
}
