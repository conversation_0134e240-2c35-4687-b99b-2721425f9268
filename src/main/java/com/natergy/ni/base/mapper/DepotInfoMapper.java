/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.mapper;

import com.natergy.ni.base.dto.DepotLocationDTO;
import com.natergy.ni.base.entity.DepotInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.entity.DepotLocation;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface DepotInfoMapper extends BaseMapper<DepotInfo> {

  /**
   * 自定义分页
   *
   * @param depotInfo
   * @return
   */
  List<DepotInfo> selectListOrderByDistance(@Param("depotInfo") DepotInfo depotInfo);

  List<DepotLocationDTO> selectDepotLocationPage(IPage<DepotLocationDTO> page, Long depotId, String depotLocation);
}
