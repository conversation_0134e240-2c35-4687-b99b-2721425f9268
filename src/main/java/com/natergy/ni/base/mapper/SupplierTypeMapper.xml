<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.SupplierTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="supplierTypeResultMap" type="com.natergy.ni.base.entity.SupplierType">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="sn" property="sn"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="com.natergy.ni.base.vo.SupplierTypeVO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
        <result column="has_children" property="hasChildren"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="ancestors" property="ancestors"/>
        <result column="sn" property="sn"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <select id="selectSupplierTypePage" resultMap="supplierTypeResultMap">
        select *
        from ni_base_supplier_type
        where is_deleted = 0
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select *, name+'('+code+')' as title, id as "value", id as "key"
        from ni_base_supplier_type
            ${ew.customSqlSegment}
    </select>

    <select id="getUnSyncList" resultType="com.natergy.ni.base.entity.SupplierType">
        SELECT DISTINCT ni_base_supplier_type.*
        FROM ni_base_supplier_type
        OUTER APPLY
        OPENJSON(yonyou_sync)
        WITH
        (
        sequence VARCHAR(100),
        val VARCHAR(100) '$.value'
        )
        WHERE is_deleted = 0
        AND isnull(val,'un')='un'
        and (ISNULL(sequence, '') = '' or sequence IN
        <foreach collection="sequenceList" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>)
        order by code asc
  </select>
</mapper>
