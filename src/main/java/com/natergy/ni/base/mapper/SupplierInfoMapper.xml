<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.SupplierInfoMapper">
  <resultMap id="SupplierInfoVOResultMap" type="com.natergy.ni.base.vo.SupplierInfoVO">
    <result column="id" property="id"/>
    <result column="sync" property="sync"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_user" property="createUser"/>
    <result column="create_dept" property="createDept"/>
    <result column="create_time" property="createTime"/>
    <result column="update_user" property="updateUser"/>
    <result column="update_time" property="updateTime"/>
    <result column="status" property="status"/>
    <result column="is_deleted" property="isDeleted"/>
    <result column="name" property="name"/>
    <result column="code" property="code"/>
    <result column="type" property="type"/>
    <result column="province_code" property="provinceCode"/>
    <result column="city_code" property="cityCode"/>
    <result column="address" property="address"/>
    <result column="corporation" property="corporation"/>
    <result column="registered_capital" property="registeredCapital"/>
    <result column="level" property="level"/>
    <result column="scale" property="scale"/>
    <result column="linkman" property="linkman"/>
    <result column="link_phone" property="linkPhone"/>
    <result column="remark" property="remark"/>
    <result column="finance_bank" property="financeBank"/>
    <result column="finance_name" property="financeName"/>
    <result column="finance_account" property="financeAccount"/>
    <result column="tax_id" property="taxId"/>
    <result column="finance_address" property="financeAddress"/>
    <result column="finance_phone" property="financePhone"/>
    <result column="account_period" property="accountPeriod"/>
    <result column="payment_days" property="paymentDays"/>
    <result column="short_name" property="shortName"/>
    <result column="type_id" property="typeId"/>
    <result column="tags" property="tags"/>
    <result column="honest_status" property="honestStatus"/>
    <result column="honest_no" property="honestNo"/>
    <result column="honest_start_date" property="honestStartDate"/>
    <result column="honest_end_date" property="honestEndDate"/>
    <result column="apply_time" property="applyTime"/>
    <result column="blacklist" property="blacklist"/>
    <result column="acl" property="acl"/>
    <result column="yonyou_sync"
      typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"
      property="yonyouSync"/>
    <result column="usci" property="usci"/>
  </resultMap>
  <select id="selectSupplierInfoPage" resultMap="SupplierInfoVOResultMap">
    SELECT *
    FROM ni_base_supplier nbs
    WHERE is_deleted = 0
    <if test="param2.sync!=null">
      and sync = #{param2.sync}
    </if>
    <if test="param2.keyword!=null and param2.keyword!=''">
      and (name like concat('%',#{param2.keyword},'%') or code like
      concat('%',#{param2.keyword},'%') or cast(id as varchar(100))=#{param2.keyword})
    </if>
    <if test="param2.name!=null and param2.name!=''">
      <bind name="nameLike" value="'%'+param2.name+'%'"/>
      and name like #{nameLike}
    </if>
    <if test="param2.code!=null and param2.code!=''">
      and code like concat(#{param2.code},'%')
    </if>
    <if test="param2.status!=null">
      and status = #{param2.status}
    </if>
    <if test="param2.id!=null">
      and id = #{param2.id}
    </if>
    <if test="param2.level!=null and param2.level!=''">
      and level = #{param2.level}
    </if>
    <if test="param2.typeIds!=null">
      and type_id in
      <foreach collection="param2.typeIds" item="value" separator=" or " open="("
        close=")">
        #{value}
      </foreach>
    </if>
    <if test="param2.provinceCode!=null and param2.provinceCode!=''">
      and province_code =#{param2.provinceCode}
    </if>
    <if test="param2.cityCode!=null and param2.cityCode!=''">
      and city_code = #{param2.cityCode}
    </if>
    <if test="param2.linkman!=null and param2.linkman!=''">
      <bind name="linkmanLike" value="'%'+param2.linkman+'%'"/>
      and (linkman like #{linkmanLike} or id in (select supplier_id from ni_base_supplier_contact
      where is_deleted=0 and name like #{linkmanLike}))
    </if>
    <if test="param2.linkPhone!=null and param2.linkPhone!=''">
      <bind name="linkPhoneLike" value="'%'+param2.linkPhone+'%'"/>
      and (link_phone like #{linkPhone} or id in (select supplier_id from ni_base_supplier_contact
      where is_deleted=0 and phone like #{linkPhoneLike}))
    </if>
    <if test="param2.honestStatus!=null">
      and honest_status = #{param2.honestStatus}
    </if>
    <if test="param2.honestNo!=null and param2.honestNo!=''">
      <bind name="honestNoLike" value="'%'+param2.honestNo+'%'"/>
      and honest_no like #{honestNoLike}
    </if>
    <if test="param2.blacklist!=null">
      and isnull(blacklist,0) = #{param2.blacklist}
    </if>
    <if test="param2.acl!=null and param2.acl!=''">
      and acl = #{param2.acl}
    </if>
    <if test="param2.usci!=null and param2.usci!=''">
      <bind name="usciLike" value="'%'+param2.usci+'%'"/>
      and usci like #{usciLike}
    </if>
    <if test="param2.own!=null and param2.own==true and param2.queryDeptId!=null">
      and ( acl='2' or(acl='1' and id in (
      select supplier_id from ni_base_supplier_dept where dept_id in
      <foreach collection="param2.queryDeptId" item="value" separator=" or " open="("
        close=")">
        #{value}
      </foreach>)))
    </if>
    <if test="param2.scope!=null and param2.scope!=''">
      <bind name="scopeLike" value="'%'+param2.scope+'%'"/>
      and scope like #{scopeLike}
    </if>
    <if test="param2.tags!=null and param2.tags!=''">
      <bind name="tagsLike" value="'%'+param2.tags+'%'"/>
      and tags like #{tagsLike}
    </if>
    order by id desc
  </select>

  <select id="logisticsList" resultType="com.natergy.ni.base.vo.SupplierInfoVO">
    SELECT nbs.*
    FROM ni_base_supplier nbs
           LEFT JOIN blade_dict_biz bdb ON nbs.type = bdb.id AND bdb.is_deleted = 0
      AND bdb.is_sealed = 0
    WHERE nbs.is_deleted = 0
      AND (nbs.code LIKE CONCAT('%', #{query,jdbcType=VARCHAR}, '%')
      OR nbs.name LIKE CONCAT('%', #{query,jdbcType=VARCHAR}, '%'))
  </select>

  <select id="bopTotal" resultType="java.math.BigDecimal">
    select isnull(nbs.initial_amount, 0)
    <if test="startDate!=null">
      <![CDATA[
    +(select isnull(sum(npoi.amount), 0)
    from ni_por_order npo
    left join ni_por_order_item npoi
    on npo.id = npoi.order_id and npoi.is_deleted = 0
    where npo.is_deleted = 0
    and nbs.id = npo.supplier_id
    and npo.purchase_time<#{startDate,jdbcType=DATE}
    )-(select isnull(sum(amount), 0)
    from ni_fin_trans_flow nftf
    where nftf.is_deleted = 0
    and nftf.type = '1'
    and nftf.sub_type = '1'
    and nftf.related_id = nbs.id
    and nftf.date <#{startDate,jdbcType=DATE})+
    (select sum(amount)
    from ni_fin_trans_flow nftf
    where nftf.is_deleted = 0
    and nftf.type = '1'
    and nftf.sub_type in ('2', '3')
    and nftf.related_id = nbs.id
    and nftf.date <#{startDate,jdbcType=DATE})
    ]]>
    </if>
    from ni_base_supplier nbs
    where is_deleted = 0
  </select>

  <select id="getUnSyncList" resultType="com.natergy.ni.base.entity.SupplierInfo">
    SELECT *
    FROM ni_base_supplier
    WHERE is_deleted = 0
      AND ISNULL(sync, 0) = 1
      AND status = 2
      AND ISNULL(code, '') = ''
    AND ISNULL(JSON_VALUE(yonyou_sync,'$.value'),'un') in ('un','fail')
    AND (ISNULL(JSON_VALUE(yonyou_sync,'$.sequence'), '') = '' or
    JSON_VALUE(yonyou_sync,'$.sequence') IN
    <foreach collection="sequenceList" item="value" separator="," open="(" close=")">
      #{value}
    </foreach>)
  </select>
</mapper>
