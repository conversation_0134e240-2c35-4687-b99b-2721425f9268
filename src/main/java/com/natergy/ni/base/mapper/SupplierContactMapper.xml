<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.base.mapper.SupplierContactMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="supplierContactResultMap" type="com.natergy.ni.base.entity.SupplierContact">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="name" property="name"/>
        <result column="dept" property="dept"/>
        <result column="post" property="post"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="hometown" property="hometown"/>
        <result column="phone" property="phone"/>
        <result column="qq" property="qq"/>
        <result column="wechat" property="wechat"/>
        <result column="email" property="email"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectSupplierContactPage" resultMap="supplierContactResultMap">
        select * from ni_base_supplier_contact where is_deleted = 0
    </select>

</mapper>
