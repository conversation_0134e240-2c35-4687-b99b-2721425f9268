/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.base.entity.SupplierContact;
import com.natergy.ni.base.vo.SupplierContactVO;
import java.util.Objects;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
public class SupplierContactWrapper extends BaseEntityWrapper<SupplierContact, SupplierContactVO>  {

	public static SupplierContactWrapper build() {
		return new SupplierContactWrapper();
 	}

	@Override
	public SupplierContactVO entityVO(SupplierContact supplierContact) {
		SupplierContactVO supplierContactVO = Objects.requireNonNull(BeanUtil.copy(supplierContact, SupplierContactVO.class));

		//User createUser = UserCache.getUser(supplierContact.getCreateUser());
		//User updateUser = UserCache.getUser(supplierContact.getUpdateUser());
		//supplierContactVO.setCreateUserName(createUser.getName());
		//supplierContactVO.setUpdateUserName(updateUser.getName());

		return supplierContactVO;
	}

}
