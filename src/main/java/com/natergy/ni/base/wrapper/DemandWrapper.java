package com.natergy.ni.base.wrapper;

import com.natergy.ni.base.entity.DemandEntity;
import com.natergy.ni.base.vo.DemandVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 研发需求 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
public class DemandWrapper extends BaseEntityWrapper<DemandEntity, DemandVO> {

	public static DemandWrapper build() {
		return new DemandWrapper();
	}
	@Override
	public DemandVO entityVO(DemandEntity Demand) {
		DemandVO DemandVO = Objects.requireNonNull(BeanUtil.copy(Demand, DemandVO.class));

		//User createUser = UserCache.getUser(PaLeave.getCreateUser());
		//User updateUser = UserCache.getUser(PaLeave.getUpdateUser());
		//DemandVO.setCreateUserName(createUser.getName());
		//DemandVO.setUpdateUserName(updateUser.getName());

		return DemandVO;
	}


}
