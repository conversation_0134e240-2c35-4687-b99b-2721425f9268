/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.wrapper;

import com.natergy.ni.base.entity.SupplierInfo;
import com.natergy.ni.base.vo.SupplierInfoVO;
import java.util.Objects;
import org.springblade.common.cache.RegionCache;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.system.entity.Region;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
public class SupplierInfoWrapper extends BaseEntityWrapper<SupplierInfo, SupplierInfoVO> {

  public static SupplierInfoWrapper build() {
    return new SupplierInfoWrapper();
  }

  @Override
  public SupplierInfoVO entityVO(SupplierInfo supplierInfo) {
    SupplierInfoVO supplierInfoVO = Objects.requireNonNull(
        BeanUtil.copy(supplierInfo, SupplierInfoVO.class));
    Region province = RegionCache.getByCode(supplierInfoVO.getProvinceCode());
    if (province != null) {
      supplierInfoVO.setProvince(province.getProvinceName());
    }
    Region city = RegionCache.getByCode(supplierInfoVO.getCityCode());
    if (city != null) {
      supplierInfoVO.setCity(city.getCityName());
    }
    //User createUser = UserCache.getUser(supplierInfo.getCreateUser());
    //User updateUser = UserCache.getUser(supplierInfo.getUpdateUser());
    //supplierInfoVO.setCreateUserName(createUser.getName());
    //supplierInfoVO.setUpdateUserName(updateUser.getName());

    return supplierInfoVO;
  }

}
