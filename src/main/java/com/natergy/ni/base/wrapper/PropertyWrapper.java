/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.base.entity.PropertyEntity;
import com.natergy.ni.base.vo.PropertyVO;
import java.util.Objects;

/**
 * 动态属性 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
public class PropertyWrapper extends BaseEntityWrapper<PropertyEntity, PropertyVO>  {

	public static PropertyWrapper build() {
		return new PropertyWrapper();
 	}

	@Override
	public PropertyVO entityVO(PropertyEntity property) {
		PropertyVO propertyVO = Objects.requireNonNull(BeanUtil.copy(property, PropertyVO.class));

		//User createUser = UserCache.getUser(property.getCreateUser());
		//User updateUser = UserCache.getUser(property.getUpdateUser());
		//propertyVO.setCreateUserName(createUser.getName());
		//propertyVO.setUpdateUserName(updateUser.getName());

		return propertyVO;
	}


}
