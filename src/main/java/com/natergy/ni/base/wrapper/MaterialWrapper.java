/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.wrapper;

import com.natergy.ni.base.entity.Material;
import com.natergy.ni.base.vo.MaterialVO;
import java.util.List;
import java.util.stream.Collectors;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.system.entity.User;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public class MaterialWrapper extends BaseEntityWrapper<Material, MaterialVO> {

  public static MaterialWrapper build() {
    return new MaterialWrapper();
  }

  @Override
  public MaterialVO entityVO(Material material) {
    MaterialVO materialInfoVO = BeanUtil.copy(material, MaterialVO.class);
    User createUser = UserCache.getUser(material.getCreateUser());
    if (createUser != null) {
      assert materialInfoVO != null;
      materialInfoVO.setCreateUserName(createUser.getRealName());
      List<String> deptNames =  SysCache.getDeptNames(createUser.getDeptId());
      if(!deptNames.isEmpty()){
        materialInfoVO.setCreateDeptName(String.join(",", deptNames));
      }
    }
    //materialInfoVO.setUpdateUserName(updateUser.getName());

    return materialInfoVO;
  }

}
