
package com.natergy.ni.base.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.base.entity.ToolManagementEntity;
import com.natergy.ni.base.vo.ToolManagementVO;
import java.util.Objects;

/**
 * 基本信息-工具管理 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public class ToolManagementWrapper extends BaseEntityWrapper<ToolManagementEntity, ToolManagementVO>  {

	public static ToolManagementWrapper build() {
		return new ToolManagementWrapper();
 	}

	@Override
	public ToolManagementVO entityVO(ToolManagementEntity toolManagementEntity) {
		ToolManagementVO toolManagementVO = Objects.requireNonNull(BeanUtil.copy(toolManagementEntity, ToolManagementVO.class));

		//User createUser = UserCache.getUser(niBaseTool.getCreateUser());
		//User updateUser = UserCache.getUser(niBaseTool.getUpdateUser());
		//niBaseToolVO.setCreateUserName(createUser.getName());
		//niBaseToolVO.setUpdateUserName(updateUser.getName());

		return toolManagementVO;
	}


}
