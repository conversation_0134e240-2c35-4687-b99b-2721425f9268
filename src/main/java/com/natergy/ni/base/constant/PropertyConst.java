package com.natergy.ni.base.constant;

public class PropertyConst {

	public static final String PROPERTY_PREFIX = "ni_property_";

	public static final String EXTENDED_PROPERTY_PREFIX = "ni_extended_property_";

	/**
	 * 动态属性组
	 */
	public enum Group {

		SKU("SKU", "SKU");

		private final String value;

		private final String description;

		Group(String value, String description) {
			this.value = value;
			this.description = description;
		}

		public String value() {
			return value;
		}

		public String description() {
			return description;
		}

	}

	/**
	 * 是/否 枚举
	 */
	public enum YesNo {

		YES(1, "是"),
		NO(0, "否");

		private final Integer value;

		private final String description;

		YesNo(Integer value, String description) {
			this.value = value;
			this.description = description;
		}

		public Integer value() {
			return value;
		}

		public String description() {
			return description;
		}

		public static YesNo fromValue(Integer value) {
			for (YesNo type : YesNo.values()) {
				if (type.value.equals(value)) {
					return type;
				}
			}
			throw new IllegalArgumentException("无效是/否值: " + value);
		}
	}
}
