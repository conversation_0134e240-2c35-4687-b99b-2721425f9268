
package com.natergy.ni.base.service;

import com.natergy.ni.base.entity.ToolManagementEntity;
import com.natergy.ni.base.excel.ToolManagementExcel;
import com.natergy.ni.base.vo.ToolManagementVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.system.entity.User;

import java.util.List;

/**
 * 基本信息-工具管理 服务类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public interface IToolManagementService extends BaseService<ToolManagementEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param toolManagementVO
	 * @return
	 */
	IPage<ToolManagementVO> selectBaseToolPage(IPage<ToolManagementVO> page, ToolManagementVO toolManagementVO);

	ToolManagementVO handlePageRes(ToolManagementVO toolManagementVO);

	User selectUserInfo(Long userId);

	void importTool(List<ToolManagementExcel> data);

}
