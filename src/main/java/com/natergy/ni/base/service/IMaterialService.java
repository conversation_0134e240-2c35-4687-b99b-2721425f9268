/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.entity.Material;
import com.natergy.ni.base.entity.MaterialUnit;
import com.natergy.ni.base.excel.MaterialComparisonExcel;
import com.natergy.ni.base.excel.MaterialExcel;
import com.natergy.ni.base.vo.MaterialVO;
import com.natergy.ni.base.vo.MaterialWithStockVO;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.system.entity.DictBiz;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface IMaterialService extends BaseService<Material> {

  /**
   * 带库存分页
   *
   * @param page
   * @param material
   * @return
   */
  IPage<MaterialWithStockVO> pageWithStock(IPage<MaterialWithStockVO> page, MaterialVO material);

  List<MaterialUnit> selectMaterialUnitList(Long id);

  boolean saveOrUpdateVO(MaterialVO material);

  List<DictBiz> selectMaterialUnitDict(Long id);

  List<Material> listByCodes(Set<String> codes);

  boolean apply(MaterialVO materialVO, String processDefKey);

  List<Map<String, Object>> sync(Long id, boolean isCovered);

  void yonyouSync();

  void importMaterial(List<MaterialExcel> data, Boolean isCovered);

  boolean updateDisabledById(Long id, Boolean disabled);

  boolean submit(Material material);

	List<MaterialComparisonExcel> comparison(List<MaterialComparisonExcel> items);
}
