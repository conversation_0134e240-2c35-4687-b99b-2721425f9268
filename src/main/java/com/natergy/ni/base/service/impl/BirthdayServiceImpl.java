package com.natergy.ni.base.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.natergy.ni.base.entity.BirthdayEntity;
import com.natergy.ni.base.mapper.BirthdayMapper;
import com.natergy.ni.base.service.IBirthdayService;
import com.natergy.ni.base.vo.UserBirthdayVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BirthdayServiceImpl extends BaseServiceImpl<BirthdayMapper, BirthdayEntity> implements
	IBirthdayService {

	private final IUserService userService;

	private final RedisTemplate<String, Object> redisTemplate;

	private final IAttachService attachService;

	@Override
	public List<BirthdayEntity> findBirthdays(LocalDate startDate, LocalDate endDate) {
		return null;
	}

	@Override
	public BirthdayEntity saveBirthday(BirthdayEntity birthday) {
		return null;
	}

	@Override
	public List<BirthdayEntity> loadWishes(Long receiverId) {
		return null;
	}

	private boolean birthdayRemind(LocalDate birthday, LocalDate start, LocalDate end) {

		return !(birthday.isBefore(start) || birthday.isAfter(end));
	}

	@Override
	public List<UserBirthdayVO> getBirthdayRemindUser() {
		LocalDate localDate = LocalDate.now();
		List<User> allUserList = userService.list();
		LocalDate start = localDate.plusDays(1);
		LocalDate end = localDate.plusDays(7);

		BladeUser user = AuthUtil.getUser();

		return allUserList.stream()
			.filter(item -> (!Objects.equals(item.getId(), user.getUserId())) && Objects.nonNull(item.getBirthday()))
			.filter(item -> {
				LocalDate birthdayLocal = item.getBirthday().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
				LocalDate changeYearBirthDay = birthdayLocal.withYear(localDate.getYear());
				Integer isDeleted = item.getIsDeleted();
				Integer status = item.getStatus();
				return birthdayRemind(changeYearBirthDay, start, end) && !Objects.equals(isDeleted, 1) && !Objects.equals(status, 2);
			}).map(item -> {

				String avatarId = item.getAvatar();

				String avatarSrc = "";

				if (StringUtil.isNotBlank(avatarId)) {
					avatarSrc = attachService.getLinkById(Long.parseLong(avatarId));
				}
				LocalDate birthdayLocal = item.getBirthday().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
				LocalDate changeYearBirthDay = birthdayLocal.withYear(localDate.getYear());
				long birthdayDiff = localDate.until(changeYearBirthDay, ChronoUnit.DAYS);

				return new UserBirthdayVO(item.getId(), item.getName(), avatarSrc, birthdayDiff, changeYearBirthDay);
			}).sorted(Comparator.comparing(UserBirthdayVO::getBirthday))
			.collect(Collectors.toList());



	}
}
