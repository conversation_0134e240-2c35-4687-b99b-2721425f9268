package com.natergy.ni.base.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.entity.DemandEntity;
import com.natergy.ni.base.mapper.DemandMapper;
import com.natergy.ni.base.service.IDemandService;
import com.natergy.ni.base.vo.DemandVO;
import io.jsonwebtoken.lang.Assert;
import lombok.RequiredArgsConstructor;
import org.springblade.common.cache.DictBizCache;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.plugin.workflow.design.entity.WfProcessDef;
import org.springblade.plugin.workflow.design.service.IWfFormService;
import org.springblade.plugin.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 研发需求 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Service
@RequiredArgsConstructor
public class DemandServiceImpl extends BaseServiceImpl<DemandMapper, DemandEntity> implements
	IDemandService {

	private final IWfFormService formService;
	private final IWfProcessService processService;

	@Override
	public IPage<DemandVO> selectDemandPage(IPage<DemandVO> page, DemandVO Demand) {
		return page.setRecords(baseMapper.selectDemandPage(page, Demand));
	}

	@Override
	public Boolean submit(DemandEntity demandEntity) {
		demandEntity.setTenantId(BladeConstant.ADMIN_TENANT_ID);
		return this.saveOrUpdate(demandEntity);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Boolean submitAndStart(String processDefKey, DemandEntity demandEntity) {
		Assert.isTrue(this.submit(demandEntity), "提交失败");
		Map<String, Object> map = formService.getFormByProcessDefKey(processDefKey);
		String processDefId = "";
		if (map.containsKey("process")) {
			WfProcessDef processDef = (WfProcessDef) map.get("process");
			processDefId = processDef.getId();
		}
		Assert.isTrue(!"".equals(processDefId), "获取流程信息失败");
		Map<String, Object> body = JSON.parseObject(
			JSONObject.toJSONStringWithDateFormat(demandEntity, "yyyy-MM-dd"),
			new TypeReference<Map<String, Object>>() {
			});
		String processInsId = processService.startProcessInstanceById(processDefId, body);
		return true;
	}

	@Override
	public Boolean updateStatusCurrentNodeById(DemandEntity demandEntity) {
		UpdateWrapper<DemandEntity> wrapper = new UpdateWrapper<>();
		wrapper.lambda().eq(DemandEntity::getId, demandEntity.getId())
			.set(DemandEntity::getStatus, demandEntity.getStatus())
			.set(DemandEntity::getCurrentNode, demandEntity.getCurrentNode());
		return this.update(wrapper);
	}
}
