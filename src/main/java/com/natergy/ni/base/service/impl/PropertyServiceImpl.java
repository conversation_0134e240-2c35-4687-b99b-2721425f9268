/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.natergy.ni.base.constant.PropertyConst;
import com.natergy.ni.base.dto.PropertyOptionDTO;
import com.natergy.ni.base.dto.PropertyWidthOptionDTO;
import com.natergy.ni.base.dto.crud.column.OptionColumnDictData;
import com.natergy.ni.base.entity.PropertyEntity;
import com.natergy.ni.base.entity.PropertyOptionEntity;
import com.natergy.ni.base.service.IPropertyOptionService;
import com.natergy.ni.base.vo.PropertyVO;
import com.natergy.ni.base.mapper.PropertyMapper;
import com.natergy.ni.base.service.IPropertyService;
import lombok.AllArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 动态属性 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Service
@AllArgsConstructor
public class PropertyServiceImpl extends BaseServiceImpl<PropertyMapper, PropertyEntity> implements IPropertyService {

	private IPropertyOptionService propertyOptionService;

	private void check(PropertyEntity entity) {

		entity.setGroupName(PropertyConst.Group.SKU.value());
		entity.setTenantId(BladeConstant.ADMIN_TENANT_ID);

		LambdaQueryWrapper<PropertyEntity> queryWrapper = new LambdaQueryWrapper<PropertyEntity>()
			.ne(entity.getId() != null, PropertyEntity::getId, entity.getId())
			.eq(PropertyEntity::getName, entity.getName());

		PropertyEntity one = getOne(queryWrapper);

		if (one != null) {
			throw new ServiceException("属性已存在");
		}
	}

	@Override
	public boolean save(PropertyEntity entity) {

		check(entity);

		return super.save(entity);
	}

	@Override
	public boolean updateById(PropertyEntity entity) {

		check(entity);

		return super.updateById(entity);
	}

	@Override
	public boolean saveOrUpdate(PropertyEntity entity) {

		check(entity);

		return super.saveOrUpdate(entity);
	}

	@Override
	public IPage<PropertyVO> selectPropertyPage(IPage<PropertyVO> page, PropertyVO property) {
		return page.setRecords(baseMapper.selectPropertyPage(page, property));
	}

	/**
	 * 获取所有已启用属性,同时获取属性可选项
	 */
	@Override
	public List<PropertyWidthOptionDTO> selectAvailablePropertyWidthOptions() {
		List<PropertyEntity> propertyList = list(
			new LambdaQueryWrapper<PropertyEntity>()
				.eq(PropertyEntity::getActive, 1)
				.eq(PropertyEntity::getGroupName, PropertyConst.Group.SKU.value())
				.isNull(PropertyEntity::getCategory)
				.orderByAsc(PropertyEntity::getOrdered)
		);

		List<PropertyOptionEntity> propertyOptionList = propertyOptionService.list(
			new LambdaQueryWrapper<PropertyOptionEntity>()
				.eq(PropertyOptionEntity::getActive, 1)
				.in(
					PropertyOptionEntity::getPropertyId,
					propertyList.stream().map(PropertyEntity::getId).collect(Collectors.toList())
				)
				.orderByAsc(PropertyOptionEntity::getOrdered)
		);

		List<PropertyWidthOptionDTO> list = new ArrayList<>();

		for (PropertyEntity property : propertyList) {
			PropertyWidthOptionDTO dto = BeanUtil.copyProperties(property, PropertyWidthOptionDTO.class);

			if (dto != null) {
				List<PropertyOptionDTO> options = propertyOptionList.stream()
					.filter(o -> o.getPropertyId().longValue() == property.getId().longValue())
					.map(o -> BeanUtil.copyProperties(o, PropertyOptionDTO.class))
					.collect(Collectors.toList());

				dto.setOptionList(options);

				list.add(dto);
			}
		}

		return list;
	}


}
