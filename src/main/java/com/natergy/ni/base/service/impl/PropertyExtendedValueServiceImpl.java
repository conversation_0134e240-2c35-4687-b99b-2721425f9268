/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.natergy.ni.base.entity.PropertyExtendedValueEntity;
import com.natergy.ni.base.vo.PropertyExtendedValueVO;
import com.natergy.ni.base.mapper.PropertyExtendedValueMapper;
import com.natergy.ni.base.service.IPropertyExtendedValueService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 动态属性扩展属性值 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Service
public class PropertyExtendedValueServiceImpl extends BaseServiceImpl<PropertyExtendedValueMapper, PropertyExtendedValueEntity> implements IPropertyExtendedValueService {

	@Override
	public boolean saveBatch(Collection<PropertyExtendedValueEntity> entityList) {

		for (PropertyExtendedValueEntity entity : entityList) {
			entity.setTenantId(BladeConstant.ADMIN_TENANT_ID);
		}

		return super.saveBatch(entityList);
	}

	@Override
	public boolean deleteExtendedValueByPropertyOptionId(Long propertyId, Long optionId) {
		LambdaQueryWrapper<PropertyExtendedValueEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(PropertyExtendedValueEntity::getPropertyId, propertyId);
		queryWrapper.eq(PropertyExtendedValueEntity::getPropertyOptionId, optionId);

		return this.remove(queryWrapper);
	}

	@Override
	public List<PropertyExtendedValueEntity> selectExtendedValueByPropertyOptionId(Long propertyId, Long optionId) {

		LambdaQueryWrapper<PropertyExtendedValueEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(PropertyExtendedValueEntity::getPropertyId, propertyId);
		queryWrapper.eq(PropertyExtendedValueEntity::getPropertyOptionId, optionId);

		return this.list(queryWrapper);
	}

	@Override
	public List<PropertyExtendedValueEntity> selectExtendedValueByPropertyId(Long propertyId) {
		LambdaQueryWrapper<PropertyExtendedValueEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(PropertyExtendedValueEntity::getPropertyId, propertyId);

		return this.list(queryWrapper);
	}

	@Override
	public IPage<PropertyExtendedValueVO> selectPropertyExtendedValuePage(IPage<PropertyExtendedValueVO> page, PropertyExtendedValueVO propertyExtendedValue) {
		return page.setRecords(baseMapper.selectPropertyExtendedValuePage(page, propertyExtendedValue));
	}


}
