package com.natergy.ni.base.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class MaterialTypeYonyouSync extends QuartzJobBean {

	private final IMaterialTypeService materialTypeService;

	/**
	 * @param context
	 * @throws JobExecutionException
	 */
	@Override
	protected void executeInternal(JobExecutionContext context)  {
		try{
			log.info("存货档案分类开始同步");
			materialTypeService.yonyouSync();
			log.info("存货档案分类同步完成");
		}catch (Exception e){
			log.error("存货档案分类同步失败",e);
		}
	}
}
