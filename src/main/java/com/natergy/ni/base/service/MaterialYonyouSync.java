package com.natergy.ni.base.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class MaterialYonyouSync extends QuartzJobBean {

  private final IMaterialService materialService;

  /**
   * @param context
   * @throws JobExecutionException
   */
  @Override
  protected void executeInternal(@NotNull JobExecutionContext context){
    try{
      log.info("存货档案开始同步");
      materialService.yonyouSync();
      log.info("存货档案同步完成");
    }catch (Exception e){
      log.error("存货档案同步失败",e);
    }
  }
}
