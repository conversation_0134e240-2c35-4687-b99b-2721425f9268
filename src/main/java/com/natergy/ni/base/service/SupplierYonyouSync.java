package com.natergy.ni.base.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SupplierYonyouSync extends QuartzJobBean {

	private final ISupplierInfoService supplierInfoService;

	/**
	 * @param context
	 * @throws JobExecutionException
	 */
	@Override
	protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
		try{
			log.info("供应商信息开始同步");
			supplierInfoService.yonyouSync();
			log.info("供应商信息同步完成");
		}catch (Exception e){
			log.error("供应商信息同步失败",e);
		}
	}
}
