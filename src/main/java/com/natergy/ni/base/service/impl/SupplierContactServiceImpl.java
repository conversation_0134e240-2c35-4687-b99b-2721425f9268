/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.natergy.ni.base.entity.SupplierContact;
import com.natergy.ni.base.vo.SupplierContactVO;
import com.natergy.ni.base.mapper.SupplierContactMapper;
import com.natergy.ni.base.service.ISupplierContactService;
import java.util.List;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
@Service
public class SupplierContactServiceImpl extends
	BaseServiceImpl<SupplierContactMapper, SupplierContact> implements ISupplierContactService {

	@Override
	public IPage<SupplierContactVO> selectSupplierContactPage(IPage<SupplierContactVO> page,
		SupplierContactVO supplierContact) {
		return page.setRecords(baseMapper.selectSupplierContactPage(page, supplierContact));
	}

	@Override
	public List<SupplierContact> getContactsBySupplierId(Long id) {
		QueryWrapper<SupplierContact> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(SupplierContact::getSupplierId, id);
		return list(wrapper);
	}

}
