/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import static com.natergy.ni.base.constant.BaseConstant.CONTRACT_POR_MODULE;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.natergy.ni.base.cache.SupplierCache;
import com.natergy.ni.base.controller.ContractController;
import com.natergy.ni.base.dto.ContractArchiveDTO;
import com.natergy.ni.base.entity.ContractEntity;
import com.natergy.ni.base.entity.SerialNo;
import com.natergy.ni.base.entity.SupplierInfo;
import com.natergy.ni.base.event.ContractPorEvent;
import com.natergy.ni.base.mapper.ContractMapper;
import com.natergy.ni.base.service.IContractService;
import com.natergy.ni.base.service.ISerialNoService;
import com.natergy.ni.base.vo.ContractVO;
import io.jsonwebtoken.lang.Assert;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.OptionalInt;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.User;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springblade.plugin.workflow.design.entity.WfProcessDef;
import org.springblade.plugin.workflow.design.service.IWfFormService;
import org.springblade.plugin.workflow.process.mapper.WfProcessInfoMapper;
import org.springblade.plugin.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 基本信息-合同管理 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Service
@RequiredArgsConstructor
public class ContractServiceImpl extends BaseServiceImpl<ContractMapper, ContractEntity> implements
	IContractService {

	private final ISerialNoService serialNoService;

	private final IWfFormService formService;

	private final IWfProcessService processService;

	private final WfProcessInfoMapper wfProcessInfoMapper;

	private final IAttachService attachService;

	public static final String MODULE = "ni_base_contract_archive";

	@Override
	public IPage<ContractVO> selectContractPage(IPage<ContractVO> page, ContractVO baseContract) {
		Page<ContractVO> p = (Page<ContractVO>) page;
		p.setMaxLimit(-1L);
		List<ContractVO> res = selectContractList(p, baseContract);
		return page.setRecords(res);
	}

	public List<ContractVO> lazyList(Long parentId, ContractVO params) {
		if (params == null) {
			params = new ContractVO();
		}
		params.setParentId(parentId);
		params.setType(ContractEntity.TYPE_SUPPLEMENT);
		return selectContractList(null, params);
	}

	private List<ContractVO> selectContractList(IPage<ContractVO> page, ContractVO contract) {
		List<ContractVO> res = baseMapper.selectContractPage(page, contract);
		if (res.isEmpty()) {
			return new ArrayList<>();
		}
		Set<Long> contractIds = res.stream().map(ContractVO::getId).collect(Collectors.toSet());
		List<Attach> attaches = attachService.list(
			Wrappers.<Attach>lambdaQuery().eq(Attach::getBusinessName, MODULE)
				.in(Attach::getBusinessKey, contractIds));
		Map<String, List<Attach>> attachMap = attaches.stream()
			.collect(Collectors.groupingBy(Attach::getBusinessKey));
		res.forEach(item -> {
			User user = UserCache.getUser(item.getCreateUser());
			if (user != null) {
				item.setCreateUserName(user.getRealName());
			}
			item.setCreateDeptName(SysCache.getDeptName(item.getCreateDept()));
			if (item.getA() == null || item.getA() == 0) {
				item.setAName("山东能特异能源科技有限公司");
			} else if ("supplier".equals(item.getAaType())) {
				SupplierInfo supplierInfo = SupplierCache.getById(item.getA());
				if (supplierInfo != null) {
					item.setAName(supplierInfo.getName());
				}
			}
			if (item.getB() == null || item.getB() == 0) {
				item.setBName("山东能特异能源科技有限公司");
			} else if ("supplier".equals(item.getBbType())) {
				SupplierInfo supplierInfo = SupplierCache.getById(item.getB());
				if (supplierInfo != null) {
					item.setBName(supplierInfo.getName());
				}
			}
			List<Attach> attaches1 = attachMap.get(item.getId().toString());
			if (attaches1 != null && !attaches1.isEmpty()) {
				attaches1.sort(Comparator.comparing(Attach::getCreateTime).reversed());
				item.setAttachPreview(attaches1.get(0).getLink());
				item.setAttachId(attaches1.get(0).getId());
				item.setArchiveAttachName(attaches1.get(0).getOriginalName());
			}
		});
		return res;
	}

	@Override
	public boolean end(List<Long> ids) {
		UpdateWrapper<ContractEntity> wrapper = new UpdateWrapper<>();
		wrapper.lambda().in(ContractEntity::getId, ids)
			.set(ContractEntity::getStatus, WfEntity.STATUS_TERMINATION);
		return update(wrapper);
	}

	@Override
	public boolean toVoid(List<Long> ids) {
		QueryWrapper<ContractEntity> w = new QueryWrapper<>();
		w.lambda().notIn(ContractEntity::getContractState, ContractEntity.CONTRACT_STATE_END)
			.in(ContractEntity::getId, ids);
		long num = count(w);
		Assert.isTrue(ids.size() <= num, "数据状态错误");
		UpdateWrapper<ContractEntity> wrapper = new UpdateWrapper<>();
		wrapper.lambda().ne(ContractEntity::getContractState, ContractEntity.CONTRACT_STATE_END)
			.in(ContractEntity::getId, ids)
			.set(ContractEntity::getContractState, ContractEntity.CONTRACT_STATE_END);
		return update(wrapper);
	}

	@Override
	public boolean finish(List<Long> ids) {
		return update(Wrappers.<ContractEntity>lambdaUpdate().in(ContractEntity::getId, ids)
			.notIn(ContractEntity::getContractState, ContractEntity.CONTRACT_STATE_END,
				ContractEntity.CONTRACT_STATE_TO_VOID, ContractEntity.CONTRACT_STATE_FINISH)
			.set(ContractEntity::getContractState, ContractEntity.CONTRACT_STATE_FINISH)
			.set(ContractEntity::getFinishTime, new Date()));
	}

	@Override
	public boolean updateStateById(Long id, String contractState) {
		return update(Wrappers.<ContractEntity>lambdaUpdate().eq(ContractEntity::getId, id)
			.set(ContractEntity::getContractState, contractState)
			.set(contractState.equals(ContractEntity.CONTRACT_STATE_FINISH),
				ContractEntity::getFinishTime, new Date()));
	}

	/**
	 * @param id
	 * @return
	 */
	@Override
	public ContractVO detailById(Long id) {
		ContractVO q = new ContractVO();
		q.setId(id);
		List<ContractVO> data = selectContractList(null, q);
		if (data.size() == 1) {
			return data.get(0);
		}
		return null;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean submit(ContractEntity contractEntity) {
		contractEntity.setBbType("supplier");
		contractEntity.setTenantId(BladeConstant.ADMIN_TENANT_ID);
		if (contractEntity.getStatus().equals(WfEntity.STATUS_SUBMIT)) {
			QueryWrapper<SerialNo> wrapper = new QueryWrapper<>();
			wrapper.lambda().eq(SerialNo::getCode, ContractController.MODULE);
			SerialNo no = serialNoService.getOne(wrapper);
			contractEntity.setSerialNo(serialNoService.gen(no));
		}
		boolean res = this.saveOrUpdate(contractEntity);
		Preconditions.checkState(res, "保存失败");
		SpringUtil.publishEvent(new ContractPorEvent(contractEntity));
		return true;
	}

	//生成归档编号
	@Override
	public int getNextArchiveNo() {
		QueryWrapper<ContractEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.select("max(archive_no) as archive_no");
		ContractEntity contractEntity = this.getOne(queryWrapper);
		if (contractEntity == null) {
			return 1;
		} else {
			Integer maxArchiveNo = contractEntity.getArchiveNo();
			return maxArchiveNo + 1;
		}
	}

	/**
	 * 生成归档编号
	 */
	@Override
	public String getNextArchiveSerialNo(Long contractId) {
		QueryWrapper<ContractEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(ContractEntity::getId, contractId);
		ContractEntity contractEntity = this.getOne(queryWrapper);
//		Date currentDate = new Date();
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
//		String formattedDate = sdf.format(currentDate);
		String archiveNoStr = String.format("%06d", getNextArchiveNo());
		String archiveSerialNo = "";
		if (contractEntity.getBrand().equals(ContractEntity.BRAND_LN)) {
			archiveSerialNo = "LN" + archiveNoStr;
//			archiveSerialNo = "LN" + formattedDate + archiveNoStr;
		} else if (contractEntity.getBrand().equals(ContractEntity.BRAND_ZHJ)) {
			archiveSerialNo = "ZHJ" + archiveNoStr;
//			archiveSerialNo = "ZHJ" + formattedDate + archiveNoStr;
		} else if (contractEntity.getBrand().equals(ContractEntity.BRAND_YY)) {
			archiveSerialNo = "YY" + archiveNoStr;
//			archiveSerialNo = "YY" + formattedDate + archiveNoStr;
		}
		return archiveSerialNo;
	}

	@Override
	public boolean archiveStart(ContractArchiveDTO contractArchiveDTO, String processDefKey) {
		Map<String, Object> map = formService.getFormByProcessDefKey(processDefKey);
		String processDefId = ((WfProcessDef) map.get("process")).getId();
		Map<String, Object> body = JSON.parseObject(JSONObject.toJSONString(contractArchiveDTO),
			new TypeReference<Map<String, Object>>() {
			});
		String res = processService.startProcessInstanceById(processDefId, body,
			AuthUtil.getUserId().toString());
		return true;
	}

	@Override
	public String getTaskIdByProcessInsId(String processInsId) {
		return wfProcessInfoMapper.selectTaskIdProcessInsId(processInsId);
	}

	/**
	 * @param contract      合同
	 * @param processDefKey
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean apply(ContractVO contract, String processDefKey) {
		Preconditions.checkNotNull(contract, "合同不存在");
		Map<String, Object> map = formService.getFormByProcessDefKey(processDefKey);
		String processDefId = ((WfProcessDef) map.get("process")).getId();
		Map<String, Object> body = JSON.parseObject(JSONObject.toJSONString(contract),
			new TypeReference<Map<String, Object>>() {
			});
		String res = processService.startProcessInstanceById(processDefId, body);
		return true;
	}

	/**
	 * @param userId
	 * @return
	 */
	@Override
	public String genSerialNo(Long userId) {
		SerialNo serialNo = serialNoService.getByCode(CONTRACT_POR_MODULE);
		String prefix = serialNoService.buildPrefix(serialNo);
		//采购的序号做排序，其他的按照旧的规则
		if (StringUtils.isBlank(prefix) || !prefix.startsWith("H")) {
			return serialNoService.gen(CONTRACT_POR_MODULE);
		}
		//获取当月的所有编号
		String month = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
		List<ContractEntity> serialNos = this.list(
			Wrappers.<ContractEntity>lambdaQuery().like(ContractEntity::getSerialNo, month)
				.select(ContractEntity::getSerialNo));
		// 提取后三位并找出最大值
		OptionalInt maxSuffix = serialNos.stream()
			.map(ContractEntity::getSerialNo)
			.filter(no -> no.startsWith("H")) // 获取 serial_no 字符串
			.filter(sn -> sn.length() >= 3) // 确保长度足够截取后三位
			.map(sn -> sn.substring(sn.length() - 3)) // 截取后三位
			.filter(this::isNumeric) // <<<<<<<<<<<<<<<<< 新增过滤逻辑
			.mapToInt(Integer::parseInt) // 转换为整数
			.max(); // 找出最大值
		// 计算下一个序号并格式化为三位数（补零）
		int nextNum = maxSuffix.isPresent() ? maxSuffix.getAsInt() + 1 : 1;
		String suffix = String.format("%03d", nextNum);
		// 组装最终的 serial_no
		return prefix + month + suffix;
	}

	// 判断字符串是否为纯数字
	private boolean isNumeric(String str) {
		return str != null && str.matches("\\d{3}"); // 严格匹配3位数字
	}

}
