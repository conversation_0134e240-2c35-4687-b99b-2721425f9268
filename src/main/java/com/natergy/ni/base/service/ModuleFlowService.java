package com.natergy.ni.base.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.natergy.ni.base.entity.ModuleFlow;
import com.natergy.ni.base.mapper.ModuleFlowMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0

 * @since 2023/1/7 14:44
 */
@Service
public class ModuleFlowService extends ServiceImpl<ModuleFlowMapper, ModuleFlow> {

  public ModuleFlow getByModule(String module) {
    QueryWrapper<ModuleFlow> wrapper = new QueryWrapper<>();
    wrapper.lambda().eq(ModuleFlow::getModule, module);
    List<ModuleFlow> list = list(wrapper);
    if (list != null && !list.isEmpty()) {
      return list.get(0);
    }
    return null;
  }


  public boolean saveOrUpdate1(ModuleFlow moduleFlow) {
    if (moduleFlow.getId() != null && moduleFlow.getId() > 0) {
      return this.updateById(moduleFlow);
    } else {
      QueryWrapper<ModuleFlow> wrapper = new QueryWrapper<>();
      wrapper.lambda().eq(ModuleFlow::getModule, moduleFlow.getModule());
      long count = count(wrapper);
      if (count > 0) {
        return update(moduleFlow, wrapper);
      } else {
        return save(moduleFlow);
      }
    }
  }
}
