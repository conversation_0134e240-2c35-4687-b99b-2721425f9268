/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service;

import com.natergy.ni.base.entity.SupplierInfo;
import com.natergy.ni.base.excel.SupplierAccountExcel;
import com.natergy.ni.base.excel.SupplierExcel;
import com.natergy.ni.base.vo.DeptGrantParam;
import com.natergy.ni.base.vo.SupplierInfoVO;
import com.natergy.ni.base.vo.UserGrantVO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.support.Query;
import org.springblade.modules.system.vo.UserVO;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
public interface ISupplierInfoService extends BaseService<SupplierInfo> {

  /**
   * 自定义分页
   *
   * @param page
   * @param supplierInfo
   * @return
   */
  IPage<SupplierInfoVO> selectSupplierInfoPage(IPage<SupplierInfoVO> page,
      SupplierInfoVO supplierInfo);

  boolean saveOrUpdate(SupplierInfoVO supplierInfo);

  boolean removeByIds(List<Long> ids);

  List<SupplierInfoVO> logisticsList(String query);

  boolean submit(List<Long> ids);

  boolean back(List<Long> ids);

  boolean toVoid(List<Long> ids);

  boolean updateBlacklistById(Long id, Boolean blacklist);

  boolean updateAclById(Long id, String acl);

  boolean grant(DeptGrantParam grantVO);

  /**
   * 期初金额
   *
   * @param startDate
   * @param endDate
   * @return
   */
  BigDecimal bopTotal(LocalDate startDate);

  List<Map<String, Object>> sync(String id, boolean isCovered);

  void yonyouSync();

  void importSupplier(List<SupplierExcel> data, Boolean isCovered);

  void importSupplierAccount(List<SupplierAccountExcel> data, Boolean isCovered);

  DeptGrantParam getDeptGrant(Long id);
}
