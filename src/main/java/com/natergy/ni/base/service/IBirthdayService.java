package com.natergy.ni.base.service;


import com.natergy.ni.base.entity.BirthdayEntity;
import com.natergy.ni.base.vo.UserBirthdayVO;
import org.springblade.core.mp.base.BaseService;
import java.time.LocalDate;
import java.util.List;

public interface IBirthdayService extends BaseService<BirthdayEntity> {
	List<BirthdayEntity> findBirthdays(LocalDate startDate, LocalDate endDate);

	BirthdayEntity saveBirthday(BirthdayEntity birthday);

	List<BirthdayEntity> loadWishes(Long receiverId);

	List<UserBirthdayVO> getBirthdayRemindUser();


}
