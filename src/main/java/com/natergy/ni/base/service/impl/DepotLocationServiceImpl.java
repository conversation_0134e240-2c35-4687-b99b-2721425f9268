/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.entity.DepotLocation;
import com.natergy.ni.base.excel.DepotLocationExcel;
import com.natergy.ni.base.mapper.DepotLocationMapper;
import com.natergy.ni.base.service.IDepotLocationService;
import com.natergy.ni.base.vo.DepotLocationCascaderVO;
import com.natergy.ni.base.vo.DepotLocationVO;
import java.util.List;
import java.util.stream.Collectors;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Service
public class DepotLocationServiceImpl extends
	BaseServiceImpl<DepotLocationMapper, DepotLocation> implements IDepotLocationService {

	@Override
	public IPage<DepotLocationVO> selectDepotLocationPage(IPage<DepotLocationVO> page,
		DepotLocationVO depotLocation) {
		return page.setRecords(baseMapper.selectDepotLocationPage(page, depotLocation));
	}

	/**
	 * @return
	 */
	@Override
	public List<DepotLocationCascaderVO> cascaderList() {
		return null;
	}

	/**
	 * @param data
	 * @param depotId
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void importLocation(List<DepotLocationExcel> data, Long depotId) {
		if (depotId == null) {
			throw new ServiceException("仓库未设置");
		}
		List<DepotLocation> depotLocations = data.parallelStream().map(item -> {
			DepotLocation depotLocation = new DepotLocation();
			depotLocation.setInfoId(depotId);
			depotLocation.setName(item.getLocationName().trim());
			depotLocation.setCode(item.getLocationCode());
			depotLocation.setRemark(item.getRemark());
			return depotLocation;
		}).collect(Collectors.toList());
		saveBatch(depotLocations);
	}

}
