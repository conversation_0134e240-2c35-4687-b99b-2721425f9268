/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service;

import com.natergy.ni.base.entity.MaterialType;
import com.natergy.ni.base.excel.MaterialTypeExcel;
import com.natergy.ni.base.vo.MaterialTypeVO;
import java.util.List;
import java.util.Map;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface IMaterialTypeService extends BaseService<MaterialType> {

  /**
   * 自定义分页
   *
   * @param page
   * @param materialType
   * @return
   */
  IPage<MaterialTypeVO> selectMaterialTypePage(IPage<MaterialTypeVO> page,
      MaterialTypeVO materialType);

  /**
   * 返回类目tree
   *
   * @param type
   * @return
   */
  List<MaterialTypeVO> tree(MaterialType type);

  /**
   * 保存
   *
   * @param materialType
   * @return
   */
  boolean submit(MaterialType materialType);

  /**
   * 查询子类目列表
   *
   * @param typeId 类目id
   * @return
   */
  List<MaterialType> getMaterialTypeChild(Long typeId, boolean withRoot);

  List<Long> getMaterialTypeChildIds(Long typeId, boolean withRoot);

  /**
   * 同步用友
   *
   * @param id
   * @return
   */
  List<Map<String, Object>> sync(Long id, Boolean isCovered);

  void yonyouSync();

  void importMaterialType(List<MaterialTypeExcel> data, Boolean isCovered);

  List<MaterialTypeExcel> exportMaterialType(MaterialTypeVO materialType);

  List<MaterialTypeVO> lazyList(Long parentId, MaterialTypeVO materialType);

  MaterialTypeVO getByCode(String code);

  List<MaterialType> listByParentId(Long parentId);
}
