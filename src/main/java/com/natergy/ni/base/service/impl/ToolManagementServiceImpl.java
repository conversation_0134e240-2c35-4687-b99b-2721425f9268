
package com.natergy.ni.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.natergy.ni.base.entity.ToolManagementEntity;
import com.natergy.ni.base.excel.ToolManagementExcel;
import com.natergy.ni.base.vo.ToolManagementVO;
import com.natergy.ni.base.mapper.ToolManagementMapper;
import com.natergy.ni.base.service.IToolManagementService;
import io.jsonwebtoken.lang.Assert;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 基本信息-工具管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Service
@RequiredArgsConstructor
public class ToolManagementServiceImpl extends BaseServiceImpl<ToolManagementMapper, ToolManagementEntity> implements IToolManagementService {

	private final IUserService userService;
	@Override
	public IPage<ToolManagementVO> selectBaseToolPage(IPage<ToolManagementVO> page, ToolManagementVO toolManagementVO) {
		return page.setRecords(baseMapper.selectBaseToolPage(page, toolManagementVO));
	}

	/**
	 * 提交
	 * @param toolManagementVO
	 * @return
	 */
	@Override
	public ToolManagementVO handlePageRes(ToolManagementVO toolManagementVO){
		if (toolManagementVO.getKeeperId() != null) {
			User userInfo = UserCache.getUser(toolManagementVO.getKeeperId());
			String deptName = SysCache.getDeptName(Long.valueOf(userInfo.getDeptId()));
			toolManagementVO.setKeeperDeptName(deptName);
		}
		return toolManagementVO;
	}

	@Override
	public User selectUserInfo(Long userId) {
		return baseMapper.selectUserInfo(userId);
	}


	/**
	 * 数据导入
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void importTool(List<ToolManagementExcel> data){
		List<ToolManagementExcel> res = data;
		//第一个数据行
		int index = 2;
		for (ToolManagementExcel item : res) {
			LambdaQueryWrapper<User> userLambdaQueryWrapper = Wrappers.lambdaQuery(User.class);
			ToolManagementEntity toolManagementEntity = new ToolManagementEntity();
			//存货编码
			if (item.getMaterialCode() != null) {
				toolManagementEntity.setMaterialCode(item.getMaterialCode());
			}
			//物品名称
			if (item.getMaterialName() != null) {
				toolManagementEntity.setMaterialName(item.getMaterialName());
			} else {
				Assert.isTrue(false,"数据表第"+ index +"行物品名称为空，请核对后提交");
			}
			//规格
			if (item.getSpecification() != null) {
				toolManagementEntity.setSpecification(item.getSpecification());
			}
			//数量
			if (item.getNum() != null) {
				toolManagementEntity.setNum(item.getNum());
			} else {
				Assert.isTrue(false,"数据表第"+ index +"行数量为空，请核对后提交");
			}
			//TODO 单位要改成字符型的，目前是按照整型存储
			//单位
			if (item.getUnit() != null) {
				toolManagementEntity.setUnit(getToolUnit(item.getUnit()));
			} else {
				Assert.isTrue(false,"数据表第"+ index +"行单位为空，请核对后提交");
			}
			//是否上交
			if (item.getSubmit() != null) {
				if (item.getSubmit().equals("未上交")) {
					toolManagementEntity.setSubmit(0);
				} else if (item.getSubmit().equals("已上交")) {
					toolManagementEntity.setSubmit(1);
				}
			} else {
				Assert.isTrue(false,"数据表第"+ index +"行是否上交为空，请核对后提交");
			}
			//上交日期
			if (item.getSubmitDate() != null) {
				toolManagementEntity.setSubmitDate(item.getSubmitDate());
			}
			//物品状态
			if (item.getItemStatus() != null) {
				toolManagementEntity.setItemStatus(getItemStatus(item.getItemStatus()));
			} else {
				Assert.isTrue(false,"数据表第"+ index +"行物品状态为空，请核对后提交");
			}
			//领用人
			if (StringUtils.isNotBlank(item.getKeeperName())) {
				userLambdaQueryWrapper.select(User::getId,User::getDeptId)
					.eq(User::getName,item.getKeeperName());
				User user = userService.getOne(userLambdaQueryWrapper);
				if (user != null) {
					toolManagementEntity.setKeeperId(user.getId());
					toolManagementEntity.setKeeperDeptId(Long.valueOf(user.getDeptId()));
				} else Assert.isTrue(false,"数据表第"+ index +"行领用人姓名有误，请核对后提交");
			} else Assert.isTrue(false,"数据表第"+ index +"行领用人姓名为空，请核对后提交");
			//领用日期
			if (item.getLendDate() != null) {
				toolManagementEntity.setLendDate(item.getLendDate());
			}
			//厂区
			if (item.getFactory() != null) {
				toolManagementEntity.setFactory(getFactory(item.getFactory()));
			}
			//使用对象
			if (item.getUseObject() != null) {
				if (item.getUseObject().equals("个人")) {
					toolManagementEntity.setSubmit(1);
				} else if (item.getSubmit().equals("公用")) {
					toolManagementEntity.setSubmit(2);
				}
			} else {
				Assert.isTrue(false,"数据表第"+ index +"行使用对象为空，请核对后提交");
			}
			//备注
			if (item.getRemark() != null) {
				toolManagementEntity.setRemark(item.getRemark());
			}
			index += 1;
			boolean datas = this.save(toolManagementEntity);
			Preconditions.checkState(datas, "保存失败");
		}
	}

	//单位的数据转化
	private static final String UNIT_CODE = "ni_material_unit";
	public int getToolUnit(String unit) {
		if (StringUtils.isBlank(unit)) {
			return -1;
		}
		for (int i = 1; i <= DictBizCache.getList(UNIT_CODE).size(); i++) {
			String dictValue = DictBizCache.getValue(UNIT_CODE, i);
			if (unit.trim().equalsIgnoreCase(dictValue)) {
				return i;
			}
		}
		return -1;
	}

	private static final String TOOL_STATUS_CODE = "ni_base_tool_status";
	public int getItemStatus(String unit) {
		if (StringUtils.isBlank(unit)) {
			return -1;
		}
		for (int i = 1; i <= DictBizCache.getList(TOOL_STATUS_CODE).size(); i++) {
			String dictValue = DictBizCache.getValue(TOOL_STATUS_CODE, i);
			if (unit.trim().equalsIgnoreCase(dictValue)) {
				return i;
			}
		}
		return -1;
	}

	private static final String TOOL_FACTORY_CODE = "ni_base_tool_factory";
	public int getFactory(String factory) {
		if (StringUtils.isBlank(factory)) {
			return -1;
		}
		for (int i = 1; i <= DictBizCache.getList(TOOL_FACTORY_CODE).size(); i++) {
			String dictValue = DictBizCache.getValue(TOOL_FACTORY_CODE, i);
			if (factory.trim().equalsIgnoreCase(dictValue)) {
				return i;
			}
		}
		return -1;
	}

}
