/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service;

import com.natergy.ni.base.dto.DepotLocationDTO;
import com.natergy.ni.base.entity.DepotInfo;
import com.natergy.ni.base.entity.DepotLocation;
import com.natergy.ni.base.vo.DepotInfoVO;
import java.util.List;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface IDepotInfoService extends BaseService<DepotInfo> {

  boolean submit(List<Long> ids);

  boolean back(List<Long> ids);

  boolean toVoid(List<Long> ids);

  List<DepotInfo> listOrderByDistance(DepotInfo depotInfo);

  IPage<DepotLocationDTO> depotLocationPage(IPage<DepotLocationDTO> page, Long depotId, String depotLocation);
}
