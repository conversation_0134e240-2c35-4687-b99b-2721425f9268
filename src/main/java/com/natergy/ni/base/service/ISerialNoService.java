/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service;

import com.natergy.ni.base.entity.SerialNo;
import com.natergy.ni.base.vo.SerialNoVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface ISerialNoService extends BaseService<SerialNo> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param serialNo
	 * @return
	 */
	IPage<SerialNoVO> selectSerialNoPage(IPage<SerialNoVO> page, SerialNoVO serialNo);

	/**
	 * 生成顺序号
	 * @param no
	 * @return
	 */
	String gen(SerialNo no);

	String gen(String module);

    SerialNo getByCode(String module);

	String buildPrefix(SerialNo no);
}
