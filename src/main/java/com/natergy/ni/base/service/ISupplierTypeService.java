/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.entity.SupplierType;
import com.natergy.ni.base.vo.SupplierTypeVO;
import java.util.List;
import java.util.Map;
import org.springblade.core.mp.base.BaseService;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface ISupplierTypeService extends BaseService<SupplierType> {

  /**
   * 自定义分页
   *
   * @param page
   * @param supplierType
   * @return
   */
  IPage<SupplierTypeVO> selectSupplierTypePage(IPage<SupplierTypeVO> page,
      SupplierTypeVO supplierType);

  /**
   * 返回类目tree
   *
   * @param type
   * @return
   */
  List<SupplierTypeVO> tree(SupplierType type);

  /**
   * 保存
   *
   * @param supplierType
   * @return
   */
  boolean submit(SupplierType supplierType);

  /**
   * 查询子类目列表
   *
   * @param typeId 类目id
   * @return
   */
  List<SupplierType> getSupplierTypeChild(Long typeId, boolean withRoot);

  List<Long> getSupplierTypeChildIds(Long typeId, boolean withRoot);

  List<Map<String, Object>> sync(String id, boolean isCovered);

  void yonyouSync();
}
