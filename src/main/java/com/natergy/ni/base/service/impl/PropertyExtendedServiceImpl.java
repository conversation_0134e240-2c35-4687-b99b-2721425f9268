/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.natergy.ni.base.entity.PropertyExtendedEntity;
import com.natergy.ni.base.entity.PropertyOptionEntity;
import com.natergy.ni.base.vo.PropertyExtendedVO;
import com.natergy.ni.base.mapper.PropertyExtendedMapper;
import com.natergy.ni.base.service.IPropertyExtendedService;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 动态属性扩展属性 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class PropertyExtendedServiceImpl extends BaseServiceImpl<PropertyExtendedMapper, PropertyExtendedEntity> implements IPropertyExtendedService {

	private void check(PropertyExtendedEntity entity) {

		entity.setTenantId(BladeConstant.ADMIN_TENANT_ID);

		LambdaQueryWrapper<PropertyExtendedEntity> queryWrapper = new LambdaQueryWrapper<PropertyExtendedEntity>()
			.ne(entity.getId() != null, PropertyExtendedEntity::getId, entity.getId())
			.eq(PropertyExtendedEntity::getExtendedPropertyName, entity.getExtendedPropertyName());

		PropertyExtendedEntity one = getOne(queryWrapper);

		if (one != null) {
			throw new ServiceException("扩展属性已存在");
		}
	}

	@Override
	public boolean save(PropertyExtendedEntity entity) {

		check(entity);

		return super.save(entity);
	}

	@Override
	public boolean updateById(PropertyExtendedEntity entity) {

		check(entity);

		return super.updateById(entity);
	}

	@Override
	public boolean saveOrUpdate(PropertyExtendedEntity entity) {

		check(entity);

		return super.saveOrUpdate(entity);
	}

	@Override
	public IPage<PropertyExtendedVO> selectPropertyExtendedPage(IPage<PropertyExtendedVO> page, PropertyExtendedVO propertyExtended) {
		return page.setRecords(baseMapper.selectPropertyExtendedPage(page, propertyExtended));
	}


}
