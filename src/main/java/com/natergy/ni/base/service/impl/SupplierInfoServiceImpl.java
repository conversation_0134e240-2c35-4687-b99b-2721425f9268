/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.natergy.ni.base.entity.SupplierContact;
import com.natergy.ni.base.entity.SupplierDept;
import com.natergy.ni.base.entity.SupplierInfo;
import com.natergy.ni.base.entity.SupplierType;
import com.natergy.ni.base.entity.SupplierUser;
import com.natergy.ni.base.excel.SupplierAccountExcel;
import com.natergy.ni.base.excel.SupplierExcel;
import com.natergy.ni.base.mapper.SupplierInfoMapper;
import com.natergy.ni.base.service.ISupplierContactService;
import com.natergy.ni.base.service.ISupplierInfoService;
import com.natergy.ni.base.service.ISupplierTypeService;
import com.natergy.ni.base.service.SupplierDeptService;
import com.natergy.ni.base.vo.DeptGrantParam;
import com.natergy.ni.base.vo.SupplierInfoVO;
import com.natergy.ni.common.constant.NiConstant;
import com.natergy.ni.sync.yonyou.dto.BasicVendorDto;
import com.natergy.ni.sync.yonyou.dto.ResDto;
import com.natergy.ni.sync.yonyou.service.BasicService;
import io.jsonwebtoken.lang.Assert;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.cache.ParamCache;
import org.springblade.common.enums.DictBizEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.DictBiz;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.vo.UserVO;
import org.springblade.modules.system.wrapper.UserWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierInfoServiceImpl extends
    BaseServiceImpl<SupplierInfoMapper, SupplierInfo> implements ISupplierInfoService {

  private final SupplierDeptService supplierDeptService;
  private final IDeptService deptService;


  private final BasicService basicService;

  private final ISupplierTypeService supplierTypeService;

  private final ISupplierContactService supplierContactService;

  @Override
  public IPage<SupplierInfoVO> selectSupplierInfoPage(IPage<SupplierInfoVO> page,
      SupplierInfoVO supplierInfo) {
	  Page<SupplierInfoVO> page1 = (Page<SupplierInfoVO>) page;
	  page1.setMaxLimit(-1L);
    return page.setRecords(baseMapper.selectSupplierInfoPage(page, supplierInfo));
  }

  /**
   * 修改-联系人如需删除，需要前端将要删除的数据状态改为已删除
   *
   * @param supplierInfo
   * @return
   */
  @Transactional(rollbackFor = Exception.class)
  @Override
  public boolean saveOrUpdate(SupplierInfoVO supplierInfo) {
    List<SupplierContact> contacts = supplierInfo.getContacts();
    boolean res = saveOrUpdate((SupplierInfo) supplierInfo);
    Preconditions.checkArgument(res, "保存失败！");
    supplierContactService.remove(Wrappers.<SupplierContact>lambdaQuery()
        .eq(SupplierContact::getSupplierId, supplierInfo.getId()));
    contacts.forEach(contact -> {
      contact.setId(null);
      contact.setSupplierId(supplierInfo.getId());
    });
    res = supplierContactService.saveOrUpdateBatch(contacts);
    Preconditions.checkArgument(res, "保存联系人失败！");
    return true;
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public boolean removeByIds(List<Long> ids) {
    QueryWrapper<SupplierContact> wrapper = new QueryWrapper<>();
    wrapper.lambda()
        .in(SupplierContact::getSupplierId, ids);
    supplierContactService.remove(wrapper);
    boolean res = deleteLogic(ids);
    Preconditions.checkArgument(res, "删除失败！");
    return true;
  }

  @Override
  public List<SupplierInfoVO> logisticsList(String query) {
    return baseMapper.logisticsList(query);
  }

  @Override
  public boolean submit(List<Long> ids) {
    UpdateWrapper<SupplierInfo> wrapper = new UpdateWrapper<>();
    wrapper.lambda().in(SupplierInfo::getStatus, NiConstant.STATUS_DRAFT,
            NiConstant.STATUS_BACK)
        .in(SupplierInfo::getId, ids)
        .set(SupplierInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .set(SupplierInfo::getApplyTime, new Date());
    return update(wrapper);
  }

  @Override
  public boolean back(List<Long> ids) {
    QueryWrapper<SupplierInfo> w = new QueryWrapper<>();
    w.lambda().eq(SupplierInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .in(SupplierInfo::getId, ids);
    long num = count(w);
    Assert.isTrue(ids.size() <= num, "数据状态错误");
    UpdateWrapper<SupplierInfo> wrapper = new UpdateWrapper<>();
    wrapper.lambda().eq(SupplierInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .in(SupplierInfo::getId, ids)
        .set(SupplierInfo::getStatus, NiConstant.STATUS_BACK);
    return update(wrapper);
  }

  @Override
  public boolean toVoid(List<Long> ids) {
    QueryWrapper<SupplierInfo> w = new QueryWrapper<>();
    w.lambda().eq(SupplierInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .in(SupplierInfo::getId, ids);
    long num = count(w);
    Assert.isTrue(ids.size() <= num, "数据状态错误");
    UpdateWrapper<SupplierInfo> wrapper = new UpdateWrapper<>();
    wrapper.lambda().eq(SupplierInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .in(SupplierInfo::getId, ids)
        .set(SupplierInfo::getStatus, NiConstant.STATUS_TO_VOID);
    return update(wrapper);
  }

  @Override
  public boolean updateBlacklistById(Long id, Boolean blacklist) {
    UpdateWrapper<SupplierInfo> wrapper = new UpdateWrapper<>();
    wrapper.lambda().eq(SupplierInfo::getId, id)
        .set(SupplierInfo::getBlacklist, blacklist);
    return update(wrapper);
  }

  @Override
  public boolean updateAclById(Long id, String acl) {
    UpdateWrapper<SupplierInfo> wrapper = new UpdateWrapper<>();
    wrapper.lambda().eq(SupplierInfo::getId, id)
        .set(SupplierInfo::getAcl, acl);
    return update(wrapper);
  }


  @Transactional(rollbackFor = Exception.class)
  @Override
  public boolean grant(DeptGrantParam grantVO) {
    // 删除角色配置的菜单集合
    supplierDeptService.remove(Wrappers.<SupplierDept>lambdaQuery()
        .in(SupplierDept::getSupplierId, Func.toLongList(grantVO.getSupplierIds())));
    // 组装配置
    List<SupplierDept> supplierDepts = new ArrayList<>();
    Func.toLongList(grantVO.getSupplierIds())
        .forEach(supplierId -> grantVO.getDeptId().forEach(deptId -> {
          SupplierDept supplierDept = new SupplierDept();
          supplierDept.setSupplierId(supplierId);
          supplierDept.setDeptId(Func.toLong(deptId));
          supplierDepts.add(supplierDept);
        }));
    // 新增配置
    boolean res = supplierDeptService.saveBatch(supplierDepts);
    Preconditions.checkArgument(res, "权限设置错误");
    this.update(Wrappers.<SupplierInfo>lambdaUpdate()
        .in(SupplierInfo::getId, Func.toLongList(grantVO.getSupplierIds()))
        .set(SupplierInfo::getAcl, SupplierInfo.ACL_PRIVATE));
    Preconditions.checkArgument(res, "权限设置错误");
    return true;
  }


  @Override
  public BigDecimal bopTotal(LocalDate startDate) {
    //TODO 默认币种是rmb
    return baseMapper.bopTotal(startDate);
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public List<Map<String, Object>> sync(String id, boolean isCovered) {
    SupplierInfo supplier = getById(id);
    return sync(supplier, isCovered);
  }

  @Transactional(rollbackFor = Exception.class)
  public List<Map<String, Object>> sync(SupplierInfo supplier, boolean isCovered) {
    List<Map<String, Object>> sync = supplier.getYonyouSync();
    String sequences = ParamCache.getValue(NiConstant.PARAM_YONYOU_SYNC_SEQUENCE);
    List<String> sequenceList = Arrays.asList(sequences.split(","));
    List<Map<String, Object>> edSync = new ArrayList<>();
    if (sync != null) {
      edSync = sync.stream()
          .filter(s -> s.get("value").equals(NiConstant.YONYOU_SYNC_STATE_ED))
          .collect(Collectors.toList());
    }
    List<Map<String, Object>> res = new ArrayList<>(sequenceList.size());
    supplier.setYonyouSync(res);
    if (!isCovered && sync != null && !sync.isEmpty()) {
      res.addAll(edSync);
      Set<String> ss = edSync.stream().map(s -> (String) s.get("sequence"))
          .collect(Collectors.toSet());
      sequenceList = sequenceList.stream().filter(sequence -> !ss.contains(sequence)).collect(
          Collectors.toList());
    }
    BasicVendorDto dto = new BasicVendorDto(supplier);
    if (StringUtils.isBlank(dto.getVendorClassCode())) {
      throw new ServiceException("供应商分类未设置");
    }
    sequenceList.forEach(sequence -> {
      ResDto r = basicService.vendorSync(dto, sequence);
      if (r != null) {
        res.add(ImmutableMap.<String, Object>builder()
            .put("sequence", sequence)
            .put("value",
                r.getErrorCode() != null && "0".equals(r.getErrorCode())
                    ? NiConstant.YONYOU_SYNC_STATE_ED
                    : NiConstant.YONYOU_SYNC_STATE_FAIL)
            .put("errMsg", r.getErrorMessage() != null ? r.getErrorMessage() : "")
            .put("id", r.getId() != null ? r.getId() : "")
            .put("date", DateUtil.formatDateTime(new Date()))
            .build());
        if (StringUtils.isBlank(supplier.getCode())) {
          dto.setCode(r.getId());
          supplier.setCode(r.getId());
        }
      }
    });
    supplier.setSync(true);
    this.updateById(supplier);
    return res;
  }

  private String getBasicCode(SupplierType type) {
    if (type == null) {
      return null;
    }
    if (type.getParentId() == 0) {
      return type.getCode();
    }
    if (StringUtils.isNotBlank(type.getAncestors())) {
      List<Long> parentIds = Func.toLongList(type.getAncestors());
      List<SupplierType> parents = supplierTypeService.listByIds(parentIds);
      return parents.stream().filter(t -> t.getParentId() == 0).map(SupplierType::getCode)
          .findFirst().orElse(null);
    }
    return null;
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void yonyouSync() {
    log.info("供应商同步用友");
    String sequences = ParamCache.getValue(NiConstant.PARAM_YONYOU_SYNC_SEQUENCE);
    List<String> sequenceList = Func.toStrList(sequences);
    List<SupplierInfo> supplierInfos = getUnSyncList(sequenceList);
    if (supplierInfos == null || supplierInfos.isEmpty()) {
      log.info("未查询到未同步的数据，跳过该次同步");
      return;
    }
    supplierInfos.forEach(supplierInfo -> {
      try {
        this.sync(supplierInfo, false);
      } catch (Exception e) {
        log.error("同步失败", e);
      }
    });
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void importSupplier(List<SupplierExcel> data, Boolean isCovered) {
    List<SupplierType> typeList = supplierTypeService.list();
    List<DictBiz> levelList = DictBizCache.getList(DictBizEnum.NI_SUPPLIER_LEVEL.getName());
    Map<String, Long> typeNameMap = typeList.stream().filter(type -> !"其他".equals(
            type.getName()))
        .collect(Collectors.toMap(SupplierType::getName, SupplierType::getId));
    Map<String, Long> typeMap = typeList.stream()
        .collect(Collectors.toMap(SupplierType::getCode, SupplierType::getId));
    Map<String, String> levelMap = levelList.stream()
        .collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey));
    List<Dept> deptList = deptService.getAllDeptList();
    Map<String, Long> deptMap = deptList.stream()
        .collect(Collectors.toMap(Dept::getDeptName, Dept::getId));
    List<SupplierInfo> list = new ArrayList<>();
    data.stream().filter(excel -> StringUtils.isNotBlank(excel.getName())).forEach(excel -> {
      SupplierInfo entity = BeanUtil.copy(excel, SupplierInfo.class);
      if (entity != null) {
        entity.setTypeId(buildTypeId(excel, typeMap, typeNameMap));
        entity.setLevel(levelMap.get(excel.getLevel()));
        entity.setApplyTime(new Date());
        entity.setAcl(SupplierInfo.ACL_PUBLIC);
        entity.setStatus(NiConstant.STATUS_SUBMIT);
        entity.setHonestStatus(StringUtils.isNotBlank(excel.getHonestNo()));
        entity.setTags(Joiner.on(",")
            .join(StringUtils.isNotBlank(excel.getType()) ? excel.getType().trim() : "",
                StringUtils.isNotBlank(excel.getTag()) ? excel.getTag().trim() : ""));
        entity.setContacts(buildContacts(excel));
        entity.setDepts(buildDepts(excel, deptMap));
        list.add(entity);

      }
    });
    if (list.isEmpty()) {
      return;
    }
    boolean res;
    if (isCovered) {
      res = this.saveOrUpdateBatch(list, 500);
    } else {
      res = this.saveBatch(list, 500);
    }
    Preconditions.checkState(res, "导入失败");
    List<SupplierContact> contactList = list.stream()
        .filter(item -> item.getContacts() != null && !item.getContacts().isEmpty())
        .flatMap(item -> item.getContacts().stream()
            .peek(contact -> contact.setSupplierId(item.getId()))).collect(
            Collectors.toList());
    res = supplierContactService.saveBatch(contactList);
    Preconditions.checkState(res, "联系人导入失败");
    List<SupplierDept> depts =
        list.stream().filter(item -> item.getDepts() != null && !item.getDepts().isEmpty())
            .flatMap(
                item -> item.getDepts().stream().peek(dept -> dept.setSupplierId(item.getId())))
            .collect(Collectors.toList());
    res = supplierDeptService.saveBatch(depts);
    Preconditions.checkState(res, "权限导入失败");
  }

  private List<SupplierDept> buildDepts(SupplierExcel excel, Map<String, Long> deptMap) {
    if (StringUtils.isNotBlank(excel.getDept())) {
      List<String> deptStrs = Func.toStrList(excel.getDept());
      return deptStrs.stream().filter(deptStr -> deptMap.get(deptStr) != null)
          .map(deptStr -> {
            SupplierDept dept = new SupplierDept();
            dept.setDeptId(deptMap.get(deptStr));
            return dept;
          }).collect(Collectors.toList());
    }
    return null;
  }

  private List<SupplierContact> buildContacts(SupplierExcel excel) {
    List<SupplierContact> contacts = new ArrayList<>();
    SupplierContact contact = new SupplierContact();
    contact.setName(excel.getLinkman());
    contact.setPhone(excel.getLinkPhone());
    contacts.add(contact);
    if (StringUtils.isNotBlank(excel.getLink1()) && !excel.getLink1().trim().isEmpty()) {
      SupplierContact contact1 = new SupplierContact();
      contact1.setName(excel.getLink1());
      contact1.setPhone(excel.getLink1());
      contacts.add(contact1);
    }
    if (StringUtils.isNotBlank(excel.getLink2()) && !excel.getLink2().trim().isEmpty()) {
      SupplierContact contact2 = new SupplierContact();
      contact2.setName(excel.getLink2());
      contact2.setPhone(excel.getLink2());
      contacts.add(contact2);
    }
    return contacts;
  }

  private Long buildTypeId(SupplierExcel excel, Map<String, Long> typeMap,
      Map<String, Long> typeNameMap) {
    String type = excel.getType();
    Long typeId = null;
    if (StringUtils.isNotBlank(excel.getTypeCode())) {
      String typeCode = excel.getTypeCode();
      if (StringUtils.isNotBlank(excel.getTypeName())) {
        typeId = typeNameMap.get(excel.getTypeName().trim());
        if (typeId != null) {
          return typeId;
        }
      }
      typeId = typeMap.get(typeCode.trim());
    }
    if (typeId == null) {
      typeId = typeMap.get("99");
    }
    if (StringUtils.isBlank(type)) {
      return typeId;
    }
    type = type.trim();
    if ("pvc、胶、软管".equals(type) || "电线、信号".equals(type)) {
      //标准件类供应商
      typeId = typeMap.get("0511");
    } else if ("包装物".equals(type)) {
      typeId = typeMap.get("02");
    } else if ("备品备件".equals(type)) {
      typeId = typeMap.get("05");
    } else if ("备用钢材".equals(type) || "钢材".equals(type)) {
      typeId = typeMap.get("0501");
    } else if ("财务费用".equals(type) || "企管费用".equals(type) || "日常费用".equals(type)
        || "专项费用".equals(type)) {
      typeId = typeMap.get("99");
    } else if ("叉车配件及维修".equals(type)) {
      typeId = typeMap.get("05");
    } else if ("传动".equals(type) || "任猛".equals(type) || "运输配件".equals(type)) {
      typeId = typeMap.get("0507");
    } else if ("传感器、仪表".equals(type)) {
      typeId = typeMap.get("0506");
    } else if ("电机、减速机".equals(type) || "设备".equals(type)) {
      typeId = typeMap.get("0514");
    } else if ("电器配件".equals(type) || "电线、信号线".equals(type) || "模块、控制元件".equals(
        type)) {
      typeId = typeMap.get("0506");
    } else if ("阀门".equals(type) || "法兰、弯头、管件".equals(type) || "紧固件".equals(type)
        || "气动、液压".equals(type) || "气割、电焊".equals(type) || "五金".equals(type)) {
      typeId = typeMap.get("0505");
    } else if ("风机、泵".equals(type)) {
      typeId = typeMap.get("0513");
    } else if ("福利、劳保".equals(type)) {
      typeId = typeMap.get("0401");
    } else if ("工具、耗品".equals(type) || "工具、其它".equals(type)) {
      typeId = typeMap.get("0510");
    } else if ("固定资产".equals(type)) {
      typeId = typeMap.get("99");
    } else if ("国内销售".equals(type) || "国外销售".equals(type)) {
      typeId = typeMap.get("03");
    } else if ("耗品其它类".equals(type) || "蔬菜食品类".equals(type)) {
      typeId = typeMap.get("0404");
    } else if ("基建".equals(type)) {
      typeId = typeMap.get("0504");
    } else if ("密封件".equals(type) || "轴承".equals(type)) {
      typeId = typeMap.get("0511");
    } else if ("能源耗材".equals(type)) {
      typeId = typeMap.get("0104");
    } else if ("配件".equals(type) || "生产外协".equals(type) || "外修".equals(type)
        || "外修、外协、租用".equals(type) || "质量检测".equals(type)) {
      typeId = typeMap.get("0512");
    } else if ("销售".equals(type)) {
      typeId = typeMap.get("0301");
    } else if ("印刷品".equals(type)) {
      typeId = typeMap.get("0403");
    } else if ("用友记账用".equals(type)) {
      typeId = typeMap.get("07");
    } else if ("油、胶、液".equals(type)) {
      typeId = typeMap.get("0508");
    } else if ("原材料".equals(type)) {
      typeId = typeMap.get("01");
    } else if (typeId != null) {
      typeId = typeMap.get("99");
    }
    return typeId;
  }


  @Transactional(rollbackFor = Exception.class)
  @Override
  public void importSupplierAccount(List<SupplierAccountExcel> data, Boolean isCovered) {
    Map<String, List<SupplierAccountExcel>> excelMap = data.stream()
        .filter(item -> StringUtils.isNotBlank(item.getName()))
        .collect(Collectors.groupingBy(SupplierAccountExcel::getName));
    Map<Long, List<SupplierAccountExcel>> excelIdMap = data.stream()
        .filter(item -> item.getOldId() != null && item.getOldId() > 0)
        .collect(Collectors.groupingBy(SupplierAccountExcel::getOldId));
    String regExp = "[\n`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。， 、？]";
    List<SupplierInfo> supplierInfos = list();
    List<SupplierInfo> res = supplierInfos.stream()
        .filter(item -> excelMap.get(item.getName()) != null
            || excelIdMap.get(item.getOldId()) != null).peek(item -> {
          List<SupplierAccountExcel> excel1 = excelIdMap.get(item.getOldId());
          Map<Long, Map<String, Object>> financeMap = new HashMap<>();
          if (excel1 != null) {
            financeMap.putAll(excel1.stream().map(e -> {
              Map<String, Object> map = new HashMap<>();
              map.put("oldId", e.getOldAId());
              map.put("financeBank", e.getFinanceBank());
              map.put("financeName", e.getFinanceName());
              map.put("financeAccount", StringUtils.isNotBlank(e.getFinanceAccount()) ?
                  e.getFinanceAccount().replaceAll(regExp, "") : "");
              map.put("taxId", e.getTaxId());
              map.put("financeAddress", e.getFinanceAddress());
              map.put("financePhone", e.getFinancePhone());
              map.put("payType", e.getPayType());
              map.put("bill", e.getBill());
              map.put("remark", e.getRemark());
              return map;
            }).collect(Collectors.toMap(a -> (Long) a.get("oldId"), Function.identity())));
          }
          List<SupplierAccountExcel> excel = excelMap.get(item.getName());
          if (excel != null) {
            financeMap.putAll(excel.stream().map(e -> {
              Map<String, Object> map = new HashMap<>();
              map.put("oldId", e.getOldAId());
              map.put("financeBank", e.getFinanceBank());
              map.put("financeName", e.getFinanceName());
              map.put("financeAccount", StringUtils.isNotBlank(e.getFinanceAccount()) ?
                  e.getFinanceAccount().replaceAll(regExp, "") : "");
              map.put("taxId", e.getTaxId());
              map.put("financeAddress", e.getFinanceAddress());
              map.put("financePhone", e.getFinancePhone());
              map.put("payType", e.getPayType());
              map.put("bill", e.getBill());
              map.put("remark", e.getRemark());
              return map;
            }).collect(Collectors.toMap(a -> (Long) a.get("oldId"), Function.identity())));
          }
          item.setFinance(new ArrayList<>(financeMap.values()));
        }).collect(Collectors.toList());
    this.saveOrUpdateBatch(res);
  }

  /**
   * @param id
   * @return
   */
  @Override
  public DeptGrantParam getDeptGrant(Long id) {
    SupplierInfo supplierInfo = getById(id);
    DeptGrantParam grant = new DeptGrantParam();
    grant.setSupplierIds(id + "");
    if (supplierInfo.getAcl().equals(SupplierInfo.ACL_PUBLIC)) {
      grant.setDeptId(new ArrayList<>());
      return grant;
    }
    List<SupplierDept> supplierDepts = supplierDeptService.list(Wrappers.<SupplierDept>lambdaQuery()
        .eq(SupplierDept::getSupplierId, id));
    grant.setDeptId(supplierDepts.stream().map(SupplierDept::getDeptId).map(String::valueOf)
        .collect(Collectors.toList()));
    return grant;
  }

  private List<SupplierInfo> getUnSyncList(List<String> sequenceList) {
    return baseMapper.getUnSyncList(sequenceList);
  }

}
