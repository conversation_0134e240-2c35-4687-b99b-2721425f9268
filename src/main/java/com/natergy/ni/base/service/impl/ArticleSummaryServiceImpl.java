/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import com.natergy.ni.base.entity.ArticleSummaryEntity;
import com.natergy.ni.base.vo.ArticleSummaryVO;
import com.natergy.ni.base.mapper.ArticleSummaryMapper;
import com.natergy.ni.base.service.IArticleSummaryService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.datasource.DataSource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 行业资讯 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@DataSource("crawler")
@Service
public class ArticleSummaryServiceImpl extends BaseServiceImpl<ArticleSummaryMapper, ArticleSummaryEntity> implements IArticleSummaryService {

	@Override
	public IPage<ArticleSummaryVO> selectArticleSummaryPage(IPage<ArticleSummaryVO> page, ArticleSummaryVO articleSummary) {

		return page.setRecords(baseMapper.selectArticleSummaryPage(page,articleSummary));
	}

	@Override
	public IPage<ArticleSummaryVO> selectArticleSummaryList(IPage<ArticleSummaryVO> page, ArticleSummaryVO articleSummary) {

		return page.setRecords(baseMapper.selectArticleSummaryList(page,articleSummary));
	}
}
