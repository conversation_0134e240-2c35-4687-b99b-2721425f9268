package com.natergy.ni.base.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.natergy.ni.base.entity.SupplierUser;
import com.natergy.ni.base.mapper.SupplierUserMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0

 * @since 2022/12/26 15:44
 */
@Service
public class SupplierUserService extends ServiceImpl<SupplierUserMapper, SupplierUser> {

  public List<SupplierUser> listByUserId(Long userId) {
    return list(Wrappers.<SupplierUser>lambdaQuery().eq(SupplierUser::getUserId, userId));
  }
}
