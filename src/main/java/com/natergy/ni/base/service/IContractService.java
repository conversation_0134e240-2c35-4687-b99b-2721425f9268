/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.dto.ContractArchiveDTO;
import com.natergy.ni.base.entity.ContractEntity;
import com.natergy.ni.base.vo.ContractVO;
import com.natergy.ni.por.vo.PorOrderItemVO;
import java.util.List;
import org.springblade.core.mp.base.BaseService;

/**
 * 基本信息-合同管理 服务类
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
public interface IContractService extends BaseService<ContractEntity> {

  /**
   * 自定义分页
   *
   * @param page
   * @param baseContract
   * @return
   */
  IPage<ContractVO> selectContractPage(IPage<ContractVO> page, ContractVO baseContract);


  /**
   * 撤销
   *
   * @param ids
   * @return
   */
  boolean end(List<Long> ids);

  /**
   * 作废
   *
   * @param ids
   * @return
   */
  boolean toVoid(List<Long> ids);


  boolean finish(List<Long> toLongList);

  boolean updateStateById(Long id, String contractStateFinish);

  ContractVO detailById(Long id);

  int getNextArchiveNo();

  String getNextArchiveSerialNo(Long contractId);
  boolean archiveStart(ContractArchiveDTO contractArchiveDTO, String processDefKey);

  String getTaskIdByProcessInsId(String processInsId);


  boolean apply(ContractVO contract, String processDefKey);

    String genSerialNo(Long userId);

	List<ContractVO> lazyList(Long parentId, ContractVO params);
}
