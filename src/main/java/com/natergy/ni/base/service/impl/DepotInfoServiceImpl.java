/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.base.dto.DepotLocationDTO;
import com.natergy.ni.base.entity.DepotInfo;
import com.natergy.ni.base.entity.DepotLocation;
import com.natergy.ni.base.mapper.DepotInfoMapper;
import com.natergy.ni.base.service.IDepotInfoService;
import com.natergy.ni.common.constant.NiConstant;
import io.jsonwebtoken.lang.Assert;
import java.util.Date;
import java.util.List;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Service
public class DepotInfoServiceImpl extends BaseServiceImpl<DepotInfoMapper, DepotInfo> implements
    IDepotInfoService {


  @Override
  public boolean submit(List<Long> ids) {
    UpdateWrapper<DepotInfo> wrapper = new UpdateWrapper<>();
    wrapper.lambda().in(DepotInfo::getStatus, NiConstant.STATUS_DRAFT,
            NiConstant.STATUS_BACK)
        .in(DepotInfo::getId, ids)
        .set(DepotInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .set(DepotInfo::getApplyTime, new Date());
    return update(wrapper);
  }

  @Override
  public boolean back(List<Long> ids) {
    QueryWrapper<DepotInfo> w = new QueryWrapper<>();
    w.lambda().eq(DepotInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .in(DepotInfo::getId, ids);
    long num = count(w);
    Assert.isTrue(ids.size() <= num, "数据状态错误");
    UpdateWrapper<DepotInfo> wrapper = new UpdateWrapper<>();
    wrapper.lambda().eq(DepotInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .in(DepotInfo::getId, ids)
        .set(DepotInfo::getStatus, NiConstant.STATUS_BACK);
    return update(wrapper);
  }

  @Override
  public boolean toVoid(List<Long> ids) {
    QueryWrapper<DepotInfo> w = new QueryWrapper<>();
    w.lambda().eq(DepotInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .in(DepotInfo::getId, ids);
    long num = count(w);
    Assert.isTrue(ids.size() <= num, "数据状态错误");
    UpdateWrapper<DepotInfo> wrapper = new UpdateWrapper<>();
    wrapper.lambda().eq(DepotInfo::getStatus, NiConstant.STATUS_SUBMIT)
        .in(DepotInfo::getId, ids)
        .set(DepotInfo::getStatus, NiConstant.STATUS_TO_VOID);
    return update(wrapper);
  }

  /**
   * @param depotInfo
   * @return
   */
  @Override
  public List<DepotInfo> listOrderByDistance(DepotInfo depotInfo) {
    return baseMapper.selectListOrderByDistance(depotInfo);
  }

  /**
   * @param page
   * @param depotId
   * @param depotLocation
   * @return
   */
  @Override
  public IPage<DepotLocationDTO> depotLocationPage(IPage<DepotLocationDTO> page, Long depotId,
      String depotLocation) {
    List<DepotLocationDTO> records = baseMapper.selectDepotLocationPage(page, depotId, depotLocation);
    return page.setRecords(records);
  }

}
