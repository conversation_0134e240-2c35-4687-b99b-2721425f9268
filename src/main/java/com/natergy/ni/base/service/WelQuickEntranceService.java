package com.natergy.ni.base.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.natergy.ni.base.entity.WelQuickEntrance;
import com.natergy.ni.base.mapper.WelQuickEntranceMapper;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.plugin.workflow.design.entity.WfProcessDef;
import org.springblade.plugin.workflow.design.service.IWfDesignService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0

 * @since 2023/1/9 11:08
 */
@Service
@RequiredArgsConstructor
public class WelQuickEntranceService extends ServiceImpl<WelQuickEntranceMapper, WelQuickEntrance> {

  private final IWfDesignService wfDesignService;

  public List<WelQuickEntrance> getMyQuickEntrance() {
    BladeUser user = AuthUtil.getUser();
    QueryWrapper<WelQuickEntrance> wrapper = new QueryWrapper<>();
    wrapper.lambda().eq(WelQuickEntrance::getUserId, user.getUserId());
    List<WelQuickEntrance> res = list(wrapper);
    res.forEach(item -> {
      WfProcessDef def = wfDesignService.detail(item.getWorkflowKey());
      item.setWorkflowId(def.getId());
      item.setWorkflowName(def.getName());
    });
    return res;
  }

  @Transactional(rollbackFor = Exception.class)
  public boolean saveOrUpdate(BladeUser user, List<String> keys) {
    QueryWrapper<WelQuickEntrance> wrapper = new QueryWrapper<>();
    wrapper.lambda().eq(WelQuickEntrance::getUserId, user.getUserId());
    remove(wrapper);
    List<WelQuickEntrance> list = keys.stream().distinct().map(key -> {
      WelQuickEntrance welQuickEntrance = new WelQuickEntrance();
      welQuickEntrance.setUserId(user.getUserId());
      welQuickEntrance.setWorkflowKey(key);
      return welQuickEntrance;
    }).collect(Collectors.toList());
    return saveOrUpdateBatch(list);
  }
}
