package com.natergy.ni.base.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/3/27 9:01
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SupplierAccountExcel implements Serializable {

  private static final long serialVersionUID = -6488533692299409958L;

  @ExcelProperty(value = "供应商")
  private String name;
  @ExcelProperty(value = "开户银行")
  private String financeBank;
  @ExcelProperty(value = "收款人全称")
  private String financeName;
  @ExcelProperty(value = "银行账号")
  private String financeAccount;
  @ExcelProperty(value = "纳税人识别号")
  private String taxId;
  @ExcelProperty(value = "财务地址")
  private String financeAddress;
  @ExcelProperty(value = "财务电话")
  private String financePhone;
  @ExcelProperty("付款方式")
  private String payType;
  @ExcelProperty("发票类型")
  private String bill;
  @ExcelProperty("id")
  private Long oldId;
  @ExcelProperty(value = "供应商名称")
  private String name1;
  @ExcelProperty(value = "供应商编码")
  private String code1;
  @ExcelProperty("aid")
  private Long oldAId;
  @ExcelProperty("备注")
  private String remark;

}

