package com.natergy.ni.base.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/3/27 9:01
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SupplierExcel implements Serializable {

  private static final long serialVersionUID = -6488533692299409958L;
  @ExcelProperty(value = "id")
  private Long oldId;
  @ExcelProperty(value = "供应商名称")
  private String name;
  @ExcelProperty(value = "简称")
  private String shortName;
  @ExcelProperty(value = "供应商编码")
  private String code;
  @ExcelProperty(value = "类别")
  private String type;
  @ExcelProperty(value = "省")
  private String provinceCode;
  @ExcelProperty(value = "城市")
  private String cityCode;
  @ExcelProperty(value = "地址")
  private String address;
  @ExcelProperty(value = "法人")
  private String corporation;
  @ExcelProperty(value = "注册资本（万元）")
  private Double registeredCapital;
  @ExcelProperty(value = "统一社会信用代码")
  private String usci;
  @ExcelProperty(value = "供应商等级")
  private String level;
  @ExcelProperty(value = "主要联系人")
  private String linkman;
  @ExcelProperty(value = "联系电话")
  private String linkPhone;
  @ExcelProperty(value = "备注")
  private String remark;
  @ExcelProperty(value = "开户银行")
  private String financeBank;
  @ExcelProperty(value = "账户名称")
  private String financeName;
  @ExcelProperty(value = "银行账号")
  private String financeAccount;
  @ExcelProperty(value = "纳税人识别号")
  private String taxId;
  @ExcelProperty(value = "财务地址")
  private String financeAddress;
  @ExcelProperty(value = "财务电话")
  private String financePhone;
  @ExcelProperty(value = "账期（天）")
  private String accountPeriod;
  @ExcelProperty(value = "每月几号结算")
  private String paymentDays;
  /**
   * 是否签署诚信廉洁协议
   */
  @ExcelProperty(value = "是否签署诚信廉洁协议")
  private Boolean honestStatus;
  /**
   * 协议编号
   */
  @ExcelProperty(value = "协议编号")
  private String honestNo;
  /**
   * 协议签订日期
   */
  @ExcelProperty("协议签订日期")
  @DateTimeFormat("yyyy年MM月dd日")
  @JsonFormat(pattern = "yyyy年MM月dd日")
  private Date honestStartDate;
  /**
   * 协议有效期至
   */
  @ExcelProperty("协议有效期至")
  @DateTimeFormat("yyyy年MM月dd日")
  @JsonFormat(pattern = "yyyy年MM月dd日")
  private Date honestEndDate;

  @ExcelProperty("是否黑名单")
  private Boolean blacklist;
  @ExcelProperty("用友分类编码")
  private String typeCode;
  @ExcelProperty("分类名称")
  private String typeName;
  @ExcelProperty("联系人及电话1")
  private String link1;
  @ExcelProperty("联系人及电话2")
  private String link2;
  @ExcelProperty("属性")
  private String tag;
  @ExcelProperty("所属部门")
  private String dept;
  @ExcelProperty("经营范围")
  private String scope;


}
