package com.natergy.ni.base.excel;

import com.natergy.ni.base.service.IToolManagementService;
import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;

import java.util.List;

@RequiredArgsConstructor
public class ToolManagementImporter implements ExcelImporter<ToolManagementExcel> {

	private final IToolManagementService toolManagementService;
	@Override
	public void save(List<ToolManagementExcel> data) {
		toolManagementService.importTool(data);
	}
}
