package com.natergy.ni.base.excel;

import com.natergy.ni.base.service.IMaterialService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;

/**
 * <AUTHOR>
 * @version 1.0

 * @since 2023/3/27 9:06
 */
@RequiredArgsConstructor
public class MaterialImporter implements ExcelImporter<MaterialExcel> {

  private final IMaterialService service;
  private final Boolean isCovered;

  @Override
  public void save(List<MaterialExcel> data) {
    service.importMaterial(data, isCovered);
  }

}
