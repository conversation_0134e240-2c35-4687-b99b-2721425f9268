package com.natergy.ni.base.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ToolManagementExcel implements Serializable {

	@ExcelProperty(value = "存货编码")
	private String materialCode;

	@ExcelProperty(value = "物品名称")
	private String materialName;

	@ExcelProperty(value = "规格")
	private String specification;

	@ExcelProperty(value = "数量")
	private BigDecimal num;

	@ExcelProperty(value = "单位")
	private String unit;

	@ExcelProperty(value = "是否上交")
	private String submit;

	@DateTimeFormat("yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ExcelProperty(value = "上交日期")
	private String submitDate;

	@ExcelProperty(value = "物品状态")
	private String itemStatus;

	@ExcelProperty(value = "领用人")
	private String keeperName;

	@ExcelProperty(value = "领用日期")
	@DateTimeFormat("yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private String lendDate;

	@ExcelProperty(value = "厂区")
	private String factory;

	@ExcelProperty(value = "使用对象")
	private String useObject;

	@ExcelProperty(value = "备注")
	private String remark;

}
