package com.natergy.ni.base.excel;

import com.natergy.ni.base.service.IDepotLocationService;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description  导入
 * @since 2023/3/27 9:06
 */
@RequiredArgsConstructor
public class DepotLocationImporter implements ExcelImporter<DepotLocationExcel> {

	private final IDepotLocationService service;
	private final Long depotId;
	@Override
	public void save(List<DepotLocationExcel> data) {
		service.importLocation(data,depotId);
	}

}
