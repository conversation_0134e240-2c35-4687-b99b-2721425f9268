package com.natergy.ni.base.excel;

import com.natergy.ni.base.service.IMaterialTypeService;
import com.natergy.ni.base.service.ISupplierInfoService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;

/**
 * <AUTHOR>
 * @version 1.0

 * @since 2023/3/27 9:06
 */
@RequiredArgsConstructor
public class SupplierImporter implements ExcelImporter<SupplierExcel> {

  private final ISupplierInfoService service;
  private final Boolean isCovered;

  @Override
  public void save(List<SupplierExcel> data) {
    service.importSupplier(data, isCovered);
  }

}
