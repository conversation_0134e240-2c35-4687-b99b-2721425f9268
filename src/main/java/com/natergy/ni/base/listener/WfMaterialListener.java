package com.natergy.ni.base.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.natergy.ni.base.cache.MaterialTypeCache;
import com.natergy.ni.base.entity.Material;
import com.natergy.ni.base.entity.MaterialType;
import com.natergy.ni.base.service.IMaterialService;
import com.natergy.ni.base.service.IMaterialTypeService;
import com.natergy.ni.old.entity.ChdaEntity;
import com.natergy.ni.old.service.IChdaService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.FieldExtension;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component("WfMaterialListener")
public class WfMaterialListener implements ExecutionListener {

  private static final long serialVersionUID = 7163759955671994974L;
  private final IMaterialService materialService;
  private final IMaterialTypeService materialTypeService;


  /**
   * @param execution
   */
  @Override
  public void notify(DelegateExecution execution) {
    Map<String, Object> vals = execution.getParent().getVariables();
    List<FieldExtension> extensions = execution.getCurrentFlowableListener()
        .getFieldExtensions();
    if (extensions != null && !extensions.isEmpty()) {
      Map<String, Object> extensionMap = extensions.stream()
          .collect(Collectors.toMap(FieldExtension::getFieldName,
              FieldExtension::getStringValue));
      vals.putAll(extensionMap);
    }
    Material material = JSON.parseObject(JSONObject.toJSONString(vals),
        Material.class);
    material.setProcessInsId(execution.getProcessInstanceId());
    material.setProcessDefId(execution.getProcessDefinitionId());
    material.setCurrentNode(execution.getCurrentFlowElement().getName());
    if (material.getStatus() == null) {
      material.setStatus(WfEntity.STATUS_SUBMIT);
    }
    if (vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE) != null
        && WfEntity.statusMap.get(
        vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE).toString()) != null) {
      material.setStatus(WfEntity.statusMap.get(
          vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE).toString()));
    }
    MaterialType materialType = materialTypeService.getById(material.getTypeId());
    if (!Objects.equals(material.getStatus(), WfEntity.STATUS_TERMINATION) &&(StringUtils.isBlank(material.getCode()) || (materialType != null && !material.getCode()
        .startsWith(materialType.getCode())))) {
      material.setCode(buildCode(material));
      //检查code是否重复
      long num = materialService.count(
          Wrappers.<Material>lambdaQuery().eq(Material::getCode, material.getCode()));
      Preconditions.checkState(num == 0, "编码重复:" + material.getCode());
      execution.setVariable("code", material.getCode());
    }
    if (material.getCost() != null && !material.getCost()) {
      material.setSync(true);
    }
    materialService.submit(material);
    execution.setVariable("id", material.getId());
  }


  private String buildCode(Material entity) {
    MaterialType type = MaterialTypeCache.getById(entity.getTypeId());
    Preconditions.checkState(type != null && type.getType().equals(MaterialType.TYPE_LEAF),
        "编码类型不存在/类型不是叶子节点，请重新选择");
    //TODO 自定义编号生成条件,如果规则变了需要修改代码
    Material last = materialService.getOne(Wrappers.<Material>query().select("top 1 *")
        .ne("isnull(code,'')", "")
        .lambda()
        .eq(Material::getTypeId, entity.getTypeId())
        .ne(entity.getId() != null, Material::getId, entity.getId())
        .in(Material::getStatus, WfEntity.STATUS_REJECT, WfEntity.STATUS_SUSPEND,
            WfEntity.STATUS_CANCEL, WfEntity.STATUS_TERMINATION,
            WfEntity.STATUS_SUBMIT, WfEntity.STATUS_APPROVAL, WfEntity.STATUS_FINISH)
        .orderByDesc(Material::getCode), false);
    String code;
    if (last != null && StringUtils.isNotBlank(last.getCode())
        && last.getCode().length() == 14) {
      code = String.format(
          "%s%0" + (14 - type.getCode().length()) + "d", type.getCode(),
          Integer.parseInt(last.getCode().substring(type.getCode().length())) + 1);
    } else if (last != null && StringUtils.isNotBlank(last.getCode())
        && last.getCode().length() != 14) {
      code = String.format(
          "%s%0" + (last.getCode().length() - type.getCode().length()) + "d", type.getCode(),
          Integer.parseInt(last.getCode().substring(type.getCode().length())) + 1);
    } else {
      int length = 14 - (type.getCode().length() + 1);
      code = type.getCode() + String.format("%0" + length + "d", 0) + 1;
    }
    return code;
  }
}
