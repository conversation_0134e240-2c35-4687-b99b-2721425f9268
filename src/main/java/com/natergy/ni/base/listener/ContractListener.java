package com.natergy.ni.base.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.base.entity.ContractEntity;
import com.natergy.ni.base.event.ContractPorEvent;
import com.natergy.ni.por.entity.PorOrder;
import com.natergy.ni.por.service.IPorOrderService;
import com.natergy.ni.por.vo.PorOrderCostItemVO;
import com.natergy.ni.por.vo.PorOrderItemVO;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.tool.utils.Func;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/5/9 13:31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContractListener {

  private final IPorOrderService porOrderService;

  /**
   * 更新到货单状态
   *
   * @param event
   */
  @SneakyThrows
  @EventListener(ContractPorEvent.class)
  public void updatePorOrderContractId(ContractPorEvent event) {
    ContractEntity contract = event.getContract();
    if (StringUtils.isBlank(contract.getType()) || !Func.toStrList(contract.getType())
        .contains(ContractEntity.TYPE_POR)) {
      return;
    }
    Set<Long> orderIds = new HashSet<>(Func.toLongList(contract.getOrderId()));
    List<PorOrderItemVO> items = contract.getItems();
    if (items != null && !items.isEmpty()) {
      orderIds.addAll(
          items.stream().map(PorOrderItemVO::getOrderId).collect(Collectors.toSet()));
    }
    if (!orderIds.isEmpty() && Arrays.asList(WfEntity.STATUS_SUBMIT, WfEntity.STATUS_APPROVAL,
            WfEntity.STATUS_FINISH)
        .contains(contract.getStatus())) {
      porOrderService.update(Wrappers.<PorOrder>lambdaUpdate()
          .in(PorOrder::getId, orderIds).set(PorOrder::getContractId, contract.getId())
          .set(PorOrder::getContractSerialNo, contract.getSerialNo()));
    } else if (!orderIds.isEmpty() && Arrays.asList(WfEntity.STATUS_CANCEL,
        WfEntity.STATUS_TERMINATION).contains(contract.getStatus())) {
      porOrderService.update(Wrappers.<PorOrder>lambdaUpdate()
          .in(PorOrder::getId, orderIds).set(PorOrder::getContractId, null));
    }
  }


}
