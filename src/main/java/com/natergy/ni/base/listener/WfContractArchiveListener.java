package com.natergy.ni.base.listener;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.natergy.ni.base.entity.ContractEntity;
import com.natergy.ni.base.service.IContractService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
import org.springblade.plugin.workflow.core.utils.WfTaskUtil;
import org.springblade.plugin.workflow.process.mapper.WfProcessInfoMapper;
import org.springblade.plugin.workflow.process.model.WfProcess;
import org.springblade.plugin.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component("WfContractArchiveListener")
public class WfContractArchiveListener implements ExecutionListener {

	private final IContractService contractService;
	private final IWfProcessService processService;

	private final WfProcessInfoMapper wfProcessInfoMapper;

	private final IAttachService attachService;

	public static final String MODULE = "ni_base_contract_archive";
	private final RuntimeService runtimeService;

	@Override
	public void notify(DelegateExecution execution) {
		String eventName = execution.getEventName();

		if (EVENTNAME_START.equals(eventName)){
			Map<String, Object> vals = execution.getParent().getVariables();
//				List<FieldExtension> extensions = execution.getCurrentFlowableListener()
//					.getFieldExtensions();
			//把合同归档状态设为0（等待审批）
			Long contractId = Long.valueOf(vals.get("contractId").toString());
			Integer archiveNo = Integer.valueOf(vals.get("archiveNo").toString());
			String archiveSerialNo = vals.get("archiveSerialNo").toString();
			LambdaUpdateWrapper<ContractEntity> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(ContractEntity::getId, contractId)
				.set(ContractEntity::getArchiveNo, archiveNo)
				.set(ContractEntity::getArchiveSerialNo,archiveSerialNo)
				.set(ContractEntity::getArchive, 0)
				.set(ContractEntity::getArchiveProcessInsId, execution.getProcessInstanceId());
			contractService.update(updateWrapper);
			//添加附件
			//同步到附件表
			if (vals.get("attachment") != null) {
				List<Map<String,String>> attachList = (List<Map<String, String>>)vals.get("attachment");
				attachList.forEach(item -> {
					Attach attach = attachService.getById(item.get("value"));
					if (attach != null) {
						attach.setBusinessName(MODULE);
						attach.setBusinessKey(contractId.toString());
						attachService.saveOrUpdate(attach);
					}
				});
				System.out.println(attachList);
			}

		}

		if (EVENTNAME_END.equals(eventName)){
			log.error("Fastjson JSONArray class loaded from: " + com.alibaba.fastjson.JSONArray.class.getProtectionDomain().getCodeSource().getLocation());
//			try{
//				execution.getParent().getVariable("attachment");
//			}catch (Exception e){
//				log.error("结束节点异常",e);
//				runtimeService.setVariable(execution.getProcessInstanceId(), "attachment", null);
//				execution.getParent().setVariable("attachment", null);
//			}
//			Map<String, Object> vals = execution.getParent().getVariables();
//			String taskId = wfProcessInfoMapper.selectTaskIdProcessInsId(execution.getProcessInstanceId());
//			String taskId = execution.getId();
			//流程结束时生成归档编号
				//当通过时
				if (execution.getParent().getVariable(WfProcessConstant.PASS_KEY) != null) {
//					int contractNo = contractService.getNextArchiveNo();
					//把合同归档状态设为1（已归档），且附上合同编号
					LambdaUpdateWrapper<ContractEntity> updateWrapper = new LambdaUpdateWrapper<>();
					updateWrapper.eq(ContractEntity::getId, Long.valueOf(execution.getParent().getVariable("contractId").toString()))
						.set(ContractEntity::getArchive, 1);
					contractService.update(updateWrapper);
				} else {
					//把合同归档状态设为null（未归档）
					LambdaUpdateWrapper<ContractEntity> updateWrapper = new LambdaUpdateWrapper<>();
					updateWrapper.eq(ContractEntity::getId, Long.valueOf(execution.getParent().getVariable("contractId").toString()))
						.set(ContractEntity::getArchive, null)
						.set(ContractEntity::getArchiveNo,null)
						.set(ContractEntity::getArchiveSerialNo, null)
						.set(ContractEntity::getArchiveProcessInsId,null);
					contractService.update(updateWrapper);
				}
		}
	}
}
