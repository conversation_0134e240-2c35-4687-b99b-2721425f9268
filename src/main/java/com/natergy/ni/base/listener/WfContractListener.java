package com.natergy.ni.base.listener;

import static com.natergy.ni.base.constant.BaseConstant.CONTRACT_POR_MODULE;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.natergy.ni.base.controller.ContractController;
import com.natergy.ni.base.entity.ContractEntity;
import com.natergy.ni.base.event.ContractPorEvent;
import com.natergy.ni.base.service.IContractService;
import com.natergy.ni.base.service.ISerialNoService;
import com.natergy.ni.base.vo.ContractVO;
import com.natergy.ni.por.entity.PorApply;
import com.natergy.ni.por.entity.PorOrder;
import com.natergy.ni.por.service.IPorApplyService;
import com.natergy.ni.por.service.IPorOrderService;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.FieldExtension;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.cache.UserCache;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserSearchService;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component("WfContractPorListener")
public class WfContractListener implements ExecutionListener {

	private static final long serialVersionUID = 7163759955671994974L;
	private final IContractService contractService;
	private final ISerialNoService serialNoService;
	private final IAttachService attachService;
	private final IUserSearchService userSearchService;
	private final IPorOrderService porOrderService;
	private final IPorApplyService porApplyService;

	/**
	 * @param execution
	 */
	@Override
	public void notify(DelegateExecution execution) {
		Map<String, Object> vals = execution.getParent().getVariables();
		List<FieldExtension> extensions = execution.getCurrentFlowableListener()
			.getFieldExtensions();
		if (extensions != null && !extensions.isEmpty()) {
			Map<String, Object> extensionMap = extensions.stream()
				.collect(Collectors.toMap(FieldExtension::getFieldName,
					FieldExtension::getStringValue));
			vals.putAll(extensionMap);
		}
		ContractVO contractVO = JSON.parseObject(JSONObject.toJSONString(vals),
			ContractVO.class);
		contractVO.setProcessInsId(execution.getProcessInstanceId());
		contractVO.setProcessDefId(execution.getProcessDefinitionId());
		contractVO.setCurrentNode(execution.getCurrentFlowElement().getName());
		if (contractVO.getType().startsWith(ContractEntity.TYPE_POR)) {
			if (contractVO.getPorApplyUserIds() == null || contractVO.getPorApplyUserIds()
				.isEmpty()) {
				String porOrderId = contractVO.getOrderId();
				if (StringUtils.isNotBlank(porOrderId)) {
					PorOrder porOrder = porOrderService.getById(porOrderId);
					if (porOrder == null) {
						porOrder = new PorOrder();
						porOrder.setFromIds("");
					}
					List<Long> applyIds = Func.toLongList(porOrder.getFromIds());
					if (!applyIds.isEmpty()) {
						List<PorApply> applies = porApplyService.listByIds(applyIds);
						if (!applies.isEmpty()) {
							contractVO.setPorApplyUserIds(
								applies.stream().map(PorApply::getCreateUser).collect(
									Collectors.toList()));
							execution.setVariable("porApplyUserIds",
								contractVO.getPorApplyUserIds());

						}
					}
				}
			}
			if (contractVO.getPorApplyUserIds() != null && !contractVO.getPorApplyUserIds()
				.isEmpty()
				&& (contractVO.getPorApplyUserLeaderIds() == null
				|| contractVO.getPorApplyUserLeaderIds()
				.isEmpty())) {
				contractVO.setPorApplyUserLeaderIds(
					contractVO.getPorApplyUserIds().stream().flatMap(userId -> {
						User user = UserCache.getUser(userId);
						if (user != null && "陈英".equals(user.getRealName())) {
							User leader = UserCache.getUser(BladeConstant.ADMIN_TENANT_ID,
								"臧令军");
							if (leader != null) {
								return Stream.of(leader);
							}
						}
						List<User> leaders = userSearchService.deptLeaderListByUser(userId);
						if (leaders != null && !leaders.isEmpty()) {
							return leaders.stream();
						}
						return Stream.empty();
					}).map(User::getId).collect(Collectors.toList()));
				execution.setVariable("porApplyUserLeaderIds",
					contractVO.getPorApplyUserLeaderIds());
			}
		}
		if (contractVO.getStatus() == null) {
			contractVO.setStatus(WfEntity.STATUS_SUBMIT);
			execution.setVariable("status", WfEntity.STATUS_SUBMIT);
		}
		if (StringUtils.isBlank(contractVO.getSerialNo())) {
			String no;
			if (LocalDate.now().isAfter(LocalDate.of(2025, 5, 31))) {
				no = contractService.genSerialNo(contractVO.getCreateUser());
			} else {
				no = serialNoService.gen(CONTRACT_POR_MODULE);
			}
			contractVO.setSerialNo(no);
			execution.setVariable("serialNo", no);
		}
		if (vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE) != null
			&& WfEntity.statusMap.get(
			vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE).toString()) != null) {
			contractVO.setStatus(WfEntity.statusMap.get(
				vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE).toString()));
		}
		contractVO.setBbType("supplier");
		contractVO.setTenantId(BladeConstant.ADMIN_TENANT_ID);
		contractService.saveOrUpdate(contractVO);
		SpringUtil.publishEvent(new ContractPorEvent(contractVO));
		execution.setVariable("id", contractVO.getId());
		if (contractVO.getAttachment() != null && !contractVO.getAttachment().isEmpty()) {
			List<Map<String, Object>> attachment = contractVO.getAttachment();
			List<Attach> attaches = attachService.listByIds(
				attachment.stream().map(map -> String.valueOf(map.get("value")))
					.collect(Collectors.toSet()));
			attaches.forEach(attach -> {
				attach.setBusinessName(ContractController.MODULE);
				attach.setBusinessKey(contractVO.getId() + "");
			});
			attachService.saveOrUpdateBatch(attaches);
		}
	}
}
