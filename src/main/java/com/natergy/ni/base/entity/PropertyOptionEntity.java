/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 动态属性可选项 实体类
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
@TableName("ni_base_property_option")
@ApiModel(value = "PropertyOption对象", description = "动态属性可选项")
@EqualsAndHashCode(callSuper = true)
public class PropertyOptionEntity extends TenantEntity {

	/**
	 * 属性id
	 */
	@ApiModelProperty(value = "属性id")
	@NotNull(message = "属性id不能为空")
	private Long propertyId;

	/**
	 * 选项
	 */
	@ApiModelProperty(value = "选项")
	@NotEmpty(message = "选项值不能为空")
	@Length(max = 100, message = "选项值长度不能超过{max}个字符")
	private String optionName;

	/**
	 * 显示顺序
	 */
	@ApiModelProperty(value = "显示顺序")
	@NotNull(message = "显示顺序不能为空")
	private Integer ordered;

	/**
	 * 是否启用
	 */
	@ApiModelProperty(value = "是否启用")
	@NotNull(message = "是否启用不能为空")
	@TableField("is_active")
	private Integer active;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	@Length(max = 200, message = "备注长度不能超过{max}个字符")
	private String remark;

}
