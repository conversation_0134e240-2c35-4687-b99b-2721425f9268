/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@TableName("ni_base_depot")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DepotInfo对象", description = "DepotInfo对象")
public class DepotInfo extends BaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 仓库名称
   */
  @ApiModelProperty(value = "仓库名称")
  private String name;
  /**
   * 仓库编码
   */
  @TableField(condition = SqlCondition.LIKE_RIGHT)
  @ApiModelProperty(value = "仓库编码")
  private String code;
  /**
   * 仓库类型
   */
  @ApiModelProperty(value = "仓库类型")
  private String type;
  /**
   * 仓库地址
   */
  @ApiModelProperty(value = "仓库地址")
  private String address;
  /**
   * 负责人id
   */
  @ApiModelProperty(value = "负责人id")
  private Long principalId;
  /**
   * 备注
   */
  @ApiModelProperty(value = "备注")
  private String remark;

  @DateTimeFormat(
      pattern = "yyyy-MM-dd HH:mm:ss"
  )
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm:ss"
  )
  private Date applyTime;
  @TableField(exist = false)
  private String principalName;

  /**
   * 位置经度
   */
  @TableField(whereStrategy = FieldStrategy.NEVER)
  @ApiModelProperty(value = "位置经度")
  private BigDecimal longitude;
  /**
   * 位置纬度
   */
  @TableField(whereStrategy = FieldStrategy.NEVER)
  @ApiModelProperty(value = "位置纬度")
  private BigDecimal latitude;
  /**
   * 距离
   */
  @TableField(exist = false)
  private BigDecimal distance;


}
