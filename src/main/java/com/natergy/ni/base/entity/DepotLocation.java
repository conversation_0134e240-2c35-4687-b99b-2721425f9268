/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Data
@TableName("ni_base_depot_location")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DepotLocation对象", description = "DepotLocation对象")
public class DepotLocation extends BaseEntity {


	private static final long serialVersionUID = 1101180664810304076L;
	private Long infoId;
	@TableField(condition = SqlCondition.LIKE)
	private String name;
	@TableField(condition = SqlCondition.LIKE)
	private String code;
	private String remark;


}
