/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.plugin.workflow.core.entity.WfEntity;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
@JsonInclude(Include.NON_NULL)
@TableName(value = "ni_base_material", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaterialInfo对象", description = "MaterialInfo对象")
public class Material extends WfEntity {

  private static final long serialVersionUID = -8505050137552485826L;
  @ApiModelProperty(value = "存货名称")
  @TableField(condition = SqlCondition.LIKE)
  private String name;
  @ApiModelProperty(value = "存货编码")
  @TableField(condition = SqlCondition.LIKE)
  private String code;
  @ApiModelProperty(value = "制造商/品牌")
  private String manufacturer;
  @ApiModelProperty(value = "类目id")
  private Long typeId;
  @ApiModelProperty(value = "最小存量单位")
  private String sku;
  @ApiModelProperty(value = "单位")
  private String unit;
  @ApiModelProperty(value = "规格")
  @TableField(condition = SqlCondition.LIKE)
  private String specification;
  @ApiModelProperty(value = "材质")
  @TableField(condition = SqlCondition.LIKE)
  private String quality;
  @ApiModelProperty(value = "属性")
  private String attr;
  @ApiModelProperty(value = "颜色")
  private String color;
  @ApiModelProperty(value = "是否有批号")
  private Boolean hasBatchPro;
  @ApiModelProperty(value = "安全存量上限")
  private Integer stockUpper;
  @ApiModelProperty(value = "安全存量下限")
  private Integer stockLower;
  @ApiModelProperty(value = "备注")
  private String remark;
  @ApiModelProperty(value = "国标")
  private String gb;
  @ApiModelProperty(value = "净重/kg")
  private Double weight;

  @ApiModelProperty(value = "内包装")
  private String innerPackaging;
  @ApiModelProperty(value = "外包装")
  private String outerPackaging;
  @TableField(typeHandler = FastjsonTypeHandler.class)
  @ApiModelProperty(value = "扩展字段")
  private Map<String, Object> params;
  @ApiModelProperty(value = "标签")
  private String tag;
  //---包装----
  /**
   * 特殊要求
   */
  @ApiModelProperty(value = "特殊要求")
  private String special;
  @ApiModelProperty(value = "质量级别")
  private String qualityLevel;
  @ApiModelProperty(value = "包装标签")
  private String label;
  @ApiModelProperty(value = "托盘")
  private String tray;
  @ApiModelProperty(value = "纸护角")
  private Boolean corner;
  @ApiModelProperty(value = "大纸板")
  private String bigCardboard;
  @ApiModelProperty(value = "其他包装")
  private String otherPacking;


  private Long markId;

  @ApiModelProperty("用友同步状态")
  @TableField(typeHandler = FastjsonTypeHandler.class)
  private List<Map<String, Object>> yonyouSync;


  @ApiModelProperty(value = "是否销售品")
  private Boolean sell;
  @ApiModelProperty("是否采购")
  private Boolean purchase;
  @ApiModelProperty("是否自制")
  private Boolean selfMake;
  @ApiModelProperty("是否生产耗用")
  private Boolean productConsume;
  @ApiModelProperty("初始库存")
  private Long initialNum;
  @ApiModelProperty("是否费用")
  private Boolean cost;
  @ApiModelProperty("是否禁用")
  private Boolean disabled;
  @ApiModelProperty("是否开启同步")
  private Boolean sync;
  @TableField(exist = false)
  private Boolean todo;

  @ApiModelProperty("相关信息")
  @TableField(exist = false)
  private String searchInfo;
  @TableField(exist = false)
  private String sequence;
}
