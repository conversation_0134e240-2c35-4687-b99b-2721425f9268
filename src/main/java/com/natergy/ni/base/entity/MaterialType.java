/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.plugin.workflow.core.entity.WfEntity;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
@TableName(value = "ni_base_material_type", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaterialType对象", description = "MaterialType对象")
public class MaterialType extends WfEntity {


	private static final long serialVersionUID = -2898561381407730541L;
	/**
	 * 目录
	 */
	public static final String TYPE_CATALOGUE = "1";
	/**
	 * 叶子
	 */
	public static final String TYPE_LEAF = "2";


	@ApiModelProperty("分类名称")
	private String name;
	@ApiModelProperty("分类编码")
	private String code;
	@ApiModelProperty("父id")
	private Long parentId;
	private String ancestors;
	@ApiModelProperty("排序")
	private Integer sn;
	@ApiModelProperty("备注")
	private String remark;
	@ApiModelProperty("租户id")
	private String tenantId;
	@ApiModelProperty("用友同步状态")
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private List<Map<String, Object>> yonyouSync;
	@ApiModelProperty("树类型")
	private String type;
	@ApiModelProperty("规格举例")
	private String specification;
	@ApiModelProperty("默认单位")
	private String unit;
	@ApiModelProperty("分类国标")
	private String gb;
	@ApiModelProperty("材质")
	private String quality;
	@ApiModelProperty("是否已同步")
	private Boolean sync;

}
