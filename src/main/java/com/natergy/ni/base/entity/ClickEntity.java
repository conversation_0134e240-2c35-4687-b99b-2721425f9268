package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Data
@TableName("ni_birth_click")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Click对象", description = "点击记录")
public class ClickEntity extends TenantEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 点击人
	 */
	@ApiModelProperty(value = "点击人")
	private long clicker;
	/**
	 * 点击时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "点击时间")
	private Date clickTime;
}
