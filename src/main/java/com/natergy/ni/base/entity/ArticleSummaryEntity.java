/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;


/**
 * 行业资讯 实体类
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Data
@TableName("article_summary")
@ApiModel(value = "ArticleSummary对象", description = "行业资讯")
@EqualsAndHashCode(callSuper = true)
public class ArticleSummaryEntity extends BaseEntity {

	/**
	 * 编号
	 */
//	private Integer id;
	/**
	 * 文章标题
	 */
	private String title;
	/**
	 * 原文链接
	 */
	private String link;
	/**
	 * 文章发布日期
	 */
	private String date;
	/**
	 *  文章摘要
	 */
	private String summary;
	/**
	 * 信息来源
	 */
	private Integer source;
	/**
	 * 采集地址名称
	 */
	private String author;
	/**
	 * 权重
	 */
	private Integer weight;

}
