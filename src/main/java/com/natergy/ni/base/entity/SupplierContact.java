/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
@Data
@TableName("ni_base_supplier_contact")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplierContact对象", description = "SupplierContact对象")
public class SupplierContact extends BaseEntity {

	private static final long serialVersionUID = 1L;

	private Long supplierId;
	private String name;
	private String dept;
	private String post;
	private Integer gender;
	private Integer age;
	private String hometown;
	private String phone;
	private String qq;
	private String wechat;
	private String email;
	private String remark;
	private Boolean main;

}
