package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Data
@TableName("ni_base_birthday")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Birthday对象", description = "生日祝福")
public class BirthdayEntity extends TenantEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 发送人
	 */
	@ApiModelProperty(value = "发送人")
	private long senderId;
	/**
	 * 接收人
	 */
	@ApiModelProperty(value = "接收人")
	private long receiverId;
	/**
	 * 祝福信息
	 */
	@ApiModelProperty(value = "祝福信息")
	private String message;

	/**
	 * 祝福信息
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "祝福日期")
	private Date remindDate;
}

