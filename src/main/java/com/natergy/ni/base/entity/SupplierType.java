/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
@TableName(value = "ni_base_supplier_type", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "供应商类型对象", description = "供应商类型对象")
public class SupplierType extends BaseEntity {


  private static final long serialVersionUID = -2685720777877110440L;
  /**
   * 目录
   */
  public static final String TYPE_CATALOGUE = "1";
  /**
   * 叶子
   */
  public static final String TYPE_LEAF = "2";
  @ApiModelProperty("分类名称")
  private String name;
  @ApiModelProperty("分类编码")
  private String code;
  private Long parentId;
  private String ancestors;
  @ApiModelProperty("排序")
  private Integer sn;
  @ApiModelProperty("备注")
  private String remark;
  private String tenantId;
  @ApiModelProperty("用友同步状态")
  @TableField(typeHandler = FastjsonTypeHandler.class)
  private List<Map<String, Object>> yonyouSync;

  @ApiModelProperty("树类型")
  private String type;

}
