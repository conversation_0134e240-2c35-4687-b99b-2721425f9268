/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * BOM维护 实体类
 *
 * <AUTHOR>
 * @since 2023-01-15
 */
@Data
@TableName("ni_base_bom")
@ApiModel(value = "Bom对象", description = "BOM维护")
@EqualsAndHashCode(callSuper = true)
public class BomEntity extends TenantEntity {

  /**
   * 用量
   */
  @ApiModelProperty(value = "用量")
  private Integer num;
  /**
   * 物料单位
   */
  @ApiModelProperty(value = "物料单位")
  private String unit;
  /**
   * 损耗率
   */
  @ApiModelProperty(value = "损耗率")
  private Float lossRate;
  /**
   * 备注
   */
  @ApiModelProperty(value = "备注")
  private String remark;
  /**
   * 损耗数
   */
  @ApiModelProperty(value = "损耗数")
  private Integer lossNum;
  /**
   *
   */
  @ApiModelProperty(value = "")
  private Long parentId;
  /**
   *
   */
  @ApiModelProperty(value = "")
  private Long materialId;

}
