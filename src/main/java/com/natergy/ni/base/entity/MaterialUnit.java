package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * <AUTHOR>
 * @version 1.0

 * @since 2022/12/8 9:20
 */
@Data
@JsonInclude(Include.NON_NULL)
@TableName("ni_base_material_unit")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Material转换对象", description = "Material转换对象")
public class MaterialUnit extends BaseEntity {

  private static final long serialVersionUID = -2371817794909989641L;
  /**
   * 存货id
   */
  private Long materialId;
  /**
   * 单位
   */
  private String unit;
  /**
   * 转换单位
   */
  private String trans;
  /**
   * 转换率
   */
  private double rate;
  /**
   * 备注
   */
  private String remark;

}
