/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.natergy.ni.por.entity.PorOrder;
import com.natergy.ni.por.entity.PorOrderCostItem;
import com.natergy.ni.por.entity.PorOrderItem;
import com.natergy.ni.por.vo.PorOrderCostItemVO;
import com.natergy.ni.por.vo.PorOrderItemVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 基本信息-合同管理 实体类
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Data
@TableName("ni_base_contract")
@ApiModel(value = "Contract对象", description = "基本信息-合同管理")
@EqualsAndHashCode(callSuper = true)
public class ContractEntity extends WfEntity {

	/**
	 * 拟稿中
	 */
	public static final String CONTRACT_STATE_DRAFT = "1";
	/**
	 * 生效
	 */
	public static final String CONTRACT_STATE_EFFECT = "2";
	/**
	 * 终止
	 */
	public static final String CONTRACT_STATE_END = "3";
	/**
	 * 作废
	 */
	public static final String CONTRACT_STATE_TO_VOID = "4";
	/**
	 * 完成
	 */
	public static final String CONTRACT_STATE_FINISH = "5";
	/**
	 * 付款合同
	 */
	public static final String PAY_TYPE_PAY = "2";
	/**
	 * 收款合同
	 */
	public static final String PAY_TYPE_ACCEPT = "1";
	/**
	 * 无
	 */
	public static final String PAY_TYPE_NONE = "9";

	public static final String TYPE_POR = "1";
	public static final String TYPE_POR_YEAR = "11";
	public static final String TYPE_POR_NORMAL = "12";
	public static final String TYPE_POR_ORDER = "13";
	public static final String TYPE_SELL = "2";
	public static final String TYPE_LEASE = "3";
	/**
	 * 补充合同
	 */
	public static final String TYPE_SUPPLEMENT = "4";
	/**
	 * 其他合同
	 */
	public static final String TYPE_OTHER = "9";
	private static final long serialVersionUID = -3321491433230266247L;

	/**
	 * 账套 绿能
	 */
	public static final String BRAND_LN = "1";
	/**
	 * 账套 至简
	 */
	public static final String BRAND_ZHJ = "2";
	/**
	 * 账套 演绎
	 */
	public static final String BRAND_YY = "4";
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 合同类型
	 */
	@ApiModelProperty(value = "合同类型")
	private String type;
	/**
	 * 合同编号
	 */
	@ApiModelProperty(value = "合同编号")
	private String serialNo;
	/**
	 * 合同名称
	 */
	@ApiModelProperty(value = "合同名称")
	private String name;
	/**
	 * 甲方
	 */
	@ApiModelProperty(value = "甲方")
	private Long a;
	/**
	 * 乙方
	 */
	@ApiModelProperty(value = "乙方")
	private Long b;
	/**
	 * 甲方负责人
	 */
	@TableField("a_pic")
	@ApiModelProperty(value = "甲方负责人")
	private String aaPic;
	/**
	 * 乙方负责人
	 */
	@TableField("b_pic")
	@ApiModelProperty(value = "乙方负责人")
	private String bbPic;
	/**
	 * 合同金额
	 */
	@ApiModelProperty(value = "合同金额")
	private BigDecimal amount;
	/**
	 * 合同状态
	 */
	@ApiModelProperty(value = "合同状态")
	private String contractState;
	/**
	 * 签订日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "签订日期")
	private Date contractDate;
	/**
	 * 有效期
	 */
	@ApiModelProperty(value = "有效期")
	private Integer exp;
	/**
	 * 合同生效日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "合同生效日期")
	private Date effectiveDate;
	/**
	 * 合同结束日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "合同结束日期")
	private Date endDate;
	/**
	 * 关键条款
	 */
	@ApiModelProperty(value = "关键条款")
	private String keyTerms;
	/**
	 * 部门
	 */
	@ApiModelProperty(value = "部门")
	private Long deptId;
	/**
	 * 合同收付类型
	 */
	@ApiModelProperty(value = "合同收付类型")
	private String payType;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date finishTime;
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String aaType;
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String bbType;
	private String currency;

	private String brand;

	@TableField(exist = false)
	private List<PorOrderItemVO> items;

	/**
	 * 關聯訂單
	 */
	private String orderId;
	/**
	 * 发票类型
	 */
	private String billType;
	/**
	 * 税率
	 */
	private Double taxRate;
	/**
	 * 是否模板合同
	 */
	private Boolean template;

	@ApiModelProperty("付款申请状态")
	private Integer payApplyState;
	/**
	 * 发票状态
	 */
	private Integer billState;

	/**
	 * 归档编号
	 */
	private Integer archiveNo;

	/**
	 * 是否归档
	 */
	private Integer archive;

	/**
	 * 归档流程实例id
	 */
	private String archiveProcessInsId;

	/**
	 * 归档编号
	 */
	private String archiveSerialNo;
	/**
	 * 上级id。，（补充合同时使用）
	 */
	private Long parentId;
}
