
package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 基本信息-工具管理 实体类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Data
@TableName("ni_base_tool")
@ApiModel(value = "ToolManagement对象", description = "基本信息-工具管理")
@EqualsAndHashCode(callSuper = true)
public class ToolManagementEntity extends TenantEntity {


	/**
	 *物品id
	 */
	private Long materialId;
	/**
	 *数量
	 */
	private BigDecimal num;
	/**
	 *保管人
	 */
	private Long keeperId;
	/**
	 *保管人部门
	 */
	private Long keeperDeptId;
	/**
	 *领用日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private String lendDate;
	/**
	 *备注
	 */
	private String remark;
	/**
	 *物品状态
	 */
	private Integer itemStatus;
	/**
	 *人员状态
	 */
	private Integer userStatus;
	/**
	 *是否上交
	 */
	private Integer submit;
	/**
	 *上交日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private String submitDate;
	/**
	 *物品名称
	 */
	private String materialName;
	/**
	 * 厂区
	 */
	private Integer factory;
	/**
	 * 存货编码
	 */
	private String materialCode;
	/**
	 * 规格
	 */
	private String specification;
	/**
	 * 单位
	 */
	private Integer unit;
	/**
	 * 使用对象
	 */
	private Integer useObject;

}
