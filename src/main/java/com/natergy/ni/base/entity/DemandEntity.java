package com.natergy.ni.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Data
@TableName("ni_base_demand")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Demand对象", description = "研发需求")
public class DemandEntity extends WfEntity {


  private static final long serialVersionUID = 8743522788367494920L;

  public static final Integer STATUS_DRAFT = 0;
  public static final Integer STATUS_SUBMIT = 1;
  /**
   * 审核中
   */
  public static final Integer STATUS_APPROVAL = 2;
  /**
   * 已审核
   */
  public static final Integer STATUS_APPROVED = 3;
  /**
   * 开发中
   */
  public static final Integer STATUS_DEVELOPING = 4;
  /**
   * 开发完成
   */
  public static final Integer STATUS_DEVELOPED = 5;
  /**
   * 验收中
   */
  public static final Integer STATUS_ACCEPTING = 6;
  /**
   * 已验收
   */
  public static final Integer STATUS_ACCEPTED = 7;
  /**
   * 被驳回
   */
  public static final Integer STATUS_REJECT = 8;
  /**
   * 已终止
   */
  public static final Integer STATUS_TERMINATION = 9;

  /**
   * 申请日期
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd")
  @ApiModelProperty(value = "申请日期")
  private Date applyTime;
  /**
   * 申请人
   */
  @ApiModelProperty(value = "申请人")
  private String userId;
  /**
   * 部门
   */
  @ApiModelProperty(value = "部门")
  private String depId;
  /**
   * 类别
   */
  @ApiModelProperty(value = "类别")
  private String classify;
  /**
   * 需求内容
   */
  @ApiModelProperty(value = "需求内容")
  private String demandContent;
  /**
   * 优先级
   */
  @ApiModelProperty(value = "优先级")
  private String priority;
  /**
   * 研发人员
   */
  @ApiModelProperty(value = "研发人员")
  private Long developer;
  /**
   * 解决方案
   */
  @ApiModelProperty(value = "解决方案")
  private String solution;
  /**
   * 预计完成时间
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd")
  @ApiModelProperty(value = "预计完成时间")
  private Date planTime;
  /**
   * 实际完成时间
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd")
  @ApiModelProperty(value = "实际完成时间")
  private Date actualTime;
  /**
   * 当前阶段
   */
  @ApiModelProperty(value = "当前阶段")
  private String currentStage;
  /**
   * 验收人
   */
  @ApiModelProperty(value = "验收人")
  private String acceptor;
  /**
   * 验收时间
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd")
  @ApiModelProperty(value = "验收时间")
  private Date acceptTime;
  /**
   * 验收结果
   */
  @ApiModelProperty(value = "验收结果")
  private String acceptResult;
  /**
   * 验收结果
   */
  @ApiModelProperty(value = "不合格原因")
  private String unqualifiedReason;


  private String taskId;
  /**
   * 查询操作人员
   */
  @TableField(exist = false)
  private Long optionUser;

  /**
   * 申请人
   */
  @ApiModelProperty(value = "申请人")
  @TableField(exist = false)
  private String userName;
  /**
   * 部门
   */
  @ApiModelProperty(value = "部门")
  @TableField(exist = false)
  private String depName;
  @ApiModelProperty(value = "研发人员")
  @TableField(exist = false)
  private String developerName;
  @TableField(exist = false)
  private Boolean todo;
}
