/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.base.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
@Data
@TableName(value = "ni_base_supplier", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplierInfo对象", description = "SupplierInfo对象")
public class SupplierInfo extends TenantEntity {

	private static final long serialVersionUID = 2131293394532102244L;

	public static final String ACL_PRIVATE = "1";

	public static final String ACL_PUBLIC = "2";

	@ApiModelProperty(value = "供应商名称")
	private String name;
	@ApiModelProperty(value = "供应商编码")
	private String code;
	@ApiModelProperty(value = "类别")
	private String type;
	@ApiModelProperty(value = "省")
	private String provinceCode;
	@ApiModelProperty(value = "城市")
	private String cityCode;
	@ApiModelProperty(value = "地址")
	private String address;
	@ApiModelProperty(value = "法人")
	private String corporation;
	@ApiModelProperty(value = "注册资本（万元）")
	private Double registeredCapital;
	@ApiModelProperty(value = "供应商等级")
	private String level;
	@ApiModelProperty(value = "人员规模")
	private String scale;
	@ApiModelProperty(value = "主要联系人")
	private String linkman;
	@ApiModelProperty(value = "联系电话")
	private String linkPhone;
	@ApiModelProperty(value = "备注")
	private String remark;
	@ApiModelProperty(value = "开户银行")
	private String financeBank;
	@ApiModelProperty(value = "账户名称")
	private String financeName;
	@ApiModelProperty(value = "银行账号")
	private String financeAccount;
	@ApiModelProperty(value = "纳税人识别号")
	private String taxId;
	@ApiModelProperty(value = "财务地址")
	private String financeAddress;
	@ApiModelProperty(value = "财务电话")
	private String financePhone;
	@ApiModelProperty(value = "账期（天）")
	private String accountPeriod;
	@ApiModelProperty(value = "每月几号结算")
	private String paymentDays;


	@ApiModelProperty(value = "简称")
	private String shortName;

	private Long typeId;

	private String tags;
	/**
	 * 是否签署诚信廉洁协议
	 */
	private Boolean honestStatus;
	/**
	 * 协议编号
	 */
	private String honestNo;
	/**
	 * 协议签订日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date honestStartDate;
	/**
	 * 协议有效期至
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date honestEndDate;
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	private Date applyTime;

	private Boolean blacklist;
	/**
	 * 访问控制
	 */
	private String acl;

	@ApiModelProperty("用友同步状态")
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private List<Map<String, Object>> yonyouSync;
	@ApiModelProperty("统一社会信用代码")
	private String usci;
	@ApiModelProperty("付款方式")
	private String payType;
	@ApiModelProperty("发票类型")
	private String bill;

	@ApiModelProperty("财务账号")
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private List<Map<String, Object>> finance;

	@TableField(exist = false)
	private List<SupplierContact> contacts;
	@TableField(exist = false)
	private List<SupplierDept> depts;
	private Long oldId;

	private Boolean sync;

	@ExcelProperty("经营范围")
	private String scope;
}
