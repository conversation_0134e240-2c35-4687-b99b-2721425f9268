<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.product.mapper.ProductPalletMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="productPalletResultMap" type="com.natergy.ni.product.vo.ProductPalletVO">
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="base_length" property="baseLength"/>
        <result column="base_width" property="baseWidth"/>
        <result column="base_height" property="baseHeight"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectProductPalletPage" resultMap="productPalletResultMap">
        select * from ni_product_pallet where is_deleted = 0
    </select>


</mapper>
