/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.product.entity;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 产品管理-托盘管理 实体类
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@TableName("ni_product_pallet")
@ApiModel(value = "ProductPallet对象", description = "产品管理-托盘管理")
@EqualsAndHashCode(callSuper = true)
public class ProductPalletEntity extends TenantEntity {

	private static final long serialVersionUID = 7177575014235327394L;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 长
	 */
	@ApiModelProperty(value = "长")
	private Integer baseLength;
	/**
	 * 宽
	 */
	@ApiModelProperty(value = "宽")
	private Integer baseWidth;
	/**
	 * 高
	 */
	@ApiModelProperty(value = "高")
	private Integer baseHeight;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	@TableField(condition = SqlCondition.LIKE)
	private String name;
	/**
	 *
	 */
	@TableField(condition = SqlCondition.LIKE)
	@ApiModelProperty(value = "")
	private String code;

	private String box;

}
