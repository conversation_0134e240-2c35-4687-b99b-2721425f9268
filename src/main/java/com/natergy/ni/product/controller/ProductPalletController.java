/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.product.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.product.entity.ProductPalletEntity;
import com.natergy.ni.product.vo.ProductPalletVO;
import com.natergy.ni.product.wrapper.ProductPalletWrapper;
import com.natergy.ni.product.service.IProductPalletService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 产品管理-托盘管理 控制器
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("ni/product/pallet")
@Api(value = "产品管理-托盘管理", tags = "产品管理-托盘管理接口")
public class ProductPalletController extends BladeController {

	private final IProductPalletService productPalletService;

	/**
	 * 产品管理-托盘管理 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入productPallet")
	public R<ProductPalletVO> detail(ProductPalletEntity productPallet) {
		ProductPalletEntity detail = productPalletService.getOne(Condition.getQueryWrapper(productPallet));
		return R.data(ProductPalletWrapper.build().entityVO(detail));
	}
	/**
	 * 产品管理-托盘管理 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入productPallet")
	public R<IPage<ProductPalletVO>> list(ProductPalletEntity productPallet, Query query) {
		IPage<ProductPalletEntity> pages = productPalletService.page(Condition.getPage(query), Condition.getQueryWrapper(productPallet));
		return R.data(ProductPalletWrapper.build().pageVO(pages));
	}

	/**
	 * 产品管理-托盘管理 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入productPallet")
	public R<IPage<ProductPalletVO>> page(ProductPalletVO productPallet, Query query) {
		IPage<ProductPalletVO> pages = productPalletService.selectProductPalletPage(Condition.getPage(query), productPallet);
		return R.data(pages);
	}

	/**
	 * 产品管理-托盘管理 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入productPallet")
	public R save(@Valid @RequestBody ProductPalletEntity productPallet) {
		return R.status(productPalletService.save(productPallet));
	}

	/**
	 * 产品管理-托盘管理 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入productPallet")
	public R update(@Valid @RequestBody ProductPalletEntity productPallet) {
		return R.status(productPalletService.updateById(productPallet));
	}

	/**
	 * 产品管理-托盘管理 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入productPallet")
	public R submit(@Valid @RequestBody ProductPalletEntity productPallet) {
		return R.status(productPalletService.saveOrUpdate(productPallet));
	}

	/**
	 * 产品管理-托盘管理 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(productPalletService.deleteLogic(Func.toLongList(ids)));
	}


}
