package com.natergy.ni.pa.service.impl;

import com.natergy.ni.pa.entity.PaWorkSummaryEntity;
import com.natergy.ni.pa.mapper.PaWorkSummaryMapper;
import com.natergy.ni.pa.service.IPaWorkSummaryService;
import com.natergy.ni.pa.vo.PaWorkSummaryVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PaWorkSummaryServiceImpl extends BaseServiceImpl<PaWorkSummaryMapper, PaWorkSummaryEntity> implements IPaWorkSummaryService {
	@Override
	public List<PaWorkSummaryVO> selectWorkSummaryPage(PaWorkSummaryVO workSummary) {
		return baseMapper.selectWorkSummaryPage(workSummary);
	}
}
