package com.natergy.ni.pa.wrapper;

import com.natergy.ni.pa.entity.PaWorkSummaryEntity;
import com.natergy.ni.pa.vo.PaWorkSummaryVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

public class PaWorkSummaryWrapper extends BaseEntityWrapper<PaWorkSummaryEntity, PaWorkSummaryVO> {
	public static PaWorkSummaryWrapper build() {
		return new PaWorkSummaryWrapper();
	}

	@Override
	public PaWorkSummaryVO entityVO(PaWorkSummaryEntity entity) {
		PaWorkSummaryVO workSummaryVO = Objects.requireNonNull(BeanUtil.copy(entity, PaWorkSummaryVO.class));
		return workSummaryVO;
	}
}
