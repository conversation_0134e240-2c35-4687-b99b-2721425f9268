package com.natergy.ni.pa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("ni_pa_work_summary")
@ApiModel(value = "PaWorkSummaryEntity对象", description = "补卡日志")
@EqualsAndHashCode(callSuper = true)
public class PaWorkSummaryEntity extends TenantEntity {
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 工作天数
	 */
	private BigDecimal attDays;
	/**
	 * 加班总时长
	 */
	private BigDecimal overtimeTotal;
	/**
	 * 加班工时
	 */
	private BigDecimal overTimeDays;
	/**
	 * 月份
	 */
	private String month;
	/**
	 * 总工时
	 */
	private BigDecimal totalDays;

	/**
	 * 考核金额
	 */
	private BigDecimal totalAmount;
	/**
	 * 考核时薪
	 */
	private Integer hourAmount;

	/**
	 * 扣款
	 */
	private BigDecimal deduction;

	/**
	 * 是否新员工
	 */
	private Boolean isNewEmployee;

	/**
	 * 入职时间
	 */
	private Date entryTime;
}
