<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.pa.mapper.PaWorkSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="repairRecordResultMap" type="com.natergy.ni.pa.vo.PaWorkSummaryVO">
    </resultMap>


    <select id="selectWorkSummaryPage"  resultType="com.natergy.ni.pa.vo.PaWorkSummaryVO">
        select npws.*,bu.dept_id from ni_pa_work_summary npws
        LEFT JOIN blade_user bu ON bu.id = npws.user_id
        where npws.is_deleted = 0
        <if test="userIds != null">
            and npws.user_id IN
                <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        <if test="deptIds != null">
            and bu.dept_id IN
            <foreach collection="deptIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="month != null">
            and npws.month = #{month}
        </if>
    </select>


</mapper>
