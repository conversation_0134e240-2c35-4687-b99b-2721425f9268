package com.natergy.ni.pa.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.natergy.ni.pa.entity.PaWorkSummaryEntity;
import com.natergy.ni.pa.vo.PaWorkSummaryVO;

import java.util.List;

public interface PaWorkSummaryMapper extends BaseMapper<PaWorkSummaryEntity> {

	/**
	 * 自定义分页
	 */
	List<PaWorkSummaryVO> selectWorkSummaryPage(PaWorkSummaryVO paWorkSummaryVO);
}
