package com.natergy.ni.por.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.natergy.ni.por.dto.AppPorOrderArrivalDTO;
import com.natergy.ni.por.service.PorOrderArrivalService;
import com.natergy.ni.por.vo.AppPorOrderArrivalVO;
import com.natergy.ni.por.vo.PorOrderArrivalVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springblade.common.cache.CacheNames;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ni/por/order/app/arrival")
@Api(value = "App采购订单到货情况", tags = "采购管理")
public class OrderArrivalAppController {

	private final PorOrderArrivalService porOrderArrivalService;
	private final BladeRedis bladeRedis;


	@GetMapping("/page")
	public R page(AppPorOrderArrivalDTO porOrderArrivalDTO, Query query){
		IPage<PorOrderArrivalVO> page = porOrderArrivalService.getAppList(porOrderArrivalDTO, Condition.getPage(query));
		List<PorOrderArrivalVO> list = page.getRecords();
		bladeRedis.setEx(CacheNames.APP_GOODS_ACCEPTANCE_LIST_KEY + porOrderArrivalDTO.getApplyUserId(),list, Duration.ofMinutes(30));
		return R.data(page);
	}

	@PostMapping("/getArrivalInformation")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "查询采购详情", notes = "传入ids")
	public R getArrivalInformation(@RequestParam List<Long> ids,Long userId) {
		List<PorOrderArrivalVO> list = bladeRedis.get(CacheNames.APP_GOODS_ACCEPTANCE_LIST_KEY + userId);
		if (list == null){
			return R.fail("请退出重试");
		}
		List<PorOrderArrivalVO> filteredList = list.stream()
			.filter(item -> ids.contains(item.getId()))
			.collect(Collectors.toList());
		return R.data(filteredList);
	}







}
