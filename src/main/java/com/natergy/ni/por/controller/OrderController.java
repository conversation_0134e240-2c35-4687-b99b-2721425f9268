/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.por.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.google.common.base.Preconditions;
import com.natergy.ni.base.entity.ContractEntity;
import com.natergy.ni.base.entity.SerialNo;
import com.natergy.ni.base.entity.SupplierInfo;
import com.natergy.ni.base.service.IContractService;
import com.natergy.ni.base.service.ISerialNoService;
import com.natergy.ni.base.service.ISupplierInfoService;
import com.natergy.ni.base.vo.ContractVO;
import com.natergy.ni.common.constant.NiConstant;
import com.natergy.ni.depot.constant.DepotConstant;
import com.natergy.ni.depot.entity.DepotBill;
import com.natergy.ni.depot.entity.DepotBillItem;
import com.natergy.ni.depot.service.DepotBillItemService;
import com.natergy.ni.depot.service.DepotBillService;
import com.natergy.ni.fin.entity.PayableApplyEntity;
import com.natergy.ni.fin.service.IPayableApplyService;
import com.natergy.ni.por.dto.UnOrderItemQuery;
import com.natergy.ni.por.entity.PorOrder;
import com.natergy.ni.por.entity.PorOrderArrival;
import com.natergy.ni.por.entity.PorOrderItem;
import com.natergy.ni.por.service.IPorOrderItemService;
import com.natergy.ni.por.service.IPorOrderService;
import com.natergy.ni.por.service.PorOrderArrivalService;
import com.natergy.ni.por.vo.PorOrderArrivalVO;
import com.natergy.ni.por.vo.PorOrderItemUnionCostItemVO;
import com.natergy.ni.por.vo.PorOrderItemVO;
import com.natergy.ni.por.vo.PorOrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.enums.DictBizEnum;
import org.springblade.common.log.LogOptContext;
import org.springblade.common.log.LogOptRecord;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.ComparableResult;
import org.springblade.common.utils.ConditionEx;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.User;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 采购订单
 * </p>
 * 订单详情根据申请列表生成，重复的生成多条，以便于和采购申请对应
 *
 * <AUTHOR>
 * @since 2022-09-17
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/por/order")
@Api(value = "采购订单", tags = "采购管理")
public class OrderController extends BladeController {

	public static final String MODULE = "ni_por_order";

	private final IPorOrderService porOrderService;
	private final IPorOrderItemService porOrderItemService;

	private final PorOrderArrivalService porOrderArrivalService;

	private final DepotBillItemService depotBillItemService;
	private final DepotBillService depotBillService;
	private final IPayableApplyService payableApplyService;

	private final IAttachService attachService;
	private final ISerialNoService serialNoService;

	private final ISupplierInfoService supplierInfoService;
	private final IContractService contractService;
	//合同采购订单编码
	public static final String CONTRACT_MODULE = "ni_base_contract_por";

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入porOrder")
	public R<PorOrderVO> detail(PorOrderVO porOrder) {
		PorOrderVO res = porOrderService.getOne(porOrder);
		BigDecimal amount = BigDecimal.ZERO;
		List<PorOrderItemVO> items = porOrderItemService.listByOrderId(res.getId());
		res.setItems(items);
		if (items != null && !items.isEmpty()) {
			amount = items.stream().map(PorOrderItemVO::getAmount)
				.filter(Objects::nonNull).reduce(amount, BigDecimal::add);
			//到货数量
			List<PorOrderArrival> arrivalItems = porOrderArrivalService.itemsByOrderId(res.getId());
			if (arrivalItems != null && !arrivalItems.isEmpty()) {
				Map<Long, BigDecimal> arrivalItemMap = arrivalItems.stream()
					.filter(item -> item.getOrderItemId() != null).collect(
						Collectors.groupingBy(PorOrderArrival::getOrderItemId,
							Collectors.reducing(BigDecimal.ZERO,
								item -> BigDecimal.valueOf(item.getArrivalNum()),
								BigDecimal::add)));
				items.forEach(item -> {
					if (arrivalItemMap.get(item.getId()) != null) {
						item.setArrivalNum(arrivalItemMap.get(item.getId()).doubleValue());
					}
				});
			}
		}
		res.setAmount(amount);

		if (StringUtils.isNotBlank(res.getAttach())) {
			List<Attach> attach = attachService.listByIds(Func.toLongList(",", res.getAttach()));
			res.setAttaches(attach);
		}
		return R.data(res);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入porOrder")
	public R<List<PorOrderVO>> list(PorOrderVO porOrder) {
		List<PorOrderVO> list = porOrderService.selectPorOrderList(porOrder);
		return R.data(list);
	}

	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入porOrder")
	public R<IPage<PorOrderVO>> page(PorOrderVO porOrder, Query query) {
		IPage<PorOrderVO> res = porOrderService.selectPorOrderPage(Condition.getPage(query),
			porOrder);
		//1.预付款检查付款申请的付款状态
		List<Long> ids = res.getRecords().stream()
			.filter(order -> order.getPayType().equals(PorOrder.PAY_TYPE_ADVANCE))
			.map(PorOrderVO::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			List<PayableApplyEntity> payableApplies = payableApplyService.list(
				Wrappers.<PayableApplyEntity>lambdaQuery().eq(PayableApplyEntity::getType,
					PayableApplyEntity.TYPE_ADVANCE).in(PayableApplyEntity::getPorOrderId, ids).in(
					PayableApplyEntity::getStatus, WfEntity.STATUS_SUBMIT, WfEntity.STATUS_APPROVAL,
					WfEntity.STATUS_REJECT, WfEntity.STATUS_FINISH));
			if (payableApplies != null && !payableApplies.isEmpty()) {
				Map<Long, List<PayableApplyEntity>> payableMap = payableApplies.stream()
					.collect(Collectors.groupingBy(PayableApplyEntity::getPorOrderId));
				res.getRecords().forEach(order -> {
					if (order.getPayType().equals(PorOrder.PAY_TYPE_ADVANCE)) {
						List<PayableApplyEntity> as = payableMap.get(order.getId());
						Integer payState = PorOrder.PAY_STATE_UN;
						if (as != null) {
							boolean allUn = as.stream().allMatch(
								apply -> apply.getPayState() == null || apply.getPayState()
									.equals(PayableApplyEntity.PAY_STATE_UN));
							boolean finish = as.stream().allMatch(
								apply -> apply.getPayState() != null && apply.getPayState()
									.equals(PayableApplyEntity.PAY_STATE_FINISH));
							if (finish) {
								payState = PorOrder.PAY_STATE_FINISH;
							} else if (!allUn) {
								payState = PorOrder.PAY_STATE_PART;
							}
							order.setPayState(payState);
						}
					}
				});
			}
		}
		//2.账期
		//3.支付宝
		//把已经终止、撤销、驳回...的关联合同合同编号置空
		List<PorOrderVO> porOrderVOList = res.getRecords();

		for (PorOrderVO porOrderVO : porOrderVOList) {
			QueryWrapper<ContractEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(ContractEntity::getId, porOrderVO.getContractId())
				.in(ContractEntity::getStatus, ContractEntity.STATUS_REJECT,
					ContractEntity.STATUS_CANCEL,
					ContractEntity.STATUS_TERMINATION);

			ContractEntity contractEntity = contractService.getOne(queryWrapper);
			//采购订单关联的合同信息置空
			if (contractEntity != null) {
				UpdateWrapper<PorOrder> updateWrapper = new UpdateWrapper<>();
				updateWrapper.lambda().eq(PorOrder::getContractId, contractEntity.getId())
					.set(PorOrder::getContractSerialNo, null)
					.set(PorOrder::getContractId, null);
				porOrderService.update(updateWrapper);
			}
		}
		return R.data(res);
	}


	/**
	 * 新增
	 */
	@LogOptRecord(module = MODULE, businessId = "{#porOrder.id}", context = "数据新增:{#status}")
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入porOrderItem")
	public R save(@Valid @RequestBody PorOrderVO porOrder) {
		LogOptContext.putVariable("status", getStatusName(porOrder));
		//当状态是已审核时，生成项目编号
		if (porOrder.getStatus() != null && porOrder.getStatus().equals(NiConstant.STATUS_SUBMIT)) {
			QueryWrapper<SerialNo> wrapper = new QueryWrapper<>();
			wrapper.lambda().eq(SerialNo::getCode, MODULE);
			SerialNo no = serialNoService.getOne(wrapper);
			porOrder.setSerialNo(serialNoService.gen(no));
		}
		if (porOrder.getBillState() == null) {
			porOrder.setBillState(PorOrder.BILL_STATE_UN);
		}
		return R.status(porOrderService.saveOrUpdate(porOrder));
	}

	private java.lang.String getStatusName(BaseEntity entity) {
		return DictBizCache.getValue(DictBizEnum.NI_DATA_STATUS,
			String.valueOf(entity.getStatus()));
	}

	/**
	 * 修改
	 */
	@LogOptRecord(module = MODULE, businessId = "{#porOrder.id}", context = "数据修改:{#change};修改前数据=>{#old}")
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入porOrderItem")
	public R update(@Valid @RequestBody PorOrderVO porOrder) {
		//当状态是已审核时，生成项目编号
		if (porOrder.getStatus() != null && porOrder.getStatus().equals(NiConstant.STATUS_SUBMIT)
			&& StringUtils.isBlank(porOrder.getSerialNo())) {
			porOrder.setSerialNo(serialNoService.gen(MODULE));
		}
		PorOrderVO query = new PorOrderVO();
		query.setId(porOrder.getId());
		PorOrderVO old = porOrderService.getOne(query);
		LogOptContext.putVariable("old", old);
		List<ComparableResult> list = CommonUtil.compareInstance(old, porOrder);
		StringBuilder change = new StringBuilder();
		list.forEach(res -> {
			if (!"status".equals(res.getFieldName())) {
				change.append(
						StringUtils.isNotBlank(res.getFieldPropertyName()) ? res.getFieldPropertyName()
							+ ":" + res.getFieldName() : res.getFieldName()).append(":")
					.append(res.getFieldContent()).append("=>").append(res.getNewFieldContent())
					.append(",");
			}
		});
		if (!old.getStatus().equals(porOrder.getStatus())) {
			change.append("状态:").append(getStatusName(old)).append("=>")
				.append(getStatusName(porOrder)).append(",");
		}
		if (porOrder.getBillState() == null) {
			porOrder.setBillState(PorOrder.BILL_STATE_UN);
		}
		LogOptContext.putVariable("change", change.toString());
		return R.status(porOrderService.saveOrUpdate(porOrder));
	}

	/**
	 * 提交该订单
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "提交该订单", notes = "传入ids")
	public R submit(@Valid @RequestParam String ids,
		@RequestParam(required = false, defaultValue = "false") Boolean contractFinish) {
		return R.status(porOrderService.submit(MODULE, Func.toLongList(ids), contractFinish));
	}


	/**
	 * 删除
	 */
	@LogOptRecord(module = MODULE, businessId = "{#ids}", context = "数据删除")
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		//是否到货
		long arrivalNum = porOrderArrivalService.count(Wrappers.lambdaQuery(PorOrderArrival.class)
			.in(PorOrderArrival::getOrderId, Func.toLongList(ids)));
		if (arrivalNum > 0) {
			throw new ServiceException("订单中存在已到货的订单，无法删除");
		}
		//是否发起付款申请
		long payableApplyNum = payableApplyService.count(
			Wrappers.lambdaQuery(PayableApplyEntity.class)
				.eq(PayableApplyEntity::getType, PayableApplyEntity.TYPE_ADVANCE)
				.in(PayableApplyEntity::getPorOrderId, Func.toLongList(ids))
				.in(PayableApplyEntity::getStatus, WfEntity.STATUS_SUBMIT, WfEntity.STATUS_APPROVAL,
					WfEntity.STATUS_FINISH));
		if (payableApplyNum > 0) {
			throw new ServiceException("订单中存在已发起付款申请的订单，无法删除");
		}
		return R.status(porOrderService.remove(Func.toLongList(ids)));
	}

	@PostMapping("/register")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "合同登记", notes = "合同登记")
	public R register(@RequestBody Map<String, Object> params) {
		Long id = Long.valueOf((String) params.get("id"));
		Boolean level = (Boolean) params.get("level");
		String attachIds = (String) params.get("attach");
		return R.status(porOrderService.registerById(id, level, attachIds));
	}

	/**
	 * 提交该订单
	 */
	@PostMapping("/toVoid")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "提交该订单", notes = "传入ids")
	public R toVoid(@Valid @RequestParam String ids) {
		//是否到货
		long arrivalNum = porOrderArrivalService.count(Wrappers.lambdaQuery(PorOrderArrival.class)
			.in(PorOrderArrival::getOrderId, Func.toLongList(ids)));
		if (arrivalNum > 0) {
			throw new ServiceException("订单中存在已到货的订单，无法作废");
		}
		//是否发起付款申请
		long payableApplyNum = payableApplyService.count(Wrappers.<PayableApplyEntity>query()
			.eq("isnull(to_void,0)", false).lambda()
			.eq(PayableApplyEntity::getType, PayableApplyEntity.TYPE_ADVANCE)
			.eq(PayableApplyEntity::getType, PayableApplyEntity.TYPE_ADVANCE)
			.in(PayableApplyEntity::getPorOrderId, Func.toLongList(ids))
			.in(PayableApplyEntity::getStatus, WfEntity.STATUS_SUBMIT, WfEntity.STATUS_APPROVAL,
				WfEntity.STATUS_FINISH));
		if (payableApplyNum > 0) {
			throw new ServiceException("订单中存在已发起付款申请的订单，无法作废");
		}
		return R.status(porOrderService.toVoid(Func.toLongList(ids)));
	}

	/**
	 *
	 */
	@GetMapping("/itemsUnionCostItemsPage")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "详情和费用详情分页", notes = "传入porApply")
	public R<IPage<PorOrderItemUnionCostItemVO>> itemsPage(
		@Valid PorOrderItemUnionCostItemVO porOrder, Query query) {
		IPage<PorOrderItemUnionCostItemVO> pages = porOrderService.selectItemsPage(
			ConditionEx.getPage(query), porOrder);
		List<Long> itemIds = pages.getRecords().stream()
			.filter(item -> "1".equals(item.getItemType())).map(PorOrderItemUnionCostItemVO::getId)
			.collect(Collectors.toList());
		if (!itemIds.isEmpty()) {
			List<PorOrderArrivalVO> arrivalVOS = porOrderArrivalService.listByOrderItemIds(itemIds);
			Map<Long, List<PorOrderArrivalVO>> arrivalMap = arrivalVOS.stream()
				.collect(Collectors.groupingBy(PorOrderArrivalVO::getOrderItemId));
			pages.getRecords().forEach(item -> item.setArrivalData(arrivalMap.get(item.getId())));
		}
		return R.data(pages);
	}


	/**
	 * 修改供应商信息
	 */
	@PostMapping("/linkContract")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "关联合同", notes = "传入ids")
	public R linkContract(@Valid @RequestParam String ids, @RequestParam Long contractId) {
		return R.status(porOrderService.linkContract(Func.toLongList(ids), contractId));
	}


	/**
	 * 修改供应商信息
	 */
	@PostMapping("/cancel")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "取消订单", notes = "传入ids")
	public R cancel(@RequestBody List<PorOrderItemUnionCostItemVO> dataList) {
		//已签合同，已到货，已付款申请，已付款的数据都无法取消
		Set<Long> orderIds = dataList.stream().map(PorOrderItemUnionCostItemVO::getOrderId)
			.collect(Collectors.toSet());
		List<PorOrder> orders = porOrderService.listByIds(orderIds);
		for (PorOrder order : orders) {
			if (order.getContractId() != null) {
				ContractEntity contract = contractService.getById(order.getContractId());
				if (Arrays.asList(WfEntity.STATUS_SUBMIT, WfEntity.STATUS_REJECT,
					WfEntity.STATUS_APPROVAL,
					WfEntity.STATUS_FINISH).contains(contract.getStatus())) {
					throw new ServiceException("已关联合同的订单无法取消");
				}
			}
		}
		Set<Long> itemIds = dataList.stream()
			.map(PorOrderItemUnionCostItemVO::getId).collect(Collectors.toSet());
		if (!itemIds.isEmpty()) {
			long arrivalCount = porOrderArrivalService.count(Wrappers.<PorOrderArrival>lambdaQuery()
				.in(PorOrderArrival::getOrderItemId, itemIds)
				//作废的数据不算
				.eq(PorOrderArrival::getStatus, NiConstant.STATUS_SUBMIT));
			if (arrivalCount > 0) {
				throw new ServiceException("已到货的数据无法取消");

			}
//      long count = porOrderItemService.count(
//          Wrappers.<PorOrderItem>lambdaQuery().in(PorOrderItem::getId, itemIds).and(
//              w -> w.eq(PorOrderItem::getPayApplyState, PorOrder.PAY_APPLY_STATE_FINISH).or()
//                  .eq(PorOrderItem::getPayState, PorOrder.PAY_STATE_FINISH)));
//      if (count > 0) {
//        throw new ServiceException("已申请付款/已付款的数据无法取消");
//
//      }
		}
		return R.status(porOrderService.cancel(dataList));
	}

	/**
	 * 修改付款方式
	 */
	@PostMapping("/changePayType")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改付款方式", notes = "传入ids")
	public R changePayType(@Valid @RequestParam String ids, @RequestParam String payType) {
		return R.status(porOrderService.changePayType(Func.toLongList(ids), payType));
	}

	@PostMapping("/buildContract")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "创建合同并关联", notes = "传入ids")
	public R buildContract(@Valid @RequestBody ContractVO contract) {
		return R.status(porOrderService.buildContract(contract));
	}

	/**
	 * 修改付款方式
	 */
	@PostMapping("/changeBillType")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改付款方式", notes = "传入ids")
	public R changeBillType(@RequestBody Map<String, Object> body) {
		List<Long> ids = Func.toLongList((String) body.get("ids"));
		String billType = (String) body.get("billType");
		BigDecimal taxRate;
		if (body.get("taxRate") != null) {
			taxRate = new BigDecimal(String.valueOf(body.get("taxRate")));
		} else {
			taxRate = BigDecimal.ZERO;
		}
		return R.status(porOrderService.update(
			Wrappers.<PorOrder>lambdaUpdate().in(PorOrder::getId, ids)
				.set(PorOrder::getBillType, billType).set(PorOrder::getTaxRate, taxRate)));
	}

	@PostMapping("/splitNum")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "拆分数量", notes = "传入ids")
	public R splitNum(@RequestParam Long id, Double num) {
		PorOrderItem item = porOrderItemService.getById(id);
		Preconditions.checkArgument(item != null, "订单明细查询失败");
		if (item.getAmount() == null) {
			item.setAmount(BigDecimal.ZERO);
		}
		List<PorOrderItem> items = porOrderItemService.list(Wrappers.<PorOrderItem>lambdaQuery()
			.eq(PorOrderItem::getApplyItemId, item.getApplyItemId()).ne(PorOrderItem::getId, id));
		BigDecimal price = item.getAmount()
			.divide(new BigDecimal(String.valueOf(item.getNum())), 2, RoundingMode.HALF_UP);
		BigDecimal amount = item.getAmount();
		double leftNum = item.getNum() - num;
		Preconditions.checkArgument(leftNum > 0, "拆分数大于原订单数，请重新操作");
		item.setNum(leftNum);
		item.setAmount(price.multiply(new BigDecimal(leftNum)).setScale(2, RoundingMode.HALF_UP));
		items.add(item);
		PorOrderItem newItem = new PorOrderItem();
		BeanUtil.copyProperties(item, newItem);
		newItem.setId(null);
		newItem.setNum(num);
		newItem.setAmount(amount.subtract(item.getAmount()));
		newItem.setNo(null);
		//2024-04-29跟采购孙凯沟通，拆分订单时，拆分的订单去除掉到货状态和到货描述，暂存位置
		newItem.setDepotLocation(null);
		newItem.setReceive(null);
		newItem.setReceiveRemark(null);
		items.add(newItem);
		AtomicReference<Integer> lastNo = new AtomicReference<>(
			items.stream().map(PorOrderItem::getNo)
				.filter(Objects::nonNull).max(Comparator.comparing(Integer::valueOf))
				.orElse(0));
		items.stream().filter(i -> i.getNo() == null).forEach(i -> {
			lastNo.set(lastNo.get() + 1);
			i.setNo(lastNo.get());
		});
		return R.status(porOrderItemService.saveOrUpdateBatch(items));
	}

	@PostMapping("/splitNumAndOrder")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "拆分数量并生成新订单", notes = "传入ids")
	public R splitNumAndOrder(@RequestParam Long id, Double num) {
		PorOrderItem item = porOrderItemService.getById(id);
		Preconditions.checkArgument(item != null, "订单明细查询失败");
		Preconditions.checkArgument(item.getOrderId() != null, "订单明细未关联订单！");
		if (item.getAmount() == null) {
			item.setAmount(BigDecimal.ZERO);
		}
		List<PorOrderItem> items = porOrderItemService.list(Wrappers.<PorOrderItem>lambdaQuery()
			.eq(PorOrderItem::getApplyItemId, item.getApplyItemId()).ne(PorOrderItem::getId, id));
		BigDecimal price = item.getAmount()
			.divide(new BigDecimal(String.valueOf(item.getNum())), 2, RoundingMode.HALF_UP);
		BigDecimal amount = item.getAmount();
		double leftNum = item.getNum() - num;
		Preconditions.checkArgument(leftNum > 0, "拆分数大于原订单数，请重新操作");
		item.setNum(leftNum);
		item.setAmount(price.multiply(new BigDecimal(leftNum)).setScale(2, RoundingMode.HALF_UP));
		items.add(item);
		PorOrderItem newItem = new PorOrderItem();
		BeanUtil.copyProperties(item, newItem);
		newItem.setId(null);
		newItem.setNum(num);
		newItem.setAmount(amount.subtract(item.getAmount()));
		newItem.setNo(null);
		//2024-04-29跟采购孙凯沟通，拆分订单时，拆分的订单去除掉到货状态和到货描述，暂存位置
		newItem.setDepotLocation(null);
		newItem.setReceive(null);
		newItem.setReceiveRemark(null);
		items.add(newItem);
		PorOrder newOrder = buildNewOrder(item.getOrderId());
		porOrderService.save(newOrder);
		newItem.setOrderId(newOrder.getId());
		AtomicReference<Integer> lastNo = new AtomicReference<>(
			items.stream().map(PorOrderItem::getNo)
				.filter(Objects::nonNull).max(Comparator.comparing(Integer::valueOf))
				.orElse(0));
		items.stream().filter(i -> i.getNo() == null).forEach(i -> {
			lastNo.set(lastNo.get() + 1);
			i.setNo(lastNo.get());
		});
		return R.status(porOrderItemService.saveOrUpdateBatch(items));
	}

	private PorOrder buildNewOrder(Long orderId) {
		PorOrder order = porOrderService.getById(orderId);
		PorOrder newOrder = new PorOrder();
		BeanUtil.copyProperties(order, newOrder);
		newOrder.setId(null);
		newOrder.setSerialNo(serialNoService.gen(MODULE));
		newOrder.setBillState(PorOrder.BILL_STATE_UN);
		newOrder.setContractId(null);
		newOrder.setContractSerialNo(null);
		newOrder.setContract(null);
		return newOrder;
	}

	@PostMapping("/changeNum")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改数量", notes = "传入ids")
	public R changeNum(@RequestParam Long id, Double num) {
		PorOrderItem item = porOrderItemService.getById(id);
		if (item.getAmount() == null) {
			item.setAmount(BigDecimal.ZERO);
		}
		BigDecimal price = item.getAmount()
			.divide(new BigDecimal(String.valueOf(item.getNum())), 2, RoundingMode.HALF_UP);
		if (item.getMappingNum() == null) {
			item.setMappingNum(item.getNum());
		}
		item.setNum(num);
		item.setAmount(price.multiply(new BigDecimal(num)).setScale(2, RoundingMode.HALF_UP));
		return R.status(porOrderItemService.saveOrUpdate(item));
	}

	@PostMapping("/changeAmount")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改金额", notes = "传入ids")
	public R changeAmount(@RequestParam Long id, BigDecimal amount) {
		Preconditions.checkArgument(amount != null, "修改金额不能为空");
		PorOrderItem orderItem = porOrderItemService.getById(id);
		Preconditions.checkArgument(orderItem != null, "订单明细不存在");
		BigDecimal price = amount.divide(BigDecimal.valueOf(orderItem.getNum()), 2,
			RoundingMode.HALF_UP);
		List<PorOrderArrival> arrivals = porOrderArrivalService.list(
			Wrappers.<PorOrderArrival>lambdaQuery().ne(PorOrderArrival::getParentId, 0L)
				.eq(PorOrderArrival::getOrderItemId, id)
				.eq(PorOrderArrival::getStatus, NiConstant.STATUS_SUBMIT));
		if (!arrivals.isEmpty()) {
			long stockInNum = depotBillItemService.count(
				Wrappers.<DepotBillItem>lambdaQuery().in(DepotBillItem::getPorOrderArrivalId,
					arrivals.stream().map(PorOrderArrival::getId).collect(
						Collectors.toList())));
			Preconditions.checkArgument(stockInNum <= 0, "物品已入库无法修改");
//      if (!billItems.isEmpty()) {
//        if (billItems.size() == 1 && BigDecimal.valueOf(billItems.get(0).getNum())
//            .compareTo(BigDecimal.valueOf(orderItem.getNum())) == 0) {
//          billItems.get(0).setAmount(amount);
//        } else {
//          billItems.forEach(i -> {
//            i.setAmount(price.multiply(BigDecimal.valueOf(i.getNum())).setScale(2,
//                RoundingMode.HALF_UP));
//          });
//        }
//        depotBillItemService.updateBatchById(billItems);
//      }
		}
		boolean res = porOrderItemService.update(
			Wrappers.<PorOrderItem>lambdaUpdate().eq(PorOrderItem::getId, id)
				.set(PorOrderItem::getAmount, amount));
		if (res && !arrivals.isEmpty()) {
			//修改到貨單价格
			arrivals.forEach(i -> {
				if (arrivals.size() == 1 && i.getArrivalNum().equals(orderItem.getNum())) {
					i.setArrivalAmount(amount);
				} else {
					i.setArrivalAmount(
						price.multiply(BigDecimal.valueOf(i.getArrivalNum())).setScale(2,
							RoundingMode.HALF_UP));
				}
				if (i.getQualifiedNum() != null) {
					if (BigDecimal.valueOf(i.getQualifiedNum())
						.compareTo(BigDecimal.valueOf(i.getArrivalNum())) == 0) {
						i.setQualifiedAmount(i.getArrivalAmount());
					} else {
						i.setQualifiedAmount(
							price.multiply(BigDecimal.valueOf(i.getQualifiedNum())).setScale(2,
								RoundingMode.HALF_UP));
					}
				}
			});
			porOrderArrivalService.updateBatchById(arrivals);
		}
		return R.status(res);
	}

	@PostMapping("/changeRemark")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改金额", notes = "传入ids")
	public R changeRemark(@RequestParam List<Long> ids, String remark) {
		porOrderItemService.update(
			Wrappers.<PorOrderItem>lambdaUpdate().in(PorOrderItem::getId, ids)
				.set(PorOrderItem::getRemark, remark));
		return R.success("修改成功");
	}

	/**
	 * 修改供应商信息
	 */
	@PostMapping("/changePurchaseUser")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "提交该订单", notes = "传入ids")
	public R changePurchaseUser(@Valid @RequestParam String ids,
		@RequestParam Long purchaseUserId) {
		porOrderItemService.update(
			Wrappers.<PorOrderItem>lambdaUpdate().in(PorOrderItem::getId, Func.toLongList(ids))
				.set(PorOrderItem::getPurchaseUserId, purchaseUserId));
		return R.success("修改成功");
	}

	/**
	 * 修改供应商信息
	 */
	@PostMapping("/changeSupplier")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改供应商", notes = "传入ids")
	public R changeSupplier(@Valid @RequestParam String ids, @RequestParam Long supplierId) {
		//根据id获取供应商信息
		SupplierInfo supplierInfo = supplierInfoService.getById(supplierId);
		boolean res = porOrderService.update(
			Wrappers.<PorOrder>lambdaUpdate().in(PorOrder::getId, Func.toLongList(ids))
				.set(PorOrder::getSupplierId, supplierId)
				.set(PorOrder::getSupplierLinkman, supplierInfo.getLinkman())
				.set(PorOrder::getSupplierLinkPhone, supplierInfo.getLinkPhone()));
		Preconditions.checkState(res, "修改失败");
		res = porOrderItemService.update(
			Wrappers.<PorOrderItem>lambdaUpdate().in(PorOrderItem::getOrderId, Func.toLongList(ids))
				.set(PorOrderItem::getSupplierId, supplierId));
		Preconditions.checkState(res, "items修改失败");
		List<PorOrderArrival> arrivals = porOrderArrivalService.list(
			Wrappers.<PorOrderArrival>lambdaQuery()
				.in(PorOrderArrival::getOrderId, Func.toLongList(ids))
				.gt(PorOrderArrival::getParentId, 0L));
		if (!arrivals.isEmpty()) {
			List<DepotBillItem> billItems = depotBillItemService.list(
				Wrappers.<DepotBillItem>lambdaQuery()
					.in(DepotBillItem::getPorOrderArrivalId,
						arrivals.stream().map(PorOrderArrival::getId).collect(
							Collectors.toList())));
			if (!billItems.isEmpty()) {
				List<DepotBill> bills = depotBillService.listByIds(
					billItems.stream().map(DepotBillItem::getBillId).collect(
						Collectors.toSet()));
				List<Long> needUpdateIds = bills.stream()
					.filter(item -> item.getType().equals(DepotConstant.TYPE_IN))
					.map(DepotBill::getId).collect(Collectors.toList());
				res = depotBillService.update(
					Wrappers.<DepotBill>lambdaUpdate().in(DepotBill::getId, needUpdateIds)
						.set(DepotBill::getSupplierId, supplierId));
				Preconditions.checkState(res, "入库单供应商修改失败");
			}
		}
		return R.status(res);
	}

	@GetMapping("/unOrderItemList")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "未订货明细", notes = "采购订单明细中")
	public R<List<PorOrderItemVO>> unOrderItemList(@RequestParam(required = false) Long userId,
		@RequestParam(required = false) Integer pv) {
		List<PorOrderItemVO> list = porOrderService.unOrderItemList();
		if (pv != null && list != null) {
			list = list.stream().filter(item -> item.getPv() != null && item.getPv().equals(pv))
				.collect(
					Collectors.toList());
		}
		if (userId != null && list != null) {
			list = list.stream().filter(
					item -> item.getPurchaseUserId() != null && item.getPurchaseUserId().equals(userId))
				.collect(Collectors.toList());
		}
		return R.data(list);
	}

	/**
	 * 创建订单
	 */
	@PostMapping("/buildOrder")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "创建订单", notes = "传入ids")
	public R buildOrder(@RequestBody List<PorOrderItemUnionCostItemVO> dataList) {
		return R.status(porOrderService.buildOrder(dataList));
	}

	@GetMapping("/unArrivalItemList")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "未订货明细", notes = "采购订单明细中")
	public R<List<PorOrderItemVO>> unArrivalItemList(
		@RequestParam(required = false) Long purchaseUserId,
		@RequestParam(required = false) Boolean crash,
		@RequestParam(required = false) Integer pv) {
		List<PorOrderItemVO> pages = porOrderService.unArrivalItemList();
		pages = pages.stream()
			.filter(item -> (pv == null || (item.getPv() != null && item.getPv().equals(pv))))
			.filter(item -> (purchaseUserId == null || (item.getPurchaseUserId() != null
				&& item.getPurchaseUserId().equals(purchaseUserId))))
			.filter(
				item -> (crash == null || (item.getCrash() != null && item.getCrash()
					.equals(crash))))
			.sorted(Comparator
				.comparing(PorOrderItemVO::getSerialNo, Comparator.nullsFirst(String::compareTo))
				.thenComparing(PorOrderItemVO::getRow, Comparator.nullsFirst(Long::compareTo)))
			.collect(Collectors.toList());
		return R.data(pages);

	}

	@GetMapping("/purchaseUserList")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "采购人员列表", notes = "采购人员列表-方便进行权限控制")
	public R<List<User>> purchaseUserList() {
		List<User> list = porOrderService.purchaseUserList();
		return R.data(list);
	}

	@PostMapping("/changeType")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改类型", notes = "传入ids")
	public R changeType(@RequestParam List<Long> ids, String type) {
		porOrderService.update(Wrappers.<PorOrder>lambdaUpdate().in(PorOrder::getId, ids)
			.set(PorOrder::getType, type));
		return R.success("修改成功");
	}

	@GetMapping("/getAdvanceAmount")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "获取预付款", notes = "orderItemIds")
	public R<BigDecimal> getAdvanceAmount(@RequestParam List<Long> orderItemIds) {
		List<PorOrderItem> orderItems = porOrderItemService.listByIds(orderItemIds);
		if (!orderItems.isEmpty()) {
			List<Long> orderIds = orderItems.stream().map(PorOrderItem::getOrderId)
				.collect(Collectors.toList());
			List<PorOrder> orders = porOrderService.listByIds(orderIds);
			Map<Long, PorOrder> orderMap = new HashMap<>();
			if (!orders.isEmpty()) {
				orderMap.putAll(orders.stream().collect(Collectors.toMap(PorOrder::getId,
					Function.identity())));
			}
			orderItems.forEach(item -> {
				if (StringUtils.isBlank(item.getPayType())) {
					if (orderMap.get(item.getOrderId()) != null) {
						item.setPayType(orderMap.get(item.getOrderId()).getPayType());
					}
				}
			});
			BigDecimal advanceAmount = orderItems.stream()
				.filter(item -> item.getPayType() != null && item.getPayType()
					.equals(PorOrder.PAY_TYPE_ADVANCE))
				.map(PorOrderItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
			return R.data(advanceAmount);
		}
		return R.data(BigDecimal.ZERO);
	}

	@PostMapping("/generateContractSerialNo")
	@ApiOperation(value = "生成合同编号/如果已经有合同，把合同的编号拉过来", notes = "porOrderVO")
	public R generateContractSerialNo(@RequestParam Long id) {
		PorOrder order = porOrderService.getById(id);
		List<ContractEntity> contractEntities = contractService.list(
			Wrappers.<ContractEntity>lambdaQuery().eq(ContractEntity::getOrderId, id)
				.in(ContractEntity::getStatus,
					Arrays.asList(WfEntity.STATUS_SUBMIT, WfEntity.STATUS_APPROVAL,
						WfEntity.STATUS_REJECT,
						WfEntity.STATUS_FINISH)));
		if (contractEntities != null && !contractEntities.isEmpty()) {
			order.setContractSerialNo(contractEntities.get(0).getSerialNo());
		} else {
			if (StringUtils.isBlank(order.getContractSerialNo())) {
				order.setContractSerialNo(serialNoService.gen(CONTRACT_MODULE));
				porOrderService.updateById(order);
			} else {
				return R.status(false);
			}
		}

		return R.data(order);
	}


	/**
	 * 订单合并
	 */
	@PostMapping("/mergePorOrder")
	@ApiOperation(value = "订单合并", notes = "")
	public R mergePorOrder(@RequestParam Long id, @RequestBody List<PorOrderVO> porOrderVOList) {
		List<PorOrderItemVO> items = porOrderItemService.listByOrderIds(
			porOrderVOList.stream().map(PorOrderVO::getId).collect(
				Collectors.toList()));
		Set<Long> orderIds = items.stream().map(PorOrderItemVO::getOrderId)
			.filter(orderId -> !orderId.equals(id)).collect(Collectors.toSet());
		List<Long> itemIds = items.stream().map(PorOrderItemVO::getId).collect(Collectors.toList());
		porOrderItemService.update(
			Wrappers.<PorOrderItem>lambdaUpdate().in(PorOrderItem::getId, itemIds)
				.set(PorOrderItem::getOrderId, id));
		if (!orderIds.isEmpty()) {
			porOrderService.removeBatchByIds(orderIds);
		}
		return R.status(true);
	}

	@GetMapping("/v1/unOrderItemPage")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "未采购及未生成订单的明细", notes = "")
	public R<IPage<PorOrderItemVO>> unOrderItemPage(Query query,
		UnOrderItemQuery porOrderItem) {
		IPage<PorOrderItemVO> pages = porOrderItemService.unOrderItemPage(
			ConditionEx.getPage(query), porOrderItem);
		return R.data(pages);
	}
}
