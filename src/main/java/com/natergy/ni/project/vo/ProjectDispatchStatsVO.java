package com.natergy.ni.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel("工人派工统计信息")
public class ProjectDispatchStatsVO implements Serializable {

	private static final long serialVersionUID = -7041200553587971072L;
	@ApiModelProperty(value = "总人数", example = "10")
	private Integer total;

	@ApiModelProperty(value = "已派人数", example = "7")
	private Integer assigned;

	@ApiModelProperty(value = "请假人数", example = "2")
	private Integer leave;

	@ApiModelProperty(value = "年假人数", example = "1")
	private Integer annualLeave;}
