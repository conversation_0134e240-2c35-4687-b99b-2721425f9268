/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.natergy.ni.fg.entity.ShippingItemEntity;
import com.natergy.ni.fg.service.IShippingItemService;
import com.natergy.ni.fg.vo.ShippingItemVO;
import com.natergy.ni.fg.wrapper.ShippingItemWrapper;
import com.natergy.ni.product.entity.ProductSku;
import com.natergy.ni.product.service.ProductSkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 成品库管理-发货明细 控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("ni/fg/shipping-item")
@Api(value = "成品库管理-发货明细", tags = "成品库管理-发货明细接口")
public class ShippingItemController extends BladeController {

    private final IShippingItemService shippingItemService;
    private final ProductSkuService productSkuService;

    /**
     * 成品库管理-发货明细 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入shippingItem")
    public R<ShippingItemVO> detail(ShippingItemEntity shippingItem) {
        ShippingItemEntity detail = shippingItemService.getOne(
            Condition.getQueryWrapper(shippingItem));
        return R.data(ShippingItemWrapper.build().entityVO(detail));
    }

    /**
     * 成品库管理-发货明细 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入shippingItem")
    public R<IPage<ShippingItemVO>> list(ShippingItemEntity shippingItem, Query query) {
        IPage<ShippingItemEntity> pages = shippingItemService.page(Condition.getPage(query),
            Condition.getQueryWrapper(shippingItem));
        Map<Long, ProductSku> productSkuMap = new HashMap<>();
        if (!pages.getRecords().isEmpty()) {
            List<Long> skuIds = pages.getRecords().stream().map(ShippingItemEntity::getSkuId)
                .distinct()
                .collect(Collectors.toList());
            List<ProductSku> productSkus = productSkuService.listByIds(skuIds);
            productSkuMap.putAll(productSkus.stream()
                .collect(Collectors.toMap(ProductSku::getId, Function.identity())));
        }
        return R.data(pages.convert(item -> {
            ShippingItemVO shippingItemVO = Objects.requireNonNull(
                BeanUtil.copy(item, ShippingItemVO.class));
            ProductSku productSku = productSkuMap.getOrDefault(item.getSkuId(), new ProductSku());
            shippingItemVO.setSpecText(productSku.getSpecText());
            shippingItemVO.setPackageText(productSku.getPackageText());
            shippingItemVO.setInnerPackageText(productSku.getInnerPackageText());
            shippingItemVO.setQualityLevel(productSku.getQualityLevel());
            shippingItemVO.setCapacity(productSku.getCapacity());
            return shippingItemVO;
        }));
    }

    /**
     * 成品库管理-发货明细 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入shippingItem")
    public R<IPage<ShippingItemVO>> page(ShippingItemVO shippingItem, Query query) {
        IPage<ShippingItemVO> pages = shippingItemService.selectshippingItemPage(
            Condition.getPage(query), shippingItem);
        return R.data(pages);
    }

    /**
     * 成品库管理-发货明细 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入shippingItem")
    public R<Void> save(@Valid @RequestBody ShippingItemEntity shippingItem) {
        ProductSku sku = productSkuService.getById(shippingItem.getSkuId());
        shippingItem.setWeight(
            sku.getCapacity().multiply(BigDecimal.valueOf(shippingItem.getNum())));
        return R.status(shippingItemService.save(shippingItem));
    }

    /**
     * 成品库管理-发货明细 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入shippingItem")
    public R<Void> update(@Valid @RequestBody ShippingItemEntity shippingItem) {
        return R.status(shippingItemService.updateById(shippingItem));
    }

    /**
     * 成品库管理-发货明细 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入shippingItem")
    public R<Void> submit(@Valid @RequestBody ShippingItemEntity shippingItem) {
        return R.status(shippingItemService.saveOrUpdate(shippingItem));
    }

    /**
     * 成品库管理-发货明细 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R<Void> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(shippingItemService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 成品库管理-发货明细 新增
     */
    @PostMapping("/saveBatch")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入shippingItem")
    public R<Void> saveBatch(@Valid @RequestBody List<ShippingItemEntity> shippingItems) {
        List<ProductSku> productSkus = productSkuService.listByIds(
            shippingItems.stream().map(ShippingItemEntity::getSkuId).filter(Objects::nonNull)
                .collect(Collectors.toList()));
        Map<Long, ProductSku> productSkuMap = productSkus.stream()
            .collect(Collectors.toMap(ProductSku::getId, Function.identity()));
        shippingItems.forEach(item -> {
            if (item.getSkuId() != null) {
                ProductSku sku = productSkuMap.getOrDefault(item.getSkuId(), new ProductSku());
                item.setWeight(sku.getCapacity().multiply(BigDecimal.valueOf(item.getNum())));
            }
        });
        return R.status(shippingItemService.saveBatch(shippingItems));
    }
}
