/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.natergy.ni.fg.entity.ShippingEntity;
import com.natergy.ni.fg.entity.ShippingItemEntity;
import com.natergy.ni.fg.service.IShippingItemService;
import com.natergy.ni.fg.service.IShippingService;
import com.natergy.ni.fg.vo.FgOutboundVO;
import com.natergy.ni.fg.vo.ShippingItemVO;
import com.natergy.ni.fg.vo.ShippingVO;
import com.natergy.ni.fg.wrapper.ShippingWrapper;
import com.natergy.ni.old.entity.GuoWaiFaHuoEntity;
import com.natergy.ni.old.entity.XiaoShouDingDanEntity;
import com.natergy.ni.old.entity.XiaoShouDingDanMingXiEntity;
import com.natergy.ni.old.entity.XiaoShouWaiKuBuHuoEntity;
import com.natergy.ni.old.entity.XiaoShouWaiKuBuHuoMingXiEntity;
import com.natergy.ni.old.service.IGuoWaiFaHuoService;
import com.natergy.ni.old.service.IXiaoShouDingDanMingXiService;
import com.natergy.ni.old.service.IXiaoShouDingDanService;
import com.natergy.ni.old.service.IXiaoShouWaiKuBuHuoMingXiService;
import com.natergy.ni.old.service.IXiaoShouWaiKuBuHuoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.UserCache;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.User;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 成品库-发货管理 控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/ni/fg/shipping")
@Api(value = "成品库-发货管理", tags = "成品库-发货管理接口")
public class ShippingController extends BladeController {

	private final IShippingService shippingService;
	private final IShippingItemService shippingItemService;
	private final IXiaoShouDingDanService xiaoShouDingDanService;
	private final IXiaoShouDingDanMingXiService xiaoShouDingDanMingXiService;
	private final IXiaoShouWaiKuBuHuoService xiaoShouWaiKuBuHuoService;
	private final IXiaoShouWaiKuBuHuoMingXiService xiaoShouWaiKuBuHuoMingXiService;
	private final IGuoWaiFaHuoService guoWaiFaHuoService;
	private final IAttachService attachService;

	/**
	 * 成品库-发货管理 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入shipping")
	public R<ShippingVO> detail(@RequestParam Long id) {
		ShippingVO detail = shippingService.getDetailById(id);
		if (detail != null) {
			List<Attach> attaches = attachService.listByBusiness("ni_fg_shipping_zj",
				detail.getSerialNo());
			detail.setZjAttach(attaches.stream().map(attach -> {
				Map<String, Object> map = new HashMap<>();
				map.put("label", attach.getOriginalName());
				map.put("value", attach.getId() + "");
				return map;
			}).collect(Collectors.toList()));
			List<Attach> zjAttaches = attachService.listByBusiness("ni_fg_shipping_fh",
				detail.getSerialNo());
			detail.setFhAttach(zjAttaches.stream().map(attach -> {
				Map<String, Object> map = new HashMap<>();
				map.put("label", attach.getOriginalName());
				map.put("value", attach.getId() + "");
				return map;
			}).collect(Collectors.toList()));
		}
		return R.data(detail);
	}

	/**
	 * 成品库-发货管理 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入shipping")
	public R<IPage<ShippingVO>> list(ShippingEntity shipping, Query query) {
		IPage<ShippingEntity> pages = shippingService.page(Condition.getPage(query),
			Condition.getQueryWrapper(shipping));
		Map<Long, String> batchNoMap = new HashMap<>();
		Map<Long, Integer> numMap = new HashMap<>();
		Map<Long, BigDecimal> weightMap = new HashMap<>();
		if (!pages.getRecords().isEmpty()) {
			List<ShippingItemEntity> shippingItemList = shippingItemService.list(
				Wrappers.<ShippingItemEntity>lambdaQuery()
					.in(ShippingItemEntity::getShippingId,
						pages.getRecords().stream().map(ShippingEntity::getId).collect(
							Collectors.toList())));
			batchNoMap.putAll(shippingItemList.stream().collect(
				Collectors.groupingBy(ShippingItemEntity::getShippingId,
					Collectors.mapping(ShippingItemEntity::getBatchNo, Collectors.joining("/")))));
			numMap.putAll(shippingItemList.stream().collect(
				Collectors.groupingBy(ShippingItemEntity::getShippingId,
					Collectors.summingInt(ShippingItemEntity::getNum))
			));
			weightMap.putAll(shippingItemList.stream().collect(
				Collectors.groupingBy(ShippingItemEntity::getShippingId,
					Collectors.reducing(BigDecimal.ZERO, ShippingItemEntity::getWeight,
						BigDecimal::add))));
		}
		return R.data(pages.convert(s -> {
			ShippingVO shippingVO = Objects.requireNonNull(BeanUtil.copy(s, ShippingVO.class));
			User createUser = UserCache.getUser(s.getCreateUser());
			if (createUser != null) {
				shippingVO.setCreateUserName(createUser.getRealName());
			}
			User opUser = UserCache.getUser(s.getOpUserId());
			if (opUser != null) {
				shippingVO.setOpUserName(opUser.getRealName());
			}
			shippingVO.setTypeText(ShippingEntity.TYPE_MAP.get(s.getType()));
			shippingVO.setBatchNo(batchNoMap.get(s.getId()));
			shippingVO.setTotalNum(numMap.get(s.getId()));
			shippingVO.setTotalWeight(weightMap.get(s.getId()));
			return shippingVO;
		}));
	}

	/**
	 * 成品库-发货管理 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入shipping")
	public R<Long> save(@Valid @RequestBody ShippingEntity shipping) {
		if (shipping.getOpUserId() == null) {
			shipping.setOpUserId(AuthUtil.getUserId());
		}
		if (shipping.getDate() == null) {
			shipping.setDate(LocalDate.now());
		}
		Assert.isTrue(StringUtils.isNotBlank(shipping.getType()), "请选择发货类型");
		Assert.isTrue(shipping.getSourceId() != null, "请选择发货编号");
		if (ShippingEntity.TYPE_IN.equals(shipping.getType())) {
			XiaoShouDingDanEntity xiaoShouDingDan = xiaoShouDingDanService.getDetail(
				shipping.getSourceId());
			Assert.isTrue(xiaoShouDingDan != null, "请选择正确的发货编号");
			shipping.setSerialNo(xiaoShouDingDan.getFdbh());
			List<XiaoShouDingDanMingXiEntity> xiaoShouDingDanMingXiList = xiaoShouDingDanMingXiService.selectMingXiList(
				xiaoShouDingDan.getId() + "");
			Integer num = xiaoShouDingDanMingXiList.stream()
				.mapToInt(mingXi -> mingXi.getJianShu().intValue()).sum();
			shipping.setNum(num);
			shipping.setWeight(BigDecimal.valueOf(xiaoShouDingDan.getJzkg()));
			shipping.setRemark(xiaoShouDingDan.getBz());
		} else if (ShippingEntity.TYPE_OUT.equals(shipping.getType())) {
			GuoWaiFaHuoEntity guoWaiFaHuoEntity = guoWaiFaHuoService.getDetail(
				shipping.getSourceId());
			Assert.isTrue(guoWaiFaHuoEntity != null, "请选择正确的发货编号");
			shipping.setSerialNo(guoWaiFaHuoEntity.getFaHuoBianHao());
			if (StringUtils.isNotBlank(guoWaiFaHuoEntity.getZongJianShu())) {
				shipping.setNum(Integer.valueOf(guoWaiFaHuoEntity.getZongJianShu()));
			}
			shipping.setWeight(new BigDecimal(guoWaiFaHuoEntity.getJingZhong()));
			shipping.setRemark(guoWaiFaHuoEntity.getBeiZhu());
		} else if (ShippingEntity.TYPE_REPLENISH.equals(shipping.getType())) {
			XiaoShouWaiKuBuHuoEntity xiaoShouWaiKuBuHuo = xiaoShouWaiKuBuHuoService.getDetail(
				shipping.getSourceId());
			Assert.isTrue(xiaoShouWaiKuBuHuo != null, "请选择正确的发货编号");
			shipping.setSerialNo(xiaoShouWaiKuBuHuo.getFaHuoBianHao());
			List<XiaoShouWaiKuBuHuoMingXiEntity> xiaoShouWaiKuBuHuoMingXiList = xiaoShouWaiKuBuHuoMingXiService.selectMingXiList(
				xiaoShouWaiKuBuHuo.getDingDanHao());
			Integer num = xiaoShouWaiKuBuHuoMingXiList.stream()
				.mapToInt(mingXi -> mingXi.getJianShu().intValue()).sum();
			shipping.setNum(num);
			shipping.setWeight(BigDecimal.valueOf(xiaoShouWaiKuBuHuo.getJingZhongKg()));
			shipping.setRemark(xiaoShouWaiKuBuHuo.getDingDanBeiZhu());
		}
		long existNum = shippingService.count(Wrappers.<ShippingEntity>lambdaQuery()
			.eq(ShippingEntity::getSourceId, shipping.getSourceId())
			.eq(ShippingEntity::getType, shipping.getType()));
		Assert.isTrue(existNum == 0, "该发货编号已存在:" + shipping.getSerialNo());
		shippingService.save(shipping);
		return R.data(shipping.getId());
	}

	/**
	 * 成品库-发货管理 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入shipping")
	public R update(@Valid @RequestBody ShippingEntity shipping) {
		return R.status(shippingService.updateById(shipping));
	}

	/**
	 * 成品库-发货管理 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入shipping")
	public R submit(@Valid @RequestBody ShippingEntity shipping) {
		return R.status(shippingService.saveOrUpdate(shipping));
	}

	/**
	 * 成品库-发货管理 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		Assert.isTrue(shippingService.count(Wrappers.<ShippingEntity>lambdaQuery()
				.in(ShippingEntity::getId, Func.toLongList(ids))
				.ne(ShippingEntity::getStatus, ShippingEntity.STATUS_WAIT)) == 0,
			"存在已出库的发货单！");
		return R.status(shippingService.remove1(Func.toLongList(ids)));
	}

	@GetMapping("/loadItemMaterial")
	public R<List<ShippingItemVO>> loadItemMaterial(@RequestParam Long id) {
		List<ShippingItemVO> res = shippingService.loadItemMaterialByShippingId(id);
		return R.data(res);
	}

	@PostMapping("/audit")
	public R<List<FgOutboundVO>> audit(@RequestParam Long id) {
		List<FgOutboundVO> res = shippingService.audit(id);
		return R.data(res);
	}
}
