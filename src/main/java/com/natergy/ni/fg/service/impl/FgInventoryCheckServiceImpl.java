/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.base.service.ISerialNoService;
import com.natergy.ni.fg.controller.FgInboundController;
import com.natergy.ni.fg.controller.FgOutboundController;
import com.natergy.ni.fg.entity.FgInventoryCheckEntity;
import com.natergy.ni.fg.entity.FgInventoryCheckItemEntity;
import com.natergy.ni.fg.entity.FgTransferItemEntity;
import com.natergy.ni.fg.event.InventoryCheckFinishEvent;
import com.natergy.ni.fg.excel.InventoryCheckExcel;
import com.natergy.ni.fg.mapper.FgInventoryCheckMapper;
import com.natergy.ni.fg.service.FgInboundService;
import com.natergy.ni.fg.service.FgOutboundService;
import com.natergy.ni.fg.service.IFgInventoryCheckItemService;
import com.natergy.ni.fg.service.IFgInventoryCheckService;
import com.natergy.ni.fg.vo.FgInboundVO;
import com.natergy.ni.fg.vo.FgInventoryCheckVO;
import com.natergy.ni.fg.vo.FgOutboundVO;
import com.natergy.ni.product.entity.ProductSku;
import com.natergy.ni.product.service.ProductSkuService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * 库存盘点表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-10
 */
@Service
@RequiredArgsConstructor
public class FgInventoryCheckServiceImpl extends
	BaseServiceImpl<FgInventoryCheckMapper, FgInventoryCheckEntity> implements
	IFgInventoryCheckService {

	private final IFgInventoryCheckItemService checkItemService;
	private final ProductSkuService productSkuService;
	private final ISerialNoService serialNoService;
	private final FgInboundService inboundService;
	private final FgOutboundService outboundService;

	@Override
	public IPage<FgInventoryCheckVO> selectNiFgInventoryCheckPage(
		IPage<FgInventoryCheckVO> page, FgInventoryCheckVO niFgInventoryCheck) {
		return page.setRecords(baseMapper.selectNiFgInventoryCheckPage(page, niFgInventoryCheck));
	}

	/**
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean finish(Long id) {
		//清理没有登记盘点数的数据
		checkItemService.update(Wrappers.<FgInventoryCheckItemEntity>lambdaUpdate()
			.isNull(FgInventoryCheckItemEntity::getActualQty)
			.eq(FgInventoryCheckItemEntity::getCheckId, id)
			.set(FgInventoryCheckItemEntity::getIsDeleted, 1));
		return update(
			Wrappers.<FgInventoryCheckEntity>lambdaUpdate().eq(FgInventoryCheckEntity::getId, id)
				.set(FgInventoryCheckEntity::getStatus, FgInventoryCheckEntity.STATUS_FINISH));
	}

	/**
	 * @param check
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean confirm(FgInventoryCheckEntity check) {
		finish(check.getId());
		List<FgInventoryCheckItemEntity> items = checkItemService.list(
			Wrappers.<FgInventoryCheckItemEntity>lambdaQuery()
				.eq(FgInventoryCheckItemEntity::getCheckId, check.getId()));
		Assert.isTrue(!items.isEmpty(), "请先填写盘点数据！");
		//生成调拨记录
		List<ProductSku> skus = productSkuService.listByIds(
			items.stream().map(FgInventoryCheckItemEntity::getSkuId)
				.collect(Collectors.toList()));
		Map<Long, ProductSku> skuMap = skus.stream()
			.collect(Collectors.toMap(ProductSku::getId, Function.identity()));

		FgOutboundVO outbound = new FgOutboundVO(check, items, skuMap);
		if (!outbound.getItems().isEmpty()) {
			outbound.setSerialNo(serialNoService.gen(FgOutboundController.MODULE));
			outboundService.save(outbound);
		}
		FgInboundVO inbound = new FgInboundVO(check, items, skuMap);
		if (!inbound.getItems().isEmpty()) {
			inbound.setSerialNo(serialNoService.gen(FgInboundController.MODULE));
			inboundService.save(inbound);
		}
		return update(
			Wrappers.<FgInventoryCheckEntity>lambdaUpdate()
				.eq(FgInventoryCheckEntity::getId, check.getId())
				.set(FgInventoryCheckEntity::getCompleteTime, LocalDateTime.now())
				.set(FgInventoryCheckEntity::getStatus, FgInventoryCheckEntity.STATUS_AUDIT));
	}


}
