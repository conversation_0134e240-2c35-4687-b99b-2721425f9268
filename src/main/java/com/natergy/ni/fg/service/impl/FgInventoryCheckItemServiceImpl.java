/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.fg.dto.FgInventoryCheckItemSumVarianceDTO;
import com.natergy.ni.fg.entity.FgInventoryCheckEntity;
import com.natergy.ni.fg.entity.FgInventoryCheckItemEntity;
import com.natergy.ni.fg.entity.FgInventoryCheckSnapshotEntity;
import com.natergy.ni.fg.event.InventoryCheckItemImportEvent;
import com.natergy.ni.fg.excel.InventoryCheckExcel;
import com.natergy.ni.fg.mapper.FgInventoryCheckItemMapper;
import com.natergy.ni.fg.service.IFgInventoryCheckItemService;
import com.natergy.ni.fg.service.IFgInventoryCheckSnapshotService;
import com.natergy.ni.fg.vo.FgInventoryCheckItemVO;
import com.natergy.ni.fg.vo.FgInventoryCheckSnapshotVO;
import com.natergy.ni.product.entity.ProductSku;
import com.natergy.ni.product.service.ProductSkuService;
import io.jsonwebtoken.lang.Assert;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.stereotype.Service;

/**
 * 成品库管理-盘点明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-10
 */
@Service
@RequiredArgsConstructor
public class FgInventoryCheckItemServiceImpl extends
	BaseServiceImpl<FgInventoryCheckItemMapper, FgInventoryCheckItemEntity> implements
	IFgInventoryCheckItemService {

	private final ProductSkuService productSkuService;
	private final IFgInventoryCheckSnapshotService inventoryCheckSnapshotService;

	@Override
	public IPage<FgInventoryCheckItemVO> selectNiFgInventoryCheckItemPage(
		IPage<FgInventoryCheckItemVO> page, FgInventoryCheckItemVO niFgInventoryCheckItem) {
		return page.setRecords(
			baseMapper.selectNiFgInventoryCheckItemPage(page, niFgInventoryCheckItem));
	}

	@Override
	public void importData(FgInventoryCheckEntity check, List<InventoryCheckExcel> data,
		boolean isCovered) {
		Assert.notNull(check, "盘点单不能为空");
		// 1. 校验导入数据的合法性（非空、重复、注册状态）
		validateImportData(data);
		// 2. 构建 ProductSku 映射关系
		Map<String, ProductSku> skuMap = buildMaterialCodeToSkuMap(data);
		Map<Long, ProductSku> skuIdToSkuMap = buildSkuIdToSkuMap(skuMap);
		// 3. 查询关联的盘点快照数据
		Map<Long, FgInventoryCheckSnapshotEntity> snapshotMap = fetchSnapshotMap(check.getId(),
			skuMap);
		// 4. 转换 Excel 数据为盘点明细实体
		List<FgInventoryCheckItemEntity> items = convertToInventoryItems(check.getId(), data,
			skuMap, snapshotMap);
		// 5. 校验转换后的数据有效性
		Assert.notEmpty(items, "导入的盘点数据不能为空");
		// 6. 执行数据持久化操作（覆盖或新增）
		persistInventoryData(check.getId(), items, isCovered, skuIdToSkuMap);
		SpringUtil.publishEvent(new InventoryCheckItemImportEvent(check.getId(), items));
	}

	/**
	 * @param checkId
	 * @return
	 */
	@Override
	public Long countByCheckId(Long checkId) {
		return this.count(Wrappers.<FgInventoryCheckItemEntity>lambdaQuery()
			.eq(FgInventoryCheckItemEntity::getCheckId, checkId));
	}

	/**
	 * @param checkId
	 * @return
	 */
	@Override
	public Pair<Integer, Integer> sumByCheckId(Long checkId) {
		Integer systemQty = baseMapper.sumSystemQtyByCheckId(checkId);
		Integer actualQty = baseMapper.sumActualQtyByCheckId(checkId);
		return new MutablePair<>(systemQty, actualQty);
	}

	/**
	 * @param checkIds
	 * @return
	 */
	@Override
	public List<FgInventoryCheckItemSumVarianceDTO> sumVarianceGroupByCheckId(
		@NotNull List<Long> checkIds) {
		return baseMapper.sumVarianceGroupByCheckId(checkIds);
	}

	/**
	 * 校验导入数据的合法性
	 *
	 * @param data Excel 数据列表
	 */
	private void validateImportData(List<InventoryCheckExcel> data) {
		List<String> materialCodes = data.stream()
			.map(InventoryCheckExcel::getMaterialCode)
			.filter(Objects::nonNull)
			.collect(Collectors.toList());

		// 校验 materialCode 是否重复
		Assert.isTrue(materialCodes.size() == materialCodes.stream().distinct().count(),
			"导入的存货编码存在重复，请检查数据");

		// 校验是否存在未注册的物料编码
		List<ProductSku> skus = productSkuService.listByMaterialCodes(materialCodes);
		List<String> unregisteredCodes = materialCodes.stream()
			.filter(code -> skus.stream().noneMatch(sku -> sku.getMaterialCode().equals(code)))
			.collect(Collectors.toList());
		Assert.isTrue(unregisteredCodes.isEmpty(),
			"导入的存货编码：" + String.join(",", unregisteredCodes) + "没有注册，请检查数据");
	}

	/**
	 * 构建物料编码到 ProductSku 的映射
	 *
	 * @param data Excel 数据列表
	 * @return 物料编码 → ProductSku 的映射表
	 */
	private Map<String, ProductSku> buildMaterialCodeToSkuMap(List<InventoryCheckExcel> data) {
		List<String> materialCodes = data.stream()
			.map(InventoryCheckExcel::getMaterialCode)
			.filter(Objects::nonNull)
			.collect(Collectors.toList());
		List<ProductSku> skus = productSkuService.listByMaterialCodes(materialCodes);
		return skus.stream()
			.collect(Collectors.toMap(ProductSku::getMaterialCode, Function.identity()));
	}

	/**
	 * 构建 SKU ID 到 ProductSku 的映射
	 *
	 * @param skuMap 物料编码 → ProductSku 的映射表
	 * @return SKU ID → ProductSku 的映射表
	 */
	private Map<Long, ProductSku> buildSkuIdToSkuMap(Map<String, ProductSku> skuMap) {
		return skuMap.values().stream()
			.collect(Collectors.toMap(ProductSku::getId, Function.identity()));
	}

	/**
	 * 查询与当前盘点单关联的快照数据
	 *
	 * @param checkId 盘点单ID
	 * @param skuMap  物料编码 → ProductSku 的映射表
	 * @return SKU ID → 快照实体的映射表
	 */
	private Map<Long, FgInventoryCheckSnapshotEntity> fetchSnapshotMap(Long checkId,
		Map<String, ProductSku> skuMap) {
		List<Long> skuIds = skuMap.values().stream()
			.map(ProductSku::getId)
			.collect(Collectors.toList());

		List<FgInventoryCheckSnapshotEntity> snapshots = inventoryCheckSnapshotService.list(
			Wrappers.<FgInventoryCheckSnapshotEntity>lambdaQuery()
				.eq(FgInventoryCheckSnapshotEntity::getCheckId, checkId)
				.in(FgInventoryCheckSnapshotEntity::getSkuId, skuIds));

		return snapshots.stream()
			.collect(
				Collectors.toMap(FgInventoryCheckSnapshotEntity::getSkuId, Function.identity()));
	}

	/**
	 * 将 Excel 数据转换为盘点明细实体列表
	 *
	 * @param checkId     盘点单ID
	 * @param data        Excel 数据列表
	 * @param skuMap      物料编码 → ProductSku 的映射表
	 * @param snapshotMap SKU ID → 快照实体的映射表
	 * @return 盘点明细实体列表
	 */
	private List<FgInventoryCheckItemEntity> convertToInventoryItems(
		Long checkId, List<InventoryCheckExcel> data,
		Map<String, ProductSku> skuMap,
		Map<Long, FgInventoryCheckSnapshotEntity> snapshotMap) {
		return data.stream()
			.filter(excel -> excel.getActualQty() != null)
			.map(excel -> {
				ProductSku sku = skuMap.getOrDefault(excel.getMaterialCode(), new ProductSku());
				FgInventoryCheckSnapshotEntity snapshot = snapshotMap.getOrDefault(
					sku.getId(), new FgInventoryCheckSnapshotEntity());
				FgInventoryCheckItemEntity item = new FgInventoryCheckItemEntity();
				item.setCheckId(checkId);
				item.setSkuId(sku.getId());
				item.setSystemQty(snapshot.getNum() == null ? 0 : snapshot.getNum());
				item.setActualQty(excel.getActualQty());
				item.setVariance(item.getActualQty() - item.getSystemQty());
				return item;
			})
			.collect(Collectors.toList());
	}

	/**
	 * 执行盘点数据的持久化操作（覆盖或新增）
	 *
	 * @param checkId       盘点单ID
	 * @param items         盘点明细实体列表
	 * @param isCovered     是否覆盖已有数据
	 * @param skuIdToSkuMap SKU ID → ProductSku 的映射表
	 */
	private void persistInventoryData(
		Long checkId, List<FgInventoryCheckItemEntity> items,
		boolean isCovered, Map<Long, ProductSku> skuIdToSkuMap) {

		if (isCovered) {
			// 覆盖模式：删除旧数据
			this.remove(Wrappers.<FgInventoryCheckItemEntity>lambdaQuery()
				.eq(FgInventoryCheckItemEntity::getCheckId, checkId)
				.in(FgInventoryCheckItemEntity::getSkuId,
					items.stream().map(FgInventoryCheckItemEntity::getSkuId)
						.collect(Collectors.toList())));
		} else {
			// 新增模式：校验是否已存在相同数据
			List<FgInventoryCheckItemEntity> exists = this.list(
				Wrappers.<FgInventoryCheckItemEntity>lambdaQuery()
					.eq(FgInventoryCheckItemEntity::getCheckId, checkId)
					.in(FgInventoryCheckItemEntity::getSkuId,
						items.stream().map(FgInventoryCheckItemEntity::getSkuId)
							.collect(Collectors.toList())));

			Assert.isTrue(exists.isEmpty(),
				"导入的盘点数据：" + exists.stream()
					.map(FgInventoryCheckItemEntity::getSkuId)
					.map(skuId -> skuIdToSkuMap.getOrDefault(skuId, new ProductSku())
						.getMaterialCode())
					.filter(Objects::nonNull)
					.collect(Collectors.joining(",")) + "已经存在，请检查数据");
		}

		// 保存新数据
		this.saveBatch(items);
	}

//	/**
//	 * @param data
//	 * @param isCovered
//	 */
//	@Override
//	public void importData(FgInventoryCheckEntity check, List<InventoryCheckExcel> data,
//		boolean isCovered) {
//		Assert.notNull(check, "盘点单不能为空");
//		List<String> materialCodes = data.stream().map(InventoryCheckExcel::getMaterialCode)
//			.filter(Objects::nonNull).collect(Collectors.toList());
//		// 校验 materialCode 是否重复
//		Assert.isTrue(materialCodes.size() == materialCodes.stream().distinct().count(),
//			"导入的存货编码存在重复，请检查数据");
//		List<ProductSku> skus = productSkuService.listByMaterialCodes(materialCodes);
//		List<ProductSku> noRegisterSkus = skus.stream()
//			.filter(sku -> !materialCodes.contains(sku.getMaterialCode())).collect(
//				Collectors.toList());
//		Assert.isTrue(noRegisterSkus.isEmpty(),
//			"导入的存货编码：" + noRegisterSkus.stream().map(ProductSku::getMaterialCode)
//				.collect(Collectors.joining(",")) + "没有注册，请检查数据");
//		Map<String, ProductSku> skuMap = skus.stream()
//			.collect(Collectors.toMap(ProductSku::getMaterialCode, Function.identity()));
//		Map<Long,ProductSku> skuMap1 = skus.stream()
//			.collect(Collectors.toMap(ProductSku::getId, Function.identity()));
//		List<FgInventoryCheckSnapshotEntity> snapshots = inventoryCheckSnapshotService.list(
//			Wrappers.<FgInventoryCheckSnapshotEntity>lambdaQuery()
//				.eq(FgInventoryCheckSnapshotEntity::getCheckId, check.getId())
//				.in(FgInventoryCheckSnapshotEntity::getSkuId,
//					skus.stream().map(ProductSku::getId).collect(Collectors.toList())));
//		Map<Long, FgInventoryCheckSnapshotEntity> snapshotMap = snapshots.stream()
//			.collect(
//				Collectors.toMap(FgInventoryCheckSnapshotEntity::getSkuId, Function.identity()));
//		List<FgInventoryCheckItemEntity> items = new ArrayList<>();
//		// 执行业务逻辑
//		data.stream().filter(excel -> excel.getActualQty() != null).forEach(excel -> {
//			FgInventoryCheckItemEntity item = new FgInventoryCheckItemEntity();
//			item.setCheckId(check.getId());
//			ProductSku sku = skuMap.getOrDefault(excel.getMaterialCode(), new ProductSku());
//			item.setSkuId(sku.getId());
//			FgInventoryCheckSnapshotEntity snapshot = snapshotMap.getOrDefault(sku.getId(),
//				new FgInventoryCheckSnapshotEntity());
//			item.setSystemQty(snapshot.getNum() == null ? 0 : snapshot.getNum());
//			item.setActualQty(excel.getActualQty());
//			item.setVariance(item.getActualQty() - item.getSystemQty());
//			items.add(item);
//		});
//		Assert.notEmpty(items, "导入的盘点数据不能为空");
//		if (isCovered) {
//			this.remove(Wrappers.<FgInventoryCheckItemEntity>lambdaQuery()
//				.eq(FgInventoryCheckItemEntity::getCheckId, check.getId())
//				.in(FgInventoryCheckItemEntity::getSkuId,
//					items.stream().map(FgInventoryCheckItemEntity::getSkuId)
//						.collect(Collectors.toList())));
//		} else {
//			//去重
//			List<FgInventoryCheckItemEntity> exists = this.list(
//				Wrappers.<FgInventoryCheckItemEntity>lambdaQuery()
//					.eq(FgInventoryCheckItemEntity::getCheckId, check.getId())
//					.in(FgInventoryCheckItemEntity::getSkuId,
//						items.stream().map(FgInventoryCheckItemEntity::getSkuId)
//							.collect(Collectors.toList())));
//			Assert.isTrue(exists.isEmpty(),
//				"导入的盘点数据：" + exists.stream().map(FgInventoryCheckItemEntity::getSkuId)
//					.map(skuId -> skuMap1.getOrDefault(skuId, new ProductSku()).getMaterialCode())
//					.filter(Objects::nonNull)
//					.collect(Collectors.joining(",")) + "已经存在，请检查数据");
//		}
//		this.saveBatch(items);
//
//	}


}
