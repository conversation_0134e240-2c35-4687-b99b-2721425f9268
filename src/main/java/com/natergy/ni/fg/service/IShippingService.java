/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.fg.entity.ShippingEntity;
import com.natergy.ni.fg.vo.FgOutboundVO;
import com.natergy.ni.fg.vo.ShippingItemVO;
import com.natergy.ni.fg.vo.ShippingVO;
import java.util.List;
import org.springblade.core.mp.base.BaseService;

/**
 * 成品库-发货管理 服务类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface IShippingService extends BaseService<ShippingEntity> {


    List<ShippingItemVO> loadItemMaterialByShippingId(Long id);

    List<FgOutboundVO> audit(Long id);

    boolean remove1(List<Long> ids);

    ShippingVO getDetailById(Long id);
}
