/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.fg.dto.FgInventoryCheckSnapshotReqDTO;
import com.natergy.ni.fg.entity.FgInventoryCheckSnapshotEntity;
import com.natergy.ni.fg.event.FgInventoryCheckSnapshotBuildEvent;
import com.natergy.ni.fg.mapper.FgInventoryCheckSnapshotMapper;
import com.natergy.ni.fg.service.IFgInventoryCheckSnapshotService;
import com.natergy.ni.fg.vo.FgInventoryCheckSnapshotVO;
import io.jsonwebtoken.lang.Assert;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 成品库管理-盘点快照 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-10
 */
@Service
public class InventoryCheckSnapshotServiceImpl extends
	BaseServiceImpl<FgInventoryCheckSnapshotMapper, FgInventoryCheckSnapshotEntity> implements
	IFgInventoryCheckSnapshotService {

	@Override
	public IPage<FgInventoryCheckSnapshotVO> selectNiFgInventoryCheckSnapshotPage(
		IPage<FgInventoryCheckSnapshotVO> page,
		FgInventoryCheckSnapshotReqDTO niFgInventoryCheckSnapshot) {
		return page.setRecords(selectCheckSnapshotList(page, niFgInventoryCheckSnapshot));
	}

	/**
	 * @param page
	 * @param q
	 * @return
	 */
	@Override
	public List<FgInventoryCheckSnapshotVO> selectCheckSnapshotList(
		IPage<FgInventoryCheckSnapshotVO> page, FgInventoryCheckSnapshotReqDTO q) {
		return baseMapper.selectCheckSnapshotList(page, q);
	}

	/**
	 * @param checkId
	 * @param depotId
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public LocalDateTime build(Long checkId, Long depotId) {
		remove(Wrappers.<FgInventoryCheckSnapshotEntity>lambdaQuery()
			.eq(FgInventoryCheckSnapshotEntity::getCheckId, checkId));
		baseMapper.build(checkId, depotId);
		boolean res = update(
			Wrappers.<FgInventoryCheckSnapshotEntity>lambdaUpdate()
				.set(FgInventoryCheckSnapshotEntity::getStatus, 1)
				.eq(FgInventoryCheckSnapshotEntity::getCheckId, checkId)
		);
		Assert.isTrue(res, "盘点快照生成失败");
		SpringUtil.publishEvent(new FgInventoryCheckSnapshotBuildEvent(checkId, depotId));
		return LocalDateTime.now();
	}

	/**
	 * @param skuIds
	 * @return
	 */
	@Override
	public List<FgInventoryCheckSnapshotVO> selectSnapshotListBySkuIds(Long checkId,
		List<Long> skuIds) {
		FgInventoryCheckSnapshotReqDTO q = new FgInventoryCheckSnapshotReqDTO();
		q.setSkuIds(skuIds);
		q.setCheckId(checkId);
		return selectCheckSnapshotList(null, q);
	}

	/**
	 * @param checkId
	 * @return
	 */
	@Override
	public Long countByCheckId(Long checkId) {
		return count(Wrappers.<FgInventoryCheckSnapshotEntity>lambdaQuery()
			.eq(FgInventoryCheckSnapshotEntity::getCheckId, checkId));
	}

	/**
	 * @param checkId
	 * @return
	 */
	@Override
	public Long sumByCheckId(Long checkId) {
		return baseMapper.sumByCheckId(checkId);
	}


}
