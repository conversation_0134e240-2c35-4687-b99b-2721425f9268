/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.base.cache.MaterialCache;
import com.natergy.ni.base.entity.Material;
import com.natergy.ni.base.service.ISerialNoService;
import com.natergy.ni.fg.controller.FgOutboundController;
import com.natergy.ni.fg.dto.ShippingItemFromOldDTO;
import com.natergy.ni.fg.entity.ShippingEntity;
import com.natergy.ni.fg.entity.ShippingItemEntity;
import com.natergy.ni.fg.mapper.ShippingMapper;
import com.natergy.ni.fg.service.FgOutboundService;
import com.natergy.ni.fg.service.IShippingItemService;
import com.natergy.ni.fg.service.IShippingService;
import com.natergy.ni.fg.vo.FgOutboundItemVO;
import com.natergy.ni.fg.vo.FgOutboundVO;
import com.natergy.ni.fg.vo.ShippingItemVO;
import com.natergy.ni.fg.vo.ShippingVO;
import com.natergy.ni.fg.wrapper.ShippingWrapper;
import com.natergy.ni.old.entity.GuoWaiFaHuoEntity;
import com.natergy.ni.old.entity.XiaoShouDingDanEntity;
import com.natergy.ni.old.entity.XiaoShouDingDanMingXiEntity;
import com.natergy.ni.old.entity.XiaoShouWaiKuBuHuoEntity;
import com.natergy.ni.old.entity.XiaoShouWaiKuBuHuoMingXiEntity;
import com.natergy.ni.old.service.IGuoWaiFaHuoService;
import com.natergy.ni.old.service.IXiaoShouDingDanMingXiService;
import com.natergy.ni.old.service.IXiaoShouDingDanService;
import com.natergy.ni.old.service.IXiaoShouWaiKuBuHuoMingXiService;
import com.natergy.ni.old.service.IXiaoShouWaiKuBuHuoService;
import com.natergy.ni.product.entity.ProductSku;
import com.natergy.ni.product.service.ProductSkuService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.enums.DictBizEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * 成品库-发货管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
@RequiredArgsConstructor
public class ShippingServiceImpl extends BaseServiceImpl<ShippingMapper, ShippingEntity> implements
	IShippingService {

	private final IXiaoShouDingDanService xiaoShouDingDanService;
	private final IXiaoShouDingDanMingXiService xiaoShouDingDanMingXiService;
	private final IXiaoShouWaiKuBuHuoService xiaoShouWaiKuBuHuoService;
	private final IXiaoShouWaiKuBuHuoMingXiService xiaoShouWaiKuBuHuoMingXiService;
	private final IGuoWaiFaHuoService guoWaiFaHuoService;
	private final IShippingItemService shippingItemService;
	private final ISerialNoService serialNoService;
	private final ProductSkuService productSkuService;
	private final FgOutboundService outboundService;

	/**
	 * @param id
	 * @return
	 */
	@Override
	public List<ShippingItemVO> loadItemMaterialByShippingId(Long id) {
		ShippingEntity shipping = getById(id);
		Assert.isTrue(shipping != null, "请选择正确的发货单");
		if (ShippingEntity.TYPE_IN.equals(shipping.getType())) {
			XiaoShouDingDanEntity xiaoShouDingDan = xiaoShouDingDanService.getDetail(
				shipping.getSourceId());
			Assert.isTrue(xiaoShouDingDan != null, "请选择正确的发货编号");
			List<XiaoShouDingDanMingXiEntity> xiaoShouDingDanMingXiList = xiaoShouDingDanMingXiService.selectMingXiList(
				xiaoShouDingDan.getId() + "");
			return xiaoShouDingDanMingXiList.stream().filter(
					xiaoShouDingDanMingXi -> StringUtils.isNotBlank(
						xiaoShouDingDanMingXi.getYyCunHuoBianMa()))
				.map(xiaoShouDingDanMingXi -> {
					ShippingItemVO shippingItemVO = new ShippingItemVO();
					shippingItemVO.setMaterialCode(xiaoShouDingDanMingXi.getYyCunHuoBianMa());
					shippingItemVO.setSpecText(xiaoShouDingDanMingXi.getGuigeMm());
					shippingItemVO.setPackageText(xiaoShouDingDanMingXi.getWaiBaoZhuang());
					shippingItemVO.setInnerPackageText(xiaoShouDingDanMingXi.getBaoZhuang());
					shippingItemVO.setNum(xiaoShouDingDanMingXi.getJianShu().intValue());
					shippingItemVO.setWeight(
						BigDecimal.valueOf(xiaoShouDingDanMingXi.getJingZhong()));
					shippingItemVO.setRemark(xiaoShouDingDanMingXi.getBeiZhu());
					Material material = MaterialCache.getByCode(
						xiaoShouDingDanMingXi.getYyCunHuoBianMa());
					Assert.isTrue(material != null,
						"系统中不存在该存货编码：" + xiaoShouDingDanMingXi.getYyCunHuoBianMa());
					shippingItemVO.setMaterialName(material.getName());
					return shippingItemVO;
				}).collect(Collectors.toList());
		} else if (ShippingEntity.TYPE_OUT.equals(shipping.getType())) {
			return new ArrayList<>();
		} else if (ShippingEntity.TYPE_REPLENISH.equals(shipping.getType())) {
			XiaoShouWaiKuBuHuoEntity xiaoShouWaiKuBuHuo = xiaoShouWaiKuBuHuoService.getDetail(
				shipping.getSourceId());
			Assert.isTrue(xiaoShouWaiKuBuHuo != null, "请选择正确的发货编号");
			List<XiaoShouWaiKuBuHuoMingXiEntity> xiaoShouWaiKuBuHuoMingXiList = xiaoShouWaiKuBuHuoMingXiService.selectMingXiList(
				xiaoShouWaiKuBuHuo.getDingDanHao());
			return xiaoShouWaiKuBuHuoMingXiList.stream().filter(
				xiaoShouDingDanMingXi -> StringUtils.isNotBlank(
					xiaoShouDingDanMingXi.getYyCunHuoBianMa())).map(xiaoShouDingDanMingXi -> {
				ShippingItemVO shippingItemVO = new ShippingItemVO();
				shippingItemVO.setMaterialCode(xiaoShouDingDanMingXi.getYyCunHuoBianMa());
				shippingItemVO.setSpecText(xiaoShouDingDanMingXi.getGuiGeMm());
				shippingItemVO.setPackageText(xiaoShouDingDanMingXi.getWaiBaoZhuang());
				shippingItemVO.setInnerPackageText(xiaoShouDingDanMingXi.getBaoZhuang());
				shippingItemVO.setNum(xiaoShouDingDanMingXi.getJianShu().intValue());
				shippingItemVO.setWeight(
					BigDecimal.valueOf(xiaoShouDingDanMingXi.getJingZhongKg()));
				shippingItemVO.setRemark(xiaoShouDingDanMingXi.getBeiZhu());
				Material material = MaterialCache.getByCode(
					xiaoShouDingDanMingXi.getYyCunHuoBianMa());
				Assert.isTrue(material != null,
					"系统中不存在该存货编码：" + xiaoShouDingDanMingXi.getYyCunHuoBianMa());
				shippingItemVO.setMaterialName(material.getName());
				return shippingItemVO;
			}).collect(Collectors.toList());
		}
		return Collections.emptyList();
	}

	/**
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public List<FgOutboundVO> audit(Long id) {
		ShippingEntity shipping = getById(id);
		Assert.notNull(shipping, "请选择正确的发货单");
		String type = trans2OutboundType(shipping.getType());
		Assert.notNull(type, "类型映射失败");
		List<ShippingItemEntity> items = shippingItemService.list(
			Wrappers.<ShippingItemEntity>lambdaQuery().eq(ShippingItemEntity::getShippingId, id));
		Assert.isTrue(!items.isEmpty(), "该发货单未登记批号信息");
		List<FgOutboundVO> res = new ArrayList<>();
		items.stream().collect(Collectors.groupingBy(ShippingItemEntity::getDepotId))
			.forEach((depotId, is) -> {
				List<FgOutboundVO> outboundVOS = outboundService.listByRelatedOrderId(
					shipping.getSourceId());
				Assert.isTrue(outboundVOS.stream()
						.noneMatch(outboundVO -> outboundVO.getDepotId().equals(depotId)),
					"该发货单已生成出库单");
				FgOutboundVO outbound = buildOutbound(type, shipping, depotId, is);
				outboundService.save(outbound);
				res.add(outbound);
			});
		this.update(
			Wrappers.<ShippingEntity>lambdaUpdate().eq(ShippingEntity::getId, shipping.getId())
				.set(ShippingEntity::getStatus, ShippingEntity.STATUS_PASS));
		return res;
	}

	/**
	 * @param ids
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean remove1(List<Long> ids) {
		boolean res = deleteLogic(ids);
		Assert.isTrue(res, "删除失败");
		shippingItemService.remove(
			Wrappers.<ShippingItemEntity>lambdaQuery().in(ShippingItemEntity::getShippingId, ids));
		return true;
	}

	/**
	 * <AUTHOR> Assistant
	 * @date 2025-07-06
	 * @description 优化后的发货单服务实现
	 */
	@Override
	public ShippingVO getDetailById(Long id) {
		// 1. 根据ID获取发货单实体
		ShippingEntity shipping = getById(id);
		// 使用包装器将实体对象转换为VO（视图对象）
		ShippingVO res = ShippingWrapper.build().entityVO(shipping);

		// 2. 获取该发货单关联的所有发货项
		List<ShippingItemVO> items = shippingItemService.selectListByShippingId(id);
		res.setItems(items);

		// 3. 计算发货项的总数量和总重量
		// 计算总数量
		res.setTotalNum(items.stream().mapToInt(ShippingItemEntity::getNum).sum());
		// 计算总重量，使用BigDecimal以保证精度
		res.setTotalWeight(
			items.stream().map(ShippingItemEntity::getWeight).filter(Objects::nonNull)
				.reduce(BigDecimal.ZERO, BigDecimal::add));

		// 4. 按物料编码对发货项进行分组，以便后续处理批次号
		Map<String, List<ShippingItemEntity>> groupedItems = items.stream().collect(
			Collectors.groupingBy(ShippingItemEntity::getMaterialCode));

		// 5. 根据发货单类型，调用不同的私有方法处理特定逻辑
		String type = shipping.getType();
		if (ShippingEntity.TYPE_IN.equals(type)) {
			// 处理 "入库" 类型的发货单 (关联销售订单)
			handleInboundShipping(res, shipping, groupedItems);
		} else if (ShippingEntity.TYPE_REPLENISH.equals(type)) {
			// 处理 "补货" 类型的发货单 (关联外库补货单)
			handleReplenishShipping(res, shipping, groupedItems);
		} else if (ShippingEntity.TYPE_OUT.equals(type)) {
			// 处理 "出库" 类型的发货单 (关联国外发货单)
			handleOutboundShipping(res, shipping);
		}

		return res;
	}

	/**
	 * 处理入库类型发货单的详情 (源自销售订单)
	 *
	 * @param res          结果视图对象，将被填充数据
	 * @param shipping     发货单实体
	 * @param groupedItems 按物料编码分组后的发货项
	 */
	private void handleInboundShipping(ShippingVO res, ShippingEntity shipping,
		Map<String, List<ShippingItemEntity>> groupedItems) {
		// 根据源ID获取关联的销售订单详情
		XiaoShouDingDanEntity xiaoShouDingDan = xiaoShouDingDanService.getDetail(
			shipping.getSourceId());
		Assert.isTrue(xiaoShouDingDan != null, "请选择正确的发货编号");

		// 从销售订单填充客户名称、车牌号和收货地址
		res.setCustomerName(xiaoShouDingDan.getKhmc());
		res.setTruckNo(xiaoShouDingDan.getTxch());
		res.setAddress(xiaoShouDingDan.getShdz());

		// 获取销售订单的明细列表
		List<XiaoShouDingDanMingXiEntity> xiaoShouDingDanMingXiList = xiaoShouDingDanMingXiService.selectMingXiList(
			xiaoShouDingDan.getId() + "");

		// 提取销售订单明细中的物料编码，并查询对应的产品SKU信息
		List<String> materialCodes = xiaoShouDingDanMingXiList.stream()
			.map(XiaoShouDingDanMingXiEntity::getYyCunHuoBianMa)
			.collect(Collectors.toList());
		Map<String, ProductSku> skuMap = productSkuService.listByMaterialCodes(materialCodes)
			.stream()
			.collect(Collectors.toMap(ProductSku::getMaterialCode, Function.identity()));

		// 将销售订单明细转换为 "源单据货品" DTO列表
		res.setItemsFromOld(
			xiaoShouDingDanMingXiList.stream().map(mingXi -> {
				ShippingItemFromOldDTO i = new ShippingItemFromOldDTO(mingXi);
				// 根据物料编码从skuMap中查找并设置skuId
				ProductSku sku = skuMap.getOrDefault(i.getMaterialCode(), new ProductSku());
				i.setSkuId(sku.getId());
				i.setQualityLevel(sku.getQualityLevel());
				i.setQualityLevelText(
					DictBizCache.getValue(DictBizEnum.NI_PRODUCT_SKU_QUALITY_LEVEL,
						sku.getQualityLevel()));
				i.setCapacity(sku.getCapacity());
				// 根据物料编码从groupedItems中查找，并将所有批次号用 "/" 连接起来
				i.setBatchNo(
					groupedItems.getOrDefault(i.getMaterialCode(), new ArrayList<>()).stream()
						.map(ShippingItemEntity::getBatchNo)
						.collect(Collectors.joining("/"))
				);
				i.setBatchNoNum(
					groupedItems.getOrDefault(i.getMaterialCode(), new ArrayList<>()).stream()
						.mapToInt(ShippingItemEntity::getNum).sum());
				return i;
			}).collect(Collectors.toList()));
	}

	/**
	 * 处理补货类型发货单的详情 (源自外库补货)
	 *
	 * @param res          结果视图对象，将被填充数据
	 * @param shipping     发货单实体
	 * @param groupedItems 按物料编码分组后的发货项
	 */
	private void handleReplenishShipping(ShippingVO res, ShippingEntity shipping,
		Map<String, List<ShippingItemEntity>> groupedItems) {
		// 根据源ID获取关联的外库补货单详情
		XiaoShouWaiKuBuHuoEntity xiaoShouWaiKuBuHuo = xiaoShouWaiKuBuHuoService.getDetail(
			shipping.getSourceId());
		Assert.isTrue(xiaoShouWaiKuBuHuo != null, "请选择正确的发货编号");

		// 从外库补货单填充仓库名称和提车号/电话
		res.setCustomerName(xiaoShouWaiKuBuHuo.getWaiKuMingCheng());
		res.setTruckNo(xiaoShouWaiKuBuHuo.getTiCheHaoShiJiDianHua());

		// 获取外库补货单的明细列表
		List<XiaoShouWaiKuBuHuoMingXiEntity> mingXiList = xiaoShouWaiKuBuHuoMingXiService.selectMingXiList(
			xiaoShouWaiKuBuHuo.getDingDanHao());

		// 提取补货单明细中的物料编码，并查询对应的产品SKU信息
		List<String> materialCodes = mingXiList.stream()
			.map(XiaoShouWaiKuBuHuoMingXiEntity::getYyCunHuoBianMa)
			.collect(Collectors.toList());
		Map<String, ProductSku> skuMap = productSkuService.listByMaterialCodes(materialCodes)
			.stream()
			.collect(Collectors.toMap(ProductSku::getMaterialCode, Function.identity()));

		// 将外库补货单明细转换为 "源单据货品" DTO列表
		res.setItemsFromOld(mingXiList.stream()
			.map(mingXi -> {
				ShippingItemFromOldDTO i = new ShippingItemFromOldDTO(mingXi);
				// 根据物料编码从skuMap中查找并设置skuId
				ProductSku sku = skuMap.getOrDefault(i.getMaterialCode(), new ProductSku());
				i.setSkuId(sku.getId());
				i.setQualityLevel(sku.getQualityLevel());
				i.setQualityLevelText(
					DictBizCache.getValue(DictBizEnum.NI_PRODUCT_SKU_QUALITY_LEVEL,
						sku.getQualityLevel()));
				i.setCapacity(sku.getCapacity());
				// 根据物料编码从groupedItems中查找，并将所有批次号用 "/" 连接起来
				i.setBatchNo(
					groupedItems.getOrDefault(i.getMaterialCode(), new ArrayList<>()).stream()
						.map(ShippingItemEntity::getBatchNo)
						.collect(Collectors.joining("/"))
				);
				i.setBatchNoNum(
					groupedItems.getOrDefault(i.getMaterialCode(), new ArrayList<>()).stream()
						.mapToInt(ShippingItemEntity::getNum).sum());
				return i;
			})
			.collect(Collectors.toList()));
	}

	/**
	 * 处理出库类型发货单的详情 (源自国外发货)
	 *
	 * @param res      结果视图对象，将被填充数据
	 * @param shipping 发货单实体
	 */
	private void handleOutboundShipping(ShippingVO res, ShippingEntity shipping) {
		// 根据源ID获取关联的国外发货单详情
		GuoWaiFaHuoEntity guoWaiFaHuo = guoWaiFaHuoService.getDetail(shipping.getSourceId());
		Assert.isTrue(guoWaiFaHuo != null, "请选择正确的发货编号");

		// 从国外发货单填充公司名称和地址（国家/目的港）
		res.setCustomerName(guoWaiFaHuo.getGongSiMingCheng());
		res.setAddress(guoWaiFaHuo.getGuoJia() + "/" + guoWaiFaHuo.getMuDiGang());
	}

	private FgOutboundVO buildOutbound(String type, ShippingEntity shipping, Long depotId,
		@NotNull List<ShippingItemEntity> items) {
		FgOutboundVO outbound = new FgOutboundVO();
		outbound.setTitle(DateUtil.format(new Date(), "yyyy-MM-dd"));
		outbound.setSerialNo(serialNoService.gen(FgOutboundController.MODULE));
		outbound.setDepotId(depotId);
		outbound.setType(type);
		outbound.setOpUserId(AuthUtil.getUserId());
		outbound.setOpDate(LocalDate.now());
		outbound.setRemark(shipping.getRemark());
		outbound.setRelatedOrderId(shipping.getSourceId());
		outbound.setRelatedOrderText(shipping.getSerialNo());
		outbound.setTotal(outbound.getTotal());
		outbound.setStatus(FgOutboundVO.STATUS_NORMAL);
		List<ProductSku> skuList = productSkuService.listByIds(
			items.stream().map(ShippingItemEntity::getSkuId).distinct().collect(
				Collectors.toList()));
		Map<Long, ProductSku> skuMap = skuList.stream()
			.collect(Collectors.toMap(ProductSku::getId, Function.identity()));
		outbound.setItems(items.stream().map(i -> {
			FgOutboundItemVO item = new FgOutboundItemVO();
			ProductSku sku = skuMap.getOrDefault(i.getSkuId(), new ProductSku());
			item.setSkuId(i.getSkuId());
			item.setBatchNo(i.getBatchNo());
			item.setNum(i.getNum());
			item.setRemark(i.getRemark());
			item.setSkuText(sku.getSku());
			item.setSpec(sku.getSpecification());
			item.setSpecText(sku.getSpecText());
			item.setPackageId(sku.getPackageId());
			item.setPackageText(sku.getPackageText());
			item.setInnerPackageId(sku.getInnerPackageId());
			item.setInnerPackageText(sku.getInnerPackageText());
			item.setMaterialId(sku.getMaterialId());
			item.setMaterialCode(sku.getMaterialCode());
			if (item.getUnit() == null) {
				item.setUnit(sku.getUnit());
			}
			if (item.getWeight() == null) {
				item.setWeight(sku.getCapacity().multiply(BigDecimal.valueOf(item.getNum())));
			}
			return item;
		}).collect(Collectors.toList()));
		outbound.setTotal(
			outbound.getItems().stream().mapToInt(FgOutboundItemVO::getNum)
				.filter(Objects::nonNull).sum());
		return outbound;
	}

	private String trans2OutboundType(String type) {
		switch (type) {
			case ShippingEntity.TYPE_IN:
				return FgOutboundVO.TYPE_SHIPMENT;
			case ShippingEntity.TYPE_OUT:
				return FgOutboundVO.TYPE_FOREIGN;
			case ShippingEntity.TYPE_REPLENISH:
				return FgOutboundVO.TYPE_WHOLESALE;
		}
		return null;
	}


}
