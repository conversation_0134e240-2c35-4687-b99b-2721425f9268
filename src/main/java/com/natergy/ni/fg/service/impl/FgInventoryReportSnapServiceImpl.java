package com.natergy.ni.fg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.natergy.ni.fg.entity.FgInventoryReportSnapEntity;
import com.natergy.ni.fg.mapper.FgInventoryReportSnapMapper;
import com.natergy.ni.fg.mapper.FgInventorySummaryMapper;
import com.natergy.ni.fg.mapper.FgTransactionMapper;
import com.natergy.ni.fg.service.IFgInventoryReportSnapService;
import com.natergy.ni.fg.vo.FgInventoryReportSnapItemVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.natergy.ni.fg.vo.FgInventoryReportSnapItemVO.ReportMode.*;

/**
 * 库存报表快照
 *
 */
@Slf4j
@Service
public class FgInventoryReportSnapServiceImpl extends BaseServiceImpl<FgInventoryReportSnapMapper, FgInventoryReportSnapEntity> implements
	IFgInventoryReportSnapService {

	@Autowired
	FgTransactionMapper transactionMapper;
	@Autowired
	FgInventorySummaryMapper inventorySummaryMapper;

	/**
	 * 生成快照
	 *
	 */
	@Override
	public void saveSnap(LocalDate date) {
		if (Objects.isNull(date)) {
			date = LocalDate.now().minusDays(BigInteger.ONE.intValue());
		}
		String opDate = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

		Long inboundTotal = Optional.ofNullable(transactionMapper.sumInboundTotal(opDate)).orElse(BigInteger.ZERO.longValue());
		log.info("库存快照 日期:{} inboundTotal:{}", opDate,inboundTotal);
		Long outboundTotal = Optional.ofNullable(transactionMapper.sumOutboundTotal(opDate)).orElse(BigInteger.ZERO.longValue());
		log.info("库存快照 日期:{} outboundTotal:{}" ,opDate,outboundTotal);
		Long total = Optional.ofNullable(inventorySummaryMapper.sumTotal()).orElse(BigInteger.ZERO.longValue());
		log.info("库存快照 日期:{} total:{}" ,opDate,total);
		List<FgInventoryReportSnapItemVO.InventorySnapItemVO> skuInboundAndOutbound = Optional.ofNullable(getBaseMapper().sumTotalGroupBySku(opDate)).orElse(new ArrayList<>());

		List<FgInventoryReportSnapItemVO.InventorySnapItemVO> skuTotal = Optional.ofNullable(getBaseMapper().dailySkuStock()).orElse(new ArrayList<>());

		List<FgInventoryReportSnapItemVO.InventorySnapItemVO> mergeList = new ArrayList<>(
			Stream.concat(skuInboundAndOutbound.stream(), skuTotal.stream())
			.collect(Collectors.toMap(
				FgInventoryReportSnapItemVO.InventorySnapItemVO::getSku,
				item -> item,
				(details, sumTotal) -> {  // 冲突处理策略
					return new FgInventoryReportSnapItemVO.InventorySnapItemVO(details.getSku(),
						StringUtils.isNotBlank(details.getSkuName()) ? details.getSkuName() : sumTotal.getSkuName(),
						Optional.ofNullable(details.getInboundTotal()).orElse(BigInteger.ZERO.longValue()),
						Optional.ofNullable(details.getOutboundTotal()).orElse(BigInteger.ZERO.longValue()),
						Optional.ofNullable(sumTotal.getTotal()).orElse(BigInteger.ZERO.longValue()));
				}))
			.values());
		String details = JsonUtil.toJson(mergeList);
		log.info("库存快照 日期:{} 聚合后明细情况:{}",opDate,details);

		FgInventoryReportSnapEntity snapEntity = new FgInventoryReportSnapEntity();
		snapEntity.setSnapDate(DateUtil.toDate(date));
		snapEntity.setTotal(total);
		snapEntity.setInbound(inboundTotal);
		snapEntity.setOutbound(outboundTotal);
		snapEntity.setDetails(details);
		save(snapEntity);

	}

	/**
	 * 获取快照列表
	 *
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<FgInventoryReportSnapItemVO> getSnapList(Date startDate, Date endDate) {
		LambdaQueryWrapper<FgInventoryReportSnapEntity> queryWrapper = new LambdaQueryWrapper<FgInventoryReportSnapEntity>().between(FgInventoryReportSnapEntity::getSnapDate, startDate, endDate);

		List<FgInventoryReportSnapEntity> result = Optional.ofNullable(this.list(queryWrapper)).orElse(new ArrayList<>());
		return result.stream().map(r->{
			List<FgInventoryReportSnapItemVO.InventorySnapItemVO> items = JsonUtil.readList(r.getDetails(), FgInventoryReportSnapItemVO.InventorySnapItemVO.class);
			return new FgInventoryReportSnapItemVO(r.getTotal().toString(),r.getSnapDate(),r.getInbound().toString(),r.getOutbound().toString(),items);
		}).collect(Collectors.toList());
	}

	/**
	 * 报表数据
	 * 默认按昨日时间计算
	 *
	 * @return
	 */
	@Override
	public FgInventoryReportSnapItemVO report(String mode) {

		// 最新日期
		LocalDate yesterday = LocalDate.now().minusDays(BigInteger.ONE.intValue());
		// 起始时间
		LocalDate start = yesterday.minusDays(14);

		// 最近十四天数据
		LambdaQueryWrapper<FgInventoryReportSnapEntity> lastFourteenDay = new LambdaQueryWrapper<FgInventoryReportSnapEntity>()
			.between(FgInventoryReportSnapEntity::getSnapDate, start, yesterday)
			.orderByDesc(FgInventoryReportSnapEntity::getSnapDate);
		List<FgInventoryReportSnapEntity> lastFourteenDayData = Optional.ofNullable(this.list(lastFourteenDay)).orElse(new ArrayList<>());

		log.info("存快照报表 获取最近十四天数据 起始:{},截止:{},数据:{}",start,yesterday,JsonUtil.toJson(lastFourteenDayData));


		List<FgInventoryReportSnapItemVO.InventorySnapItemVO> details = Lists.newArrayList();
		FgInventoryReportSnapEntity first = null,last;
		int continuousDay = -1,mark = 0, isGrowing = 0;
		boolean flag = true;

		for (int i = 0; i < lastFourteenDayData.size(); i++) {
			if (i == 0) {
				first = lastFourteenDayData.get(i);
			}
			FgInventoryReportSnapEntity tmp = lastFourteenDayData.get(i);
			details.add(new FgInventoryReportSnapItemVO.InventorySnapItemVO(tmp.getSnapDate(),tmp.getInbound(),tmp.getOutbound(),tmp.getTotal()));

			if (flag && i != 0){

				last = lastFourteenDayData.get(i-1);
				isGrowing = Long.compare(tmp.getInbound(),last.getInbound());
				// 第一天与第二天对比 可以确定是增长还是下降
				if (i == 1){
					mark = isGrowing;
				}
				// 非第一天且与上次对比结果一致,认为累计增长(或下降)
				if (mark == isGrowing ) {
					continuousDay++;
				}else{
					flag = false;
				}
			}
		}

		FgInventoryReportSnapItemVO result = new FgInventoryReportSnapItemVO();
//		result.setInboundOfMonth(monthTotal.toString());
		result.setSnapDate(DateUtil.toDate(yesterday));
		result.setContinuous((mark  == -1)  ? ""+continuousDay : "-"+continuousDay );
		result.setDetails(details);
		result.setTotal(Optional.ofNullable(first).orElse(new FgInventoryReportSnapEntity()).getTotal().toString());

		if (StringUtils.equalsIgnoreCase(mode, Week.name())) {

			List<FgInventoryReportSnapEntity> weekData = Optional.ofNullable(this.list(new LambdaQueryWrapper<FgInventoryReportSnapEntity>()
				.between(FgInventoryReportSnapEntity::getSnapDate, yesterday.with(DayOfWeek.MONDAY), yesterday.with(DayOfWeek.SUNDAY))))
				.orElse(new ArrayList<>());
			Long weekInTotal = weekData.stream().map(FgInventoryReportSnapEntity::getInbound).reduce(BigInteger.ZERO.longValue(), Long::sum);
			Long weekOutTotal = weekData.stream().map(FgInventoryReportSnapEntity::getOutbound).reduce(BigInteger.ZERO.longValue(), Long::sum);

			log.info("库存快照报表 获取本周入库总量:{} 获取本周出库总量 {}",weekInTotal,weekOutTotal);

			setInventory(result,weekInTotal,weekOutTotal);
		}else if (StringUtils.equalsIgnoreCase(mode, Month.name())) {
			//月初
			LocalDate firstDayOfMonth = yesterday.with(TemporalAdjusters.firstDayOfMonth());
			//月末
			LocalDate lastDayOfMonth = yesterday.with(TemporalAdjusters.lastDayOfMonth());
			// 当月数据

			List<FgInventoryReportSnapEntity> monthData = Optional.ofNullable(this.list(new LambdaQueryWrapper<FgInventoryReportSnapEntity>()
				.between(FgInventoryReportSnapEntity::getSnapDate, firstDayOfMonth, lastDayOfMonth)))
				.orElse(new ArrayList<>());
			Long monthInTotal = monthData.stream().map(FgInventoryReportSnapEntity::getInbound).reduce(BigInteger.ZERO.longValue(), Long::sum);
			Long monthOutTotal = monthData.stream().map(FgInventoryReportSnapEntity::getOutbound).reduce(BigInteger.ZERO.longValue(), Long::sum);

			log.info("库存快照报表 获取当月入库总量:{} 获取当月出库总量 {}",monthInTotal,monthOutTotal);

			setInventory(result,monthInTotal,monthOutTotal);

		}else{

			if (Objects.nonNull(first)){
				setInventory(result,first.getInbound(),first.getOutbound());
			}
		}
		return result;
	}

	private void setInventory(FgInventoryReportSnapItemVO result, Long inTotal, Long outTotal) {
		result.setInbound(inTotal.toString());
		result.setOutbound(outTotal.toString());
		result.setNetWorth(String.valueOf(inTotal-outTotal));
	}
}
