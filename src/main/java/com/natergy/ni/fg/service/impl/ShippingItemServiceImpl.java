/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.base.cache.DepotCache;
import com.natergy.ni.base.entity.DepotInfo;
import com.natergy.ni.fg.entity.ShippingItemEntity;
import com.natergy.ni.fg.mapper.ShippingItemMapper;
import com.natergy.ni.fg.service.IShippingItemService;
import com.natergy.ni.fg.vo.ShippingItemVO;
import com.natergy.ni.product.entity.ProductSku;
import com.natergy.ni.product.service.ProductSkuService;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.enums.DictBizEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.stereotype.Service;

/**
 * 成品库管理-发货明细 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
@RequiredArgsConstructor
public class ShippingItemServiceImpl extends
	BaseServiceImpl<ShippingItemMapper, ShippingItemEntity> implements
	IShippingItemService {

	private final ProductSkuService productSkuService;

	@Override
	public IPage<ShippingItemVO> selectshippingItemPage(IPage<ShippingItemVO> page,
		ShippingItemVO shippingItem) {
		return page.setRecords(baseMapper.selectShippingItemPage(page, shippingItem));
	}

	/**
	 * @param shippingId
	 * @return
	 */
	@Override
	public List<ShippingItemVO> selectListByShippingId(Long shippingId) {
		List<ShippingItemEntity> items = list(Wrappers.<ShippingItemEntity>lambdaQuery()
			.eq(ShippingItemEntity::getShippingId, shippingId)
			.orderByDesc(ShippingItemEntity::getId));
		List<Long> skuIds = items.stream().map(ShippingItemEntity::getSkuId)
			.filter(Objects::nonNull).collect(Collectors.toList());
		Map<Long, ProductSku> skuMap = new HashMap<>();
		if (!skuIds.isEmpty()) {
			List<ProductSku> skus = productSkuService.listByIds(skuIds);
			skuMap.putAll(skus.stream().collect(Collectors.toMap(ProductSku::getId, sku -> sku)));
		}
		return items.stream().map(item -> {
			ShippingItemVO shippingItemVO = new ShippingItemVO();
			BeanUtil.copyProperties(item, shippingItemVO);
			DepotInfo depotInfo = DepotCache.getById(item.getDepotId());
			if (depotInfo != null) {
				shippingItemVO.setDepotName(depotInfo.getName());
			}
			ProductSku sku = skuMap.getOrDefault(item.getSkuId(), new ProductSku());
			shippingItemVO.setSpecText(sku.getSpecText());
			shippingItemVO.setPackageText(sku.getPackageText());
			shippingItemVO.setInnerPackageText(sku.getInnerPackageText());
			shippingItemVO.setQualityLevel(sku.getQualityLevel());
			shippingItemVO.setQualityLevelText(
				DictBizCache.getValue(DictBizEnum.NI_PRODUCT_SKU_QUALITY_LEVEL,
					sku.getQualityLevel()));
			shippingItemVO.setCapacity(sku.getCapacity());
			return shippingItemVO;
		}).collect(Collectors.toList());
	}


}
