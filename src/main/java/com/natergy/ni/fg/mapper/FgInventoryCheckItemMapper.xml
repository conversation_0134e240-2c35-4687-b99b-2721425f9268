<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.fg.mapper.FgInventoryCheckItemMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="niFgInventoryCheckItemResultMap"
        type="com.natergy.ni.fg.vo.FgInventoryCheckItemVO">
        <result column="id" property="id"/>
        <result column="sku_id" property="skuId"/>
        <result column="system_qty" property="systemQty"/>
        <result column="spec" property="spec"/>
        <result column="package_id" property="packageId"/>
        <result column="inner_package_id" property="innerPackageId"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="actual_qty" property="actualQty"/>
        <result column="variance" property="variance"/>
        <result column="check_id" property="checkId"/>
        <result column="package_text" property="packageText"/>
        <result column="inner_package_text" property="innerPackageText"/>
        <result column="sku_text" property="skuText"/>
        <result column="spec_text" property="specText"/>
    </resultMap>


    <select id="selectNiFgInventoryCheckItemPage" resultMap="niFgInventoryCheckItemResultMap">
        select *
        from ni_fg_inventory_check_item
        where is_deleted = 0
    </select>

    <select id="sumVarianceGroupByCheckId"
        resultType="com.natergy.ni.fg.dto.FgInventoryCheckItemSumVarianceDTO"><![CDATA[
      select check_id,
      sum(iif(variance>0, variance, 0)) as overage,
      sum(iif(variance<0,variance,0)) as shortage
      from ni_fg_inventory_check_item
      where check_id in
      ]]><foreach item="item" collection="checkIds" separator="," open="(" close=")">
        #{item}
      </foreach>
      and is_deleted = 0
      group by check_id
    </select>
</mapper>
