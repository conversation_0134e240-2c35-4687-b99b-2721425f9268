<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.fg.mapper.FgInventoryCheckSnapshotMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="niFgInventoryCheckSnapshotResultMap"
        type="com.natergy.ni.fg.vo.FgInventoryCheckSnapshotVO">
        <result column="id" property="id"/>
        <result column="sku_id" property="skuId"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="num" property="num"/>
        <result column="depot_id" property="depotId"/>
        <result column="check_id" property="checkId"/>
    </resultMap>


    <select id="selectNiFgInventoryCheckSnapshotPage"
        resultMap="niFgInventoryCheckSnapshotResultMap">
        select *
        from ni_fg_inventory_check_snapshot
        where is_deleted = 0
    </select>

    <insert id="build">
      INSERT INTO ni_fg_inventory_check_snapshot
      (sku_id, remark, tenant_id, is_deleted, num, depot_id, check_id)
      SELECT sku_id,
             remark,
             tenant_id,
             0,
             num,
             depot_id,
             #{checkId} AS check_id
      FROM ni_fg_inventory_summary
      WHERE is_deleted = 0
        and depot_id = #{depotId}
    </insert>

    <select id="selectCheckSnapshotList"  resultType="com.natergy.ni.fg.vo.FgInventoryCheckSnapshotVO">
        select snapshot.*,
        sku.spec_text,
        sku.material_id,
        sku.material_code,
        sku.specification as spec,
        sku.package_id,
        sku.inner_package_id,
        sku.capacity,
        sku.package_text,
        sku.inner_package_text,
        sku.quality_level,
        sku.unit,
        sku.capacity
        from ni_fg_inventory_check_snapshot snapshot
        left join ni_product_sku sku on sku.id = snapshot.sku_id and sku.is_deleted = 0
        where snapshot.is_deleted = 0
        <if test="param2.checkId!=null">
            and snapshot.check_id = #{param2.checkId}
        </if>
        <if test="param2.depotId!=null">
            and snapshot.depot_id = #{param2.depotId}
        </if>
        <if test="param2.skuId!=null">
            and snapshot.sku_id = #{param2.skuId}
        </if>
        <if test="param2.status!=null">
            and snapshot.status = #{param2.status}
        </if>
        <if test="param2.remark!=null and param2.remark!=''">
            <bind name="remarkLike" value="'%'+pram2.remark+'%'"/>
            and snapshot.remark like#{remarkLike}
        </if>
        <if test="param2.materialCode!=null and param2.materialCode!=''">
            <bind name="materialCodeLike" value="'%'+pram2.materialCode+'%'"/>
            and sku.material_code like #{materialCodeLike}
        </if>
        <if test="param2.materialId!=null">
            and sku.material_id = #{param2.materialId}
        </if>
        <if test="param2.spec!=null and param2.spec!=''">
            <bind name="specLike" value="'%'+pram2.spec+'%'"/>
            and sku.specification like #{specLike}
        </if>
        <if test="param2.packageId!=null">
            and sku.package_id = #{param2.packageId}
        </if>
        <if test="param2.innerPackageId!=null">
            and sku.inner_package_id = #{param2.innerPackageId}
        </if>
        <if test="param2.qualityLevel!=null">
            and sku.quality_level = #{param2.qualityLevel}
        </if>
        <if test="param2.packageText!=null and param2.packageText!=''">
            <bind name="packageTextLike" value="'%'+param2.packageText+'%'"/>
            and sku.package_text like #{packageTextLike}
        </if>
        <if test="param2.innerPackageText!=null and param2.innerPackageText!=''">
            <bind name="innerPackageTextLike" value="'%'+param2.innerPackageText+'%'"/>
            and sku.inner_package_text like #{innerPackageTextLike}
        </if>
        <if test="param2.id!=null">
            and snapshot.id = #{param2.id}
        </if>
        <if test="param2.skuIds!=null and param2.skuIds.size()>0">
            and snapshot.sku_id in
            <foreach item="skuId" index="index" collection="param2.skuIds" separator="," open="("
                close=")">
                #{skuId}
            </foreach>
        </if>
        order by snapshot.id desc
    </select>
</mapper>
