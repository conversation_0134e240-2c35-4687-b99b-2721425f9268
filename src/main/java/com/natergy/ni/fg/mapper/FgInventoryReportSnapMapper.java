package com.natergy.ni.fg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.natergy.ni.fg.entity.FgInventoryReportSnapEntity;
import com.natergy.ni.fg.vo.FgInventoryReportSnapItemVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface FgInventoryReportSnapMapper extends BaseMapper<FgInventoryReportSnapEntity> {

	@Select("SELECT " +
		"    sku.id AS sku, " +
		"    sku.name AS skuName, " +
		"    SUM(CASE WHEN t.type IN ('0101','0102','0103','0104','0106','0199') " +
		"            THEN t.total ELSE 0 END) AS inboundTotal, " +
		"    SUM(CASE WHEN t.type IN ('0201','0202','0203','0204','0205','0206','0207','0208','0299') " +
		"            THEN t.total ELSE 0 END) AS outboundTotal " +
		"FROM ni_fg_transaction t " +
		"JOIN ni_fg_transaction_item item ON t.id = item.transaction_id " +
		"JOIN ni_product_sku sku ON sku.id = item.sku_id " +
		"WHERE t.is_deleted = 0 " +
		" AND t.op_date = #{opDate}" +
		"  AND t.type IN (    " +
		"    '0101','0102','0103','0104','0106','0199', " +
		"    '0201','0202','0203','0204','0205','0206','0207','0208','0299' " +
		"  ) " +
		"GROUP BY sku.id, sku.name")
	List<FgInventoryReportSnapItemVO.InventorySnapItemVO> sumTotalGroupBySku(@Param("opDate") String opDate);

	@Select("select s.sku_id as sku,p.name as skuName,sum(s.num) as total from ni_fg_inventory_summary s " +
		"join ni_product_sku p on p.id = s.sku_id " +
		"where s.is_deleted = 0 " +
		"group by s.sku_id,p.name")
	List<FgInventoryReportSnapItemVO.InventorySnapItemVO>  dailySkuStock();
}
