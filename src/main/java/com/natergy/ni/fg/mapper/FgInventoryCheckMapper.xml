<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.fg.mapper.FgInventoryCheckMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="niFgInventoryCheckResultMap" type="com.natergy.ni.fg.vo.FgInventoryCheckVO">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="serial_no" property="serialNo"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="op_user_id" property="opUserId"/>
        <result column="start_date" property="startDate"/>
        <result column="complete_time" property="completeTime"/>
        <result column="depot_id" property="depotId"/>
    </resultMap>


    <select id="selectNiFgInventoryCheckPage" resultMap="niFgInventoryCheckResultMap">
        select *
        from ni_fg_inventory_check
        where is_deleted = 0
    </select>
</mapper>
