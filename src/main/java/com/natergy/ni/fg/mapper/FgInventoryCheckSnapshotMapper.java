/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.fg.dto.FgInventoryCheckSnapshotReqDTO;
import com.natergy.ni.fg.entity.FgInventoryCheckSnapshotEntity;
import com.natergy.ni.fg.vo.FgInventoryCheckSnapshotVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 成品库管理-盘点快照 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-10
 */
public interface FgInventoryCheckSnapshotMapper extends BaseMapper<FgInventoryCheckSnapshotEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param niFgInventoryCheckSnapshot
	 * @return
	 */
	List<FgInventoryCheckSnapshotVO> selectNiFgInventoryCheckSnapshotPage(IPage page,
		FgInventoryCheckSnapshotVO niFgInventoryCheckSnapshot);

	/**
	 * 构建盘点快照
	 *
	 * @param checkId
	 * @param depotId
	 * @return
	 */
	void build(Long checkId, Long depotId);

	List<FgInventoryCheckSnapshotVO> selectCheckSnapshotList(IPage<FgInventoryCheckSnapshotVO> page,
		FgInventoryCheckSnapshotReqDTO q);

	@Select("select sum(num) from ni_fg_inventory_check_snapshot where is_deleted=0 and check_id = #{checkId}")
	Long sumByCheckId(@Param("checkId") Long checkId);
}
