/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.wrapper;

import com.natergy.ni.fg.entity.ShippingItemEntity;
import com.natergy.ni.fg.vo.ShippingItemVO;
import java.util.Objects;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 成品库管理-发货明细 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public class ShippingItemWrapper extends BaseEntityWrapper<ShippingItemEntity, ShippingItemVO> {

	public static ShippingItemWrapper build() {
		return new ShippingItemWrapper();
	}

	@Override
	public ShippingItemVO entityVO(ShippingItemEntity shippingItem) {
		ShippingItemVO shippingItemVO = Objects.requireNonNull(
			BeanUtil.copy(shippingItem, ShippingItemVO.class));

		//User createUser = UserCache.getUser(shippingItem.getCreateUser());
		//User updateUser = UserCache.getUser(shippingItem.getUpdateUser());
		//shippingItemVO.setCreateUserName(createUser.getName());
		//shippingItemVO.setUpdateUserName(updateUser.getName());

		return shippingItemVO;
	}


}
