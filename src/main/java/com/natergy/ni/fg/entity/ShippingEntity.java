/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fg.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.natergy.ni.pa.util.RestTemplateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 成品库-发货管理 实体类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@TableName("ni_fg_shipping")
@ApiModel(value = "shipping对象", description = "成品库-发货管理")
@EqualsAndHashCode(callSuper = true)
public class ShippingEntity extends TenantEntity {

	/**
	 * 国内发货
	 */
	public static final String TYPE_IN = "1";
	/**
	 * 国外发货
	 */
	public static final String TYPE_OUT = "2";
	/**
	 * 外库补货
	 */
	public static final String TYPE_REPLENISH = "3";

	private static final long serialVersionUID = -3769417639228666805L;

	/**
	 * 类型映射
	 */
	public static final Map<String, String> TYPE_MAP;

	static {
		TYPE_MAP = new HashMap<>();
		TYPE_MAP.put(TYPE_IN, "国内发货");
		TYPE_MAP.put(TYPE_OUT, "国外发货");
		TYPE_MAP.put(TYPE_REPLENISH, "外库补货");
	}

	/**
	 * 状态-待审核
	 */
	public static final Integer STATUS_WAIT = 1;
	/**
	 * 状态-审核通过
	 */
	public static final Integer STATUS_PASS = 2;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 发货编号
	 */
	@ApiModelProperty(value = "发货编号")
	private String serialNo;
	/**
	 * 发货类型
	 */
	@ApiModelProperty(value = "发货类型")
	private String type;
	/**
	 * 发货日期
	 */
	@ApiModelProperty(value = "发货日期")
	private LocalDate date;
	/**
	 * 发货人
	 */
	@ApiModelProperty(value = "发货人")
	private Long opUserId;
	/**
	 * 件数
	 */
	@ApiModelProperty(value = "件数")
	private Integer num;
	/**
	 * 重量
	 */
	@ApiModelProperty(value = "重量")
	private BigDecimal weight;
	/**
	 * 来源id
	 */
	private Long sourceId;

}
