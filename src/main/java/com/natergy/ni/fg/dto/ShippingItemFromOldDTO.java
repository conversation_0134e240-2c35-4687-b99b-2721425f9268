package com.natergy.ni.fg.dto;

import com.natergy.ni.old.entity.XiaoShouDingDanMingXiEntity;
import com.natergy.ni.old.entity.XiaoShouWaiKuBuHuoMingXiEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class ShippingItemFromOldDTO implements Serializable {

	private static final long serialVersionUID = 6719462574534992869L;
	private Long skuId;
	private String materialCode;
	private String specText;
	private String packageText;
	private String innerPackageText;
	private String qualityLevel;
	private String qualityLevelText;
	private Integer num;
	private BigDecimal weight;
	private String remark;
	private String batchNo;
	private Integer batchNoNum;
	private BigDecimal capacity;

	public ShippingItemFromOldDTO(XiaoShouDingDanMingXiEntity mingXi) {
		super();
		this.materialCode = mingXi.getYyCunHuoBianMa();
		this.specText = mingXi.getGuigeMm();
		this.packageText = mingXi.getWaiBaoZhuang();
		this.innerPackageText = mingXi.getBaoZhuang();
		this.num = mingXi.getJianShu().intValue();
		this.weight = BigDecimal.valueOf(mingXi.getJingZhong());
		this.remark = mingXi.getBeiZhu();
	}

	public ShippingItemFromOldDTO(XiaoShouWaiKuBuHuoMingXiEntity mingXi) {
		super();
		this.materialCode = mingXi.getYyCunHuoBianMa();
		this.specText = mingXi.getGuiGeMm();
		this.packageText = mingXi.getWaiBaoZhuang();
		this.innerPackageText = mingXi.getBaoZhuang();
		this.num = mingXi.getJianShu().intValue();
		this.weight = BigDecimal.valueOf(mingXi.getJingZhongKg());
		this.remark = mingXi.getBeiZhu();
	}
}
