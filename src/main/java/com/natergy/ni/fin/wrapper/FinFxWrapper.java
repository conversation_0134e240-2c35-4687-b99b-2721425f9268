/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.wrapper;

import com.natergy.ni.fin.cache.FinLedgerCache;
import com.natergy.ni.fin.entity.FinLedgerEntity;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.cache.DictCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.enums.DictBizEnum;
import org.springblade.common.enums.DictEnum;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.fin.entity.FinFxEntity;
import com.natergy.ni.fin.vo.FinFxVO;
import java.util.Objects;
import org.springblade.modules.system.entity.User;

/**
 * 资金结汇单 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public class FinFxWrapper extends BaseEntityWrapper<FinFxEntity, FinFxVO> {

	public static FinFxWrapper build() {
		return new FinFxWrapper();
	}

	@Override
	public FinFxVO entityVO(FinFxEntity finFx) {
		FinFxVO finFxVO = Objects.requireNonNull(BeanUtil.copy(finFx, FinFxVO.class));
		User createUser = UserCache.getUser(finFx.getCreateUser());
		if (createUser != null) {
			finFxVO.setCreateUserName(createUser.getName());
		}
		finFxVO.setStatusText(DictCache.getValue(DictEnum.DATA_STATUS, finFx.getStatus()));
		finFxVO.setPostStatusText(DictBizCache.getValue("ni_post_status", finFx.getPostStatus()));
		finFxVO.setBrandText(DictBizCache.getValue(DictBizEnum.NI_BRAND,
			StringUtils.isNotBlank(finFx.getBrand()) ? finFx.getBrand() : "1"));
		FinLedgerEntity sourceLedger = FinLedgerCache.getById(finFx.getSourceLedgerId());
		if (sourceLedger != null) {
			finFxVO.setSourceLedger(sourceLedger.getName());
		}
		FinLedgerEntity targetLedger = FinLedgerCache.getById(finFx.getTargetLedgerId());
		if (targetLedger != null) {
			finFxVO.setTargetLedger(targetLedger.getName());
		}
		finFxVO.setSourceCurrencyText(
			DictCache.getValue(DictEnum.CURRENCY, finFx.getSourceCurrency()));
		finFxVO.setTargetCurrencyText(
			DictCache.getValue(DictEnum.CURRENCY, finFx.getTargetCurrency()));
		return finFxVO;
	}


}
