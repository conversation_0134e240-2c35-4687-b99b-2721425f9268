/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.wrapper;

import com.natergy.ni.fin.entity.VoucherEntity;
import com.natergy.ni.fin.vo.VoucherVO;
import java.util.Objects;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.system.entity.User;

/**
 * 财务管理-凭证 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public class VoucherWrapper extends BaseEntityWrapper<VoucherEntity, VoucherVO> {

	public static VoucherWrapper build() {
		return new VoucherWrapper();
	}

	@Override
	public VoucherVO entityVO(VoucherEntity voucher) {
		VoucherVO voucherVO = Objects.requireNonNull(BeanUtil.copy(voucher, VoucherVO.class));
		User createUser = UserCache.getUser(voucher.getCreateUser());
		User approver = UserCache.getUser(voucher.getApprover());
		if (createUser != null) {
			voucherVO.setCreateUserName(createUser.getRealName());
		}
		if (approver != null) {
			voucherVO.setApproverName(approver.getRealName());
		}
		return voucherVO;
	}


}
