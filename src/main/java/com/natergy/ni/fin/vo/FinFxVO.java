/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.vo;

import com.natergy.ni.fin.entity.FinFxEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资金结汇单 视图实体类
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinFxVO extends FinFxEntity {

	private static final long serialVersionUID = 1L;
	private String brandText;
	private String sourceLedger;
	private String sourceCurrencyText;
	private String createUserName;

	private String targetLedger;
	private String targetCurrencyText;
	private String statusText;
	private String postStatusText;

}
