/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.service;

import com.natergy.ni.fin.dto.FinFxDTO;
import com.natergy.ni.fin.entity.FinFxEntity;
import com.natergy.ni.fin.vo.FinFxVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 资金结汇单 服务类
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface IFinFxService extends BaseService<FinFxEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param finFx
	 * @return
	 */
	IPage<FinFxVO> selectFinFxPage(IPage<FinFxVO> page, FinFxVO finFx);


	boolean red(Long id);

	boolean toVoid(Long id);

	boolean post(Long id);

}
