/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.fin.dto.VoucherRequestFormDTO;
import com.natergy.ni.fin.entity.VoucherEntity;
import com.natergy.ni.fin.vo.VoucherVO;
import java.util.List;
import javax.validation.Valid;
import org.springblade.core.mp.base.BaseService;

/**
 * 财务管理-凭证 服务类
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface IVoucherService extends BaseService<VoucherEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param voucher
	 * @return
	 */
	IPage<VoucherVO> selectVoucherPage(IPage<VoucherVO> page, VoucherVO voucher);


	boolean save1(@Valid VoucherRequestFormDTO voucher);

	String getSerialNo(String yyyyMMdd);

	boolean updateById1(@Valid VoucherRequestFormDTO voucher);

	boolean deleteLogic1(List<Long> ids);

	boolean audit(List<Long> ids);

	boolean posting(List<Long> ids);

	VoucherVO detailById(Long id);

	boolean submit(List<Long> ids);

	boolean overrule(List<Long> ids);

	boolean auditBack(List<Long> ids);

	boolean removal(Long id, String remark);
}
