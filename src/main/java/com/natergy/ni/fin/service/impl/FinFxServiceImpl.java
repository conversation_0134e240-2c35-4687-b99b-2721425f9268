/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.fin.dto.FinFxDTO;
import com.natergy.ni.fin.dto.VoucherRequestFormDTO;
import com.natergy.ni.fin.entity.FinFxEntity;
import com.natergy.ni.fin.mapper.FinFxMapper;
import com.natergy.ni.fin.service.IFinFxService;
import com.natergy.ni.fin.service.IVoucherService;
import com.natergy.ni.fin.vo.FinFxVO;
import io.jsonwebtoken.lang.Assert;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资金结汇单 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
@RequiredArgsConstructor
public class FinFxServiceImpl extends BaseServiceImpl<FinFxMapper, FinFxEntity> implements
	IFinFxService {

	private final IVoucherService voucherService;

	@Override
	public IPage<FinFxVO> selectFinFxPage(IPage<FinFxVO> page, FinFxVO finFx) {
		return page.setRecords(baseMapper.selectFinFxPage(page, finFx));
	}

	/**
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean red(Long id) {
		FinFxEntity fx = this.getById(id);
		Assert.isTrue(fx != null, "未找到此单据！");
		Assert.isTrue(Objects.equals(fx.getStatus(), WfEntity.STATUS_FINISH), "此单据未审核完成！");
		Assert.isTrue(Objects.equals(fx.getPostStatus(), FinFxEntity.POST_STATUS_POST),
			"此单据未结转！");
		Assert.isTrue(!Objects.equals(fx.getPostStatus(), FinFxEntity.POST_STATUS_REVERSE),
			"此单据已冲销！");
		Assert.isTrue(!Objects.equals(fx.getPostStatus(), FinFxEntity.POST_STATUS_TERMINATION),
			"此单据已作废！");
		return this.update(Wrappers.<FinFxEntity>lambdaUpdate().eq(FinFxEntity::getId, id)
			.set(FinFxEntity::getPostStatus, FinFxEntity.POST_STATUS_REVERSE));
	}


	/**
	 * @param id
	 * @return
	 */
	@Override
	public boolean toVoid(Long id) {
		FinFxEntity fx = getById(id);
		Assert.isTrue(fx != null, "未找到此单据！");
		Assert.isTrue(Objects.equals(fx.getStatus(), WfEntity.STATUS_FINISH), "此单据未审核完成！");
		Assert.isTrue(!Objects.equals(fx.getPostStatus(), FinFxEntity.POST_STATUS_POST),
			"此单据已结转！");
		Assert.isTrue(!Objects.equals(fx.getPostStatus(), FinFxEntity.POST_STATUS_REVERSE),
			"此单据已冲销！");
		Assert.isTrue(!Objects.equals(fx.getPostStatus(), FinFxEntity.POST_STATUS_TERMINATION),
			"此单据已作废！");
		return this.update(Wrappers.<FinFxEntity>lambdaUpdate().eq(FinFxEntity::getId, id)
			.set(FinFxEntity::getPostStatus, FinFxEntity.POST_STATUS_TERMINATION));
	}

	/**
	 * @param id
	 * @return
	 */
	@Override
	public boolean post(Long id) {
		FinFxEntity fx = getById(id);
		Assert.isTrue(fx != null, "未找到此单据！");
		Assert.isTrue(Objects.equals(fx.getStatus(), WfEntity.STATUS_FINISH), "此单据未审核完成！");
		Assert.isTrue(!Objects.equals(fx.getPostStatus(), FinFxEntity.POST_STATUS_DRAFT),
			"此单据执行状态错误！");
		boolean res = this.update(Wrappers.<FinFxEntity>lambdaUpdate().eq(FinFxEntity::getId, id)
			.set(FinFxEntity::getPostStatus, FinFxEntity.POST_STATUS_POST));
		Assert.isTrue(res, "执行失败！");
		VoucherRequestFormDTO blue = buildBlueVoucher(fx);
//		res = voucherService.save1(blue);
//		Assert.isTrue(res, "生成记账凭证失败！");
		return true;
	}

	private VoucherRequestFormDTO buildBlueVoucher(FinFxEntity fx) {
		VoucherRequestFormDTO res = new VoucherRequestFormDTO();
		res.setType("转");
		res.setDate(fx.getOpDate());
		res.setSummary(
			String.format("结汇 %s,%s %s->%s;%s", fx.getSerialNo(), fx.getSourceCurrency(),
				fx.getSourceAmount(), fx.getTargetCurrency(), fx.getTitle()));
		res.setFiscalYear(fx.getOpDate().getYear());
		res.setStatus(VoucherRequestFormDTO.STATUS_WAIT_AUDIT);
		return res;
	}


}
