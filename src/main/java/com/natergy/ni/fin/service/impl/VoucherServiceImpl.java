/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.fin.dto.VoucherDetailDTO;
import com.natergy.ni.fin.dto.VoucherRequestFormDTO;
import com.natergy.ni.fin.entity.VoucherDetailEntity;
import com.natergy.ni.fin.entity.VoucherEntity;
import com.natergy.ni.fin.mapper.VoucherMapper;
import com.natergy.ni.fin.service.IVoucherDetailService;
import com.natergy.ni.fin.service.IVoucherService;
import com.natergy.ni.fin.vo.VoucherVO;
import com.natergy.ni.fin.wrapper.VoucherDetailWrapper;
import com.natergy.ni.fin.wrapper.VoucherWrapper;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * 财务管理-凭证 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Service
@RequiredArgsConstructor
public class VoucherServiceImpl extends BaseServiceImpl<VoucherMapper, VoucherEntity> implements
	IVoucherService {

	private final IVoucherDetailService voucherDetailService;

	@Override
	public IPage<VoucherVO> selectVoucherPage(IPage<VoucherVO> page, VoucherVO voucher) {
		return page.setRecords(baseMapper.selectVoucherPage(page, voucher));
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean save1(VoucherRequestFormDTO voucher) {
		VoucherEntity voucherEntity = new VoucherEntity();
		BeanUtils.copyProperties(voucher, voucherEntity);
		boolean res = this.save(voucherEntity);
		Assert.isTrue(res, "保存凭证失败！");
		List<VoucherDetailDTO> details = voucher.getDetails();
		for (int i = 0; i < details.size(); i++) {
			details.get(i).setRow(i + 1);
			details.get(i).setVoucherId(voucherEntity.getId());
		}
		res = voucherDetailService.saveBatch(
			new ArrayList<>(details));
		Assert.isTrue(res, "保存凭证明细失败！");
		return true;
	}

	/**
	 * @param yyyyMMdd
	 * @return
	 */
	@Override
	public String getSerialNo(String yyyyMMdd) {
		Integer maxNum = baseMapper.getLastByDate(yyyyMMdd);
		int nextNum = (maxNum == null) ? 1 : maxNum + 1;
		return String.format("%03d", nextNum);
	}

	/**
	 * @param voucher
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean updateById1(VoucherRequestFormDTO voucher) {
		// 1. 更新主表
		VoucherEntity voucherEntity = new VoucherEntity();
		BeanUtils.copyProperties(voucher, voucherEntity);
		boolean res = this.updateById(voucherEntity);
		Assert.isTrue(res, "更新凭证失败！");

		// 2. 更新明细数据
		List<VoucherDetailDTO> details = voucher.getDetails();
		Long voucherId = voucherEntity.getId();

		// 2.1 删除旧的明细数据
		voucherDetailService.removeByVoucherId(voucherId);
		// 2.2 设置明细行号（row）
		for (int i = 0; i < details.size(); i++) {
			details.get(i).setId(null);
			details.get(i).setRow(i + 1); // 行号从 1 开始
			details.get(i).setVoucherId(voucherId);
		}
		// 2.3 构建新的明细实体
		List<VoucherDetailEntity> detailEntities = new ArrayList<>(details);
		// 2.4 批量保存新的明细数据
		res = voucherDetailService.saveBatch(detailEntities);
		Assert.isTrue(res, "更新凭证明细失败！");
		return true;
	}

	/**
	 * @param ids
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean deleteLogic1(List<Long> ids) {
		boolean res = this.deleteLogic(ids);
		Assert.isTrue(res, "删除凭证失败！");
		res = voucherDetailService.removeBatchByVoucherIds(ids);
		Assert.isTrue(res, "删除凭证明细失败！");
		return true;
	}

	/**
	 * @param ids
	 * @return
	 */
	@Override
	public boolean audit(List<Long> ids) {
		return this.update(Wrappers.<VoucherEntity>lambdaUpdate().in(VoucherEntity::getId, ids)
			.eq(VoucherEntity::getStatus, VoucherEntity.STATUS_WAIT_AUDIT)
			.set(VoucherEntity::getStatus, VoucherEntity.STATUS_AUDITED)
			.set(VoucherEntity::getApprover, AuthUtil.getUserId()));
	}

	/**
	 * @param ids
	 * @return
	 */
	@Override
	public boolean posting(List<Long> ids) {
		return this.update(Wrappers.<VoucherEntity>lambdaUpdate().in(VoucherEntity::getId, ids)
			.eq(VoucherEntity::getStatus, VoucherEntity.STATUS_AUDITED)
			.set(VoucherEntity::getStatus, VoucherEntity.STATUS_POSTED));
	}

	/**
	 * @param id
	 * @return
	 */
	@Override
	public VoucherVO detailById(Long id) {
		VoucherEntity voucher = this.getById(id);
		if (voucher != null) {
			List<VoucherDetailEntity> detailEntities = voucherDetailService.list(
				Wrappers.<VoucherDetailEntity>lambdaQuery()
					.eq(VoucherDetailEntity::getVoucherId, id));
			VoucherVO res = VoucherWrapper.build().entityVO(voucher);
			res.setDetails(VoucherDetailWrapper.build().listVO(detailEntities));
			return res;
		}
		return null;
	}

	/**
	 * @param ids
	 * @return
	 */
	@Override
	public boolean submit(List<Long> ids) {
		return this.update(Wrappers.<VoucherEntity>lambdaUpdate().in(VoucherEntity::getId, ids)
			.eq(VoucherEntity::getStatus, VoucherEntity.STATUS_DRAFT)
			.set(VoucherEntity::getStatus, VoucherEntity.STATUS_WAIT_AUDIT));
	}

	/**
	 * @param ids
	 * @return
	 */
	@Override
	public boolean overrule(List<Long> ids) {
		return this.update(Wrappers.<VoucherEntity>lambdaUpdate().in(VoucherEntity::getId, ids)
			.eq(VoucherEntity::getStatus, VoucherEntity.STATUS_WAIT_AUDIT)
			.set(VoucherEntity::getStatus, VoucherEntity.STATUS_DRAFT));
	}

	/**
	 * @param ids
	 * @return
	 */
	@Override
	public boolean auditBack(List<Long> ids) {
		return this.update(Wrappers.<VoucherEntity>lambdaUpdate().in(VoucherEntity::getId, ids)
			.eq(VoucherEntity::getStatus, VoucherEntity.STATUS_AUDITED)
			.set(VoucherEntity::getStatus, VoucherEntity.STATUS_WAIT_AUDIT));
	}

	/**
	 * @param id
	 * @param remark
	 * @return
	 */
	@Override
	public boolean removal(Long id, String remark) {
		return this.update(Wrappers.<VoucherEntity>lambdaUpdate().eq(VoucherEntity::getId, id)
			.eq(VoucherEntity::getStatus, VoucherEntity.STATUS_AUDITED)
			.set(VoucherEntity::getStatus, VoucherEntity.STATUS_REVERSED)
			.set(VoucherEntity::getRemark, remark));
	}
}
