package com.natergy.ni.fin.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.natergy.ni.base.service.ISerialNoService;
import com.natergy.ni.fin.controller.CostApplyController;
import com.natergy.ni.fin.controller.PayableApplyController;
import com.natergy.ni.fin.entity.FinCostApply;
import com.natergy.ni.fin.service.ICostApplyService;
import com.natergy.ni.fin.vo.CostApplyVO;
import com.natergy.ni.por.entity.PorApply;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.FieldExtension;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.DelegateHelper;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component("WfCostApplyTaskListener")
public class WfCostApplyTaskListener implements TaskListener {

	private static final long serialVersionUID = -3144873293804096440L;
	private final ICostApplyService costApplyService;
	private final RuntimeService runtimeService;


	/**
	 * @param delegateTask
	 */
	@Override
	public void notify(DelegateTask delegateTask) {
		Map<String, Object> processVariables = delegateTask.getVariables();
		runtimeService.setVariable(delegateTask.getProcessInstanceId(), "status", WfEntity.STATUS_APPROVAL);
		if (processVariables.containsKey("id")) {
			costApplyService.update(
				Wrappers.<FinCostApply>lambdaUpdate()
					.eq(FinCostApply::getId, processVariables.get("id"))
					.set(FinCostApply::getStatus, processVariables.get("status")));
		}
	}
}
