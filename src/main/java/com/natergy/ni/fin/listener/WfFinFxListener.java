package com.natergy.ni.fin.listener;

import com.alibaba.fastjson.JSON;
import com.natergy.ni.fin.dto.FinFxDTO;
import com.natergy.ni.fin.entity.FinLoanEntity;
import com.natergy.ni.fin.service.IFinFxService;
import com.natergy.ni.fin.service.IFinLoanService;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.flowable.bpmn.model.FieldExtension;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component("WfFinFxListener")
public class WfFinFxListener implements ExecutionListener {

	private static final long serialVersionUID = 4471213352093466735L;

	private final IFinFxService finFxService;

	/**
	 * @param execution
	 */
	@Override
	public void notify(DelegateExecution execution) {
		Map<String, Object> vals = execution.getParent().getVariables();
		List<FieldExtension> extensions = execution.getCurrentFlowableListener()
			.getFieldExtensions();
		if (extensions != null && !extensions.isEmpty()) {
			Map<String, Object> extensionMap = extensions.stream()
				.collect(Collectors.toMap(FieldExtension::getFieldName,
					FieldExtension::getStringValue));
			vals.putAll(extensionMap);
		}
		String text = JSON.toJSONString(vals);
		FinFxDTO finFxDTO = JsonUtil.parse(text, FinFxDTO.class);
		finFxDTO.setProcessInsId(execution.getProcessInstanceId());
		finFxDTO.setProcessDefId(execution.getProcessDefinitionId());
		finFxDTO.setCurrentNode(execution.getCurrentFlowElement().getName());
		if (vals.get("serialNumber") != null) {
			finFxDTO.setSerialNo((String) vals.get("serialNumber"));
		}
		if (finFxDTO.getStatus() == null) {
			finFxDTO.setStatus(WfEntity.STATUS_SUBMIT);
			finFxDTO.setOpDate(LocalDate.now());
		}
		if (vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE) != null) {
			finFxDTO.setStatus(WfEntity.statusMap.get(
				vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE).toString()));
		}
		if (vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE) != null
			&& WfEntity.statusMap.get(
			vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE).toString()) != null) {
			finFxDTO.setStatus(WfEntity.statusMap.get(
				vals.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE).toString()));
		}
		finFxDTO.setTenantId(BladeConstant.ADMIN_TENANT_ID);
		finFxService.saveOrUpdate(finFxDTO);
		execution.setVariable("id", finFxDTO.getId());

	}
}
