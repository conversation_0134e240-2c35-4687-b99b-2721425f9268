package com.natergy.ni.fin.listener;

import static com.natergy.ni.base.entity.ContractEntity.TYPE_POR_NORMAL;
import static com.natergy.ni.base.entity.ContractEntity.TYPE_POR_ORDER;
import static com.natergy.ni.fin.entity.PaymentStatusSummary.PAYMENT_STATUS_APPLIED;
import static com.natergy.ni.fin.entity.PaymentStatusSummary.PAYMENT_STATUS_PAID;
import static com.natergy.ni.fin.entity.PaymentStatusSummary.PAYMENT_STATUS_PARTIAL_PAID;
import static com.natergy.ni.fin.entity.PaymentStatusSummary.PAYMENT_STATUS_UNPAID;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.base.entity.ContractEntity;
import com.natergy.ni.base.service.IContractService;
import com.natergy.ni.fin.entity.FinTransFlow;
import com.natergy.ni.fin.entity.PaySoaEntity;
import com.natergy.ni.fin.entity.PayableApplyEntity;
import com.natergy.ni.fin.entity.PaymentStatusSummary;
import com.natergy.ni.fin.event.paymentstatus.BusinessDocApprovedEvent;
import com.natergy.ni.fin.event.paymentstatus.BusinessDocApprovedEvent.BusinessType;
import com.natergy.ni.fin.event.paymentstatus.PayableApplyRejectedEvent;
import com.natergy.ni.fin.event.paymentstatus.PayableApplySubmitEvent;
import com.natergy.ni.fin.event.paymentstatus.PaymentConfirmedEvent;
import com.natergy.ni.fin.service.FinTransFlowService;
import com.natergy.ni.fin.service.IPaySoaService;
import com.natergy.ni.fin.service.IPayableApplyService;
import com.natergy.ni.fin.service.impl.PaymentStatusSummaryService;
import com.natergy.ni.por.entity.PorOrderItem;
import com.natergy.ni.por.service.IPorOrderItemService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.Assert;

@Component
@RequiredArgsConstructor
public class PaymentStatusSummaryListener {

	private final PaymentStatusSummaryService paymentStatusSummaryService;
	private final IPayableApplyService payableApplyService;
	private final IContractService contractService;
	private final IPaySoaService paySoaService;
	private final IPorOrderItemService porOrderItemService;
	private final FinTransFlowService finTransFlowService;

	/**
	 * 初始化事件
	 *
	 * @param event
	 */
	@TransactionalEventListener(fallbackExecution = true)
	public void summaryTableInitListener(BusinessDocApprovedEvent event) {
		PaymentStatusSummary init = new PaymentStatusSummary();
		init.setType(event.getType().name());
		init.setDocId(event.getDocId());
		init.setAmount(event.getAmount());
		init.setPaidAmount(BigDecimal.ZERO);
		init.setPaymentStatus(PAYMENT_STATUS_UNPAID);
		paymentStatusSummaryService.save(init);
	}

	/**
	 * 付款申请发起事件
	 *
	 * @param event
	 */
	@TransactionalEventListener(fallbackExecution = true)
	public void payableApplySubmitListener(PayableApplySubmitEvent event) {
		PayableApplyEntity payableApplyEntity = payableApplyService.getById(
			event.getPayableApplyId());
		updateSummaryWithPayableApply(payableApplyEntity);
	}

	/**
	 * 付款申请驳回，撤销等事件 因为涉及到可能会存在多条付款申请的情况，所以处理逻辑应该根据付款金额重新计算
	 *
	 * @param event
	 */
	@TransactionalEventListener(fallbackExecution = true)
	public void payableApplyRejectedListener(PayableApplyRejectedEvent event) {
		PayableApplyEntity payableApplyEntity = payableApplyService.getById(
			event.getPayableApplyId());
		updateSummaryWithPayableApply(payableApplyEntity);
	}

	/**
	 * 付款确认事件
	 *
	 * @param event
	 */
	@TransactionalEventListener(fallbackExecution = true)
	public void PaymentConfirmedListener(PaymentConfirmedEvent event) {
		FinTransFlow payable = finTransFlowService.getById(event.getPayableId());
		Assert.notNull(payable, "找不到对应的付款单");
		PayableApplyEntity payableApplyEntity = payableApplyService.getOne(
			Wrappers.<PayableApplyEntity>lambdaQuery()
				.eq(PayableApplyEntity::getId, payable.getSource()));
		updateSummaryWithPayableApply(payableApplyEntity);
	}

	public void updateSummaryWithPayableApply(@NonNull PayableApplyEntity payableApplyEntity) {
		List<Pair<BusinessType, List<Pair<Long, Triple<BigDecimal, BigDecimal, BigDecimal>>>>> types = new ArrayList<>();
		switch (payableApplyEntity.getType()) {
			case PayableApplyEntity.TYPE_CONTRACT:
				Long contractId = payableApplyEntity.getContractId();
				types = Collections.singletonList(
					Pair.of(BusinessType.POR_ORDER_ITEM,
						loadOrderItemAmountByContractId(contractId)));
				break;
			case PayableApplyEntity.TYPE_POR:
				Long soaId = payableApplyEntity.getPaySoaId();
				types = Collections.singletonList(
					Pair.of(BusinessType.POR_ORDER_ITEM, loadOrderItemAmountBySoaId(soaId)));
				break;
			case PayableApplyEntity.TYPE_COST_ITEM:
				//应付单不涉及采购，所以不做处理
				break;
			case PayableApplyEntity.TYPE_ADVANCE:
				Long orderId = payableApplyEntity.getPorOrderId();
				types = Collections.singletonList(
					Pair.of(BusinessType.POR_ORDER_ITEM, loadOrderItemAmountByOrderId(orderId)));
				break;
		}
		types.forEach(
			type -> {
				List<Pair<Long, Triple<BigDecimal, BigDecimal, BigDecimal>>> items = type.getRight();
				if (items != null && !items.isEmpty()) {
					updateSummaryWithPayable(type.getLeft(), items.stream()
						.collect(Collectors.toMap(Pair::getLeft, Pair::getRight)));
				}
			});
	}

	/**
	 * @param type
	 * @param docAmounts docId -> (amount, paidAmount, unpaidAmount) 业务金额/付款申请金额/付款金额
	 */
	private void updateSummaryWithPayable(BusinessType type,
		Map<Long, Triple<BigDecimal, BigDecimal, BigDecimal>> docAmounts) {
		if (docAmounts == null || docAmounts.isEmpty()) {
			return;
		}
		List<PaymentStatusSummary> summaries = paymentStatusSummaryService.list(
			Wrappers.<PaymentStatusSummary>lambdaQuery()
				.eq(PaymentStatusSummary::getType, type.name())
				.in(PaymentStatusSummary::getDocId,
					docAmounts.keySet()));
		if (summaries == null || summaries.isEmpty()) {
			return;
		}
		summaries.forEach(summary -> {
			Triple<BigDecimal, BigDecimal, BigDecimal> docAmount = docAmounts.get(
				summary.getDocId());
			summary.setAmount(docAmount.getLeft());
			summary.setPaidAmount(docAmount.getRight());
			if (summary.getPaidAmount().compareTo(summary.getAmount()) >= 0) {
				summary.setPaymentStatus(PAYMENT_STATUS_PAID);
			} else if (summary.getPaidAmount().compareTo(BigDecimal.ZERO) > 0) {
				summary.setPaymentStatus(PAYMENT_STATUS_PARTIAL_PAID);
			} else if (summary.getPaidAmount().compareTo(BigDecimal.ZERO) <= 0
				&& docAmount.getMiddle().compareTo(BigDecimal.ZERO) > 0) {
				summary.setPaymentStatus(PAYMENT_STATUS_APPLIED);
			} else {
				summary.setPaymentStatus(PAYMENT_STATUS_UNPAID);
			}
		});
		paymentStatusSummaryService.updateBatchById(summaries);
	}

	private List<Pair<Long, Triple<BigDecimal, BigDecimal, BigDecimal>>> loadOrderItemAmountByOrderId(
		Long orderId) {
		List<PayableApplyEntity> applies = payableApplyService.list(
			Wrappers.<PayableApplyEntity>lambdaQuery()
				.eq(PayableApplyEntity::getPorOrderId, orderId)
				.eq(PayableApplyEntity::getType, PayableApplyEntity.TYPE_ADVANCE)
				.notIn(PayableApplyEntity::getStatus, PayableApplyEntity.STATUS_DRAFT,
					PayableApplyEntity.STATUS_CANCEL, PayableApplyEntity.STATUS_TERMINATION));
		BigDecimal payableApplyAmount = applies.stream().map(PayableApplyEntity::getAmount)
			.filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		BigDecimal payAmount = BigDecimal.ZERO;
		if (!applies.isEmpty()) {
			List<FinTransFlow> payables = finTransFlowService.list(
				Wrappers.<FinTransFlow>lambdaQuery()
					.in(FinTransFlow::getSource, applies.stream().map(PayableApplyEntity::getId)
						.collect(Collectors.toList()))
					.eq(FinTransFlow::getType, FinTransFlow.TYPE_PAYABLE));
			payAmount = payables.stream().map(FinTransFlow::getAmount)
				.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		}
		List<PorOrderItem> porOrderItem = porOrderItemService.list(
			Wrappers.<PorOrderItem>lambdaQuery().eq(PorOrderItem::getOrderId, orderId));
		BigDecimal orderAmount = porOrderItem.stream().map(PorOrderItem::getAmount)
			.filter(Objects::nonNull)
			.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		boolean allPay = applies.stream()
			.allMatch(apply -> apply.getPayState() != null && apply.getPayState()
				.equals(PayableApplyEntity.PAY_STATE_FINISH));
		boolean allUnPay = applies.stream()
			.allMatch(apply -> apply.getPayState() == null || apply.getPayState()
				.equals(PayableApplyEntity.PAY_STATE_UN));
		//如果订单是全部付款,则明细的付款状态是全部付款
		if (allPay && orderAmount.compareTo(payableApplyAmount) <= 0) {
			return porOrderItem.stream().filter(item -> item.getAmount() != null)
				.map(item -> Pair.of(item.getId(),
					Triple.of(item.getAmount(), item.getAmount(), item.getAmount())))
				.collect(Collectors.toList());
		} else if (allUnPay) {
			return porOrderItem.stream().filter(item -> item.getAmount() != null)
				.map(item -> Pair.of(item.getId(),
					Triple.of(item.getAmount(), payableApplyAmount, BigDecimal.ZERO)))
				.collect(Collectors.toList());
		} else {
			//计算付款百分比
			BigDecimal payPercent = payAmount.divide(orderAmount, 2, RoundingMode.HALF_UP);
			BigDecimal payableApplyPercent = payableApplyAmount.divide(orderAmount, 2,
				RoundingMode.HALF_UP);
			return porOrderItem.stream()
				.map(item -> Pair.of(item.getId(),
					Triple.of(item.getAmount(), item.getAmount().multiply(payableApplyPercent),
						item.getAmount().multiply(payPercent))))
				.collect(Collectors.toList());
		}
	}

	private List<Pair<Long, Triple<BigDecimal, BigDecimal, BigDecimal>>> loadOrderItemAmountBySoaId(
		Long soaId) {
		PaySoaEntity soa = paySoaService.getById(soaId);
		if (soa == null) {
			return null;
		}
		List<PayableApplyEntity> applies = payableApplyService.list(
			Wrappers.<PayableApplyEntity>lambdaQuery()
				.eq(PayableApplyEntity::getPaySoaId, soaId)
				.eq(PayableApplyEntity::getType, PayableApplyEntity.TYPE_POR)
				.notIn(PayableApplyEntity::getStatus, PayableApplyEntity.STATUS_DRAFT,
					PayableApplyEntity.STATUS_CANCEL, PayableApplyEntity.STATUS_TERMINATION));
		BigDecimal payableApplyAmount = applies.stream().map(PayableApplyEntity::getAmount).filter(
			Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		BigDecimal payAmount = BigDecimal.ZERO;
		if (!applies.isEmpty()) {
			List<FinTransFlow> payables = finTransFlowService.list(
				Wrappers.<FinTransFlow>lambdaQuery()
					.in(FinTransFlow::getSource, applies.stream().map(PayableApplyEntity::getId)
						.collect(Collectors.toList()))
					.eq(FinTransFlow::getType, FinTransFlow.TYPE_PAYABLE));
			payAmount = payables.stream().map(FinTransFlow::getAmount)
				.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		}
		List<PorOrderItem> orderItems = porOrderItemService.list(
			Wrappers.<PorOrderItem>lambdaQuery().in(PorOrderItem::getSoaId, soaId)
				.eq(PorOrderItem::getSoa, 1));
		if (soa.getType().equals(PaySoaEntity.TYPE_POR)) {
			//如果订单是部分付款，则明细的付款金额取付款金额的比例
			BigDecimal orderAmount = orderItems.stream().map(PorOrderItem::getAmount)
				.filter(Objects::nonNull)
				.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
			//计算付款百分比
			BigDecimal payPercent = payAmount.divide(orderAmount, 2, RoundingMode.HALF_UP);
			BigDecimal payableApplyPercent = payableApplyAmount.divide(orderAmount, 2,
				RoundingMode.HALF_UP);
			return orderItems.stream().filter(orderItem -> orderItem.getAmount() != null)
				.map(item -> Pair.of(item.getId(),
					Triple.of(item.getAmount(), item.getAmount().multiply(payableApplyPercent),
						item.getAmount().multiply(payPercent))))
				.collect(Collectors.toList());
		} else if (soa.getType().equals(PaySoaEntity.TYPE_ALIPAY)) {
			return orderItems.stream()
				.map(item -> Pair.of(item.getId(),
					Triple.of(item.getAmount(), item.getAmount(), item.getAmount())))
				.collect(Collectors.toList());
		}
		return null;
	}

	private List<Pair<Long, Triple<BigDecimal, BigDecimal, BigDecimal>>> loadOrderItemAmountByContractId(
		Long contractId) {
		ContractEntity contract = contractService.getById(contractId);
		if (contract == null || contract.getOrderId() == null) {
			return null;
		}
		if (!contract.getType().contains(TYPE_POR_NORMAL) && !contract.getType()
			.contains(TYPE_POR_ORDER) || !Objects.equals(contract.getStatus(),
			WfEntity.STATUS_FINISH)) {
			return null;
		}
		List<PayableApplyEntity> applies = payableApplyService.list(
			Wrappers.<PayableApplyEntity>lambdaQuery()
				.eq(PayableApplyEntity::getContractId, contractId)
				.eq(PayableApplyEntity::getType, PayableApplyEntity.TYPE_CONTRACT)
				.notIn(PayableApplyEntity::getStatus, PayableApplyEntity.STATUS_DRAFT,
					PayableApplyEntity.STATUS_CANCEL, PayableApplyEntity.STATUS_TERMINATION));
		BigDecimal payableApplyAmount = applies.stream().map(PayableApplyEntity::getAmount)
			.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		BigDecimal payAmount = BigDecimal.ZERO;
		if (!applies.isEmpty()) {
			List<FinTransFlow> payables = finTransFlowService.list(
				Wrappers.<FinTransFlow>lambdaQuery()
					.in(FinTransFlow::getSource, applies.stream().map(PayableApplyEntity::getId)
						.collect(Collectors.toList()))
					.eq(FinTransFlow::getType, FinTransFlow.TYPE_PAYABLE));
			payAmount = payables.stream().map(FinTransFlow::getAmount)
				.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		}
		List<PorOrderItem> porOrderItem = porOrderItemService.list(
			Wrappers.<PorOrderItem>lambdaQuery()
				.eq(PorOrderItem::getOrderId, contract.getOrderId()));
		BigDecimal orderAmount = porOrderItem.stream().map(PorOrderItem::getAmount)
			.filter(Objects::nonNull)
			.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		BigDecimal payableApplyPercent;
		BigDecimal payPercent;
		payableApplyPercent = payableApplyAmount.divide(orderAmount, 2,
			RoundingMode.HALF_UP);
		payPercent = payAmount.divide(orderAmount, 2, RoundingMode.HALF_UP);
		boolean allPay = applies.stream()
			.allMatch(apply -> apply.getPayState() != null && apply.getPayState()
				.equals(PayableApplyEntity.PAY_STATE_FINISH));
		boolean allUnPay = applies.stream()
			.allMatch(apply -> apply.getPayState() == null || apply.getPayState()
				.equals(PayableApplyEntity.PAY_STATE_UN));
		//如果订单是全部付款,则明细的付款状态是全部付款
		if (allPay && orderAmount.compareTo(payableApplyAmount) <= 0) {
			return porOrderItem.stream()
				.map(item -> Pair.of(item.getId(),
					Triple.of(item.getAmount(), item.getAmount(), item.getAmount())))
				.collect(Collectors.toList());
		} else if (allUnPay) {
			return porOrderItem.stream()
				.map(item -> Pair.of(item.getId(),
					Triple.of(item.getAmount(), item.getAmount().multiply(payableApplyPercent),
						BigDecimal.ZERO)))
				.collect(Collectors.toList());
		} else {
			//如果订单是部分付款，则明细的付款金额取付款金额的比例
			return porOrderItem.stream()
				.map(item -> Pair.of(item.getId(),
					Triple.of(item.getAmount(), item.getAmount().multiply(payableApplyPercent),
						item.getAmount().multiply(payPercent))))
				.collect(Collectors.toList());
		}
	}

}
