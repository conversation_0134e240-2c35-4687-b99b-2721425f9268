<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.fin.mapper.FinFxMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="finFxResultMap" type="com.natergy.ni.fin.vo.FinFxVO">
        <result column="serial_no" property="serialNo"/>
        <result column="title" property="title"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="source_ledger_id" property="sourceLedgerId"/>
        <result column="source_currency" property="sourceCurrency"/>
        <result column="source_amount" property="sourceAmount"/>
        <result column="target_ledger_id" property="targetLedgerId"/>
        <result column="target_currency" property="targetCurrency"/>
        <result column="target_amount" property="targetAmount"/>
        <result column="exec_rate" property="execRate"/>
        <result column="book_rate" property="bookRate"/>
        <result column="bank_fee" property="bankFee"/>
        <result column="fx_gain_loss" property="fxGainLoss"/>
        <result column="process_ins_id" property="processInsId"/>
        <result column="process_def_id" property="processDefId"/>
        <result column="current_node" property="currentNode"/>
        <result column="op_date" property="opDate"/>
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectFinFxPage" resultMap="finFxResultMap">
        select * from ni_fin_fx where is_deleted = 0
    </select>


</mapper>
