<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.fin.mapper.VoucherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="voucherResultMap" type="com.natergy.ni.fin.vo.VoucherVO">
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="parent_id" property="parentId"/>
        <result column="serial_no" property="serialNo"/>
        <result column="type" property="type"/>
        <result column="date" property="date"/>
        <result column="summary" property="summary"/>
        <result column="accounting_id" property="accountingId"/>
        <result column="borrow_amount" property="borrowAmount"/>
        <result column="loan_amount" property="loanAmount"/>
        <result column="currency" property="currency"/>
        <result column="exc_rate" property="excRate"/>
        <result column="posting_date" property="postingDate"/>
        <result column="fiscal_year" property="fiscalYear"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="approver" property="approver"/>
        <result column="poster" property="poster"/>
        <result column="attach_num" property="attachNum"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectVoucherPage" resultMap="voucherResultMap">
        select * from ni_fin_voucher where is_deleted = 0
    </select>

    <select id="getLastByDate" resultType="java.lang.Integer">
      SELECT MAX(CAST(RIGHT(serial_no, 3) AS INT)) AS max_num
      FROM ni_fin_voucher
      WHERE is_deleted = 0
        and serial_no LIKE '%' + '-' + #{yyyyMMdd} + '-' + '%'
    </select>
</mapper>
