package com.natergy.ni.fin.event.paymentstatus;

import java.math.BigDecimal;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class PayableApplySubmitEvent extends ApplicationEvent {


	private static final long serialVersionUID = 8519818261027432501L;
	private final Long payableApplyId;
	private final BigDecimal amount;

	public PayableApplySubmitEvent(Long payableApplyId, BigDecimal amount) {
		super(payableApplyId);
		this.payableApplyId = payableApplyId;
		this.amount = amount;
	}

}
