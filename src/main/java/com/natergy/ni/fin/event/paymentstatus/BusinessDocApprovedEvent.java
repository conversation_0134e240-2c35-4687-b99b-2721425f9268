package com.natergy.ni.fin.event.paymentstatus;

import java.math.BigDecimal;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class BusinessDocApprovedEvent extends ApplicationEvent {


	private static final long serialVersionUID = 3861005821177664151L;
	private final BusinessType type;
	private final Long docId;
	private final BigDecimal amount;

	public BusinessDocApprovedEvent(BusinessType type, Long docId, BigDecimal amount) {
		super(type);
		this.type = type;
		this.docId = docId;
		this.amount = amount;
	}

	public enum BusinessType {
		POR_APPLY,
		POR_APPLY_ITEM,
		POR_ORDER,
		POR_ORDER_ITEM,
		BASE_CONTRACT
	}
}
