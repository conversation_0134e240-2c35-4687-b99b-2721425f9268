/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.fin.entity.VoucherDetailEntity;
import com.natergy.ni.fin.vo.VoucherDetailVO;
import com.natergy.ni.fin.wrapper.VoucherDetailWrapper;
import com.natergy.ni.fin.service.IVoucherDetailService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 财务管理-凭证分录表 控制器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("ni/fin/voucherDetail")
@Api(value = "财务管理-凭证分录表", tags = "财务管理-凭证分录表接口")
public class VoucherDetailController extends BladeController {

	private final IVoucherDetailService voucherDetailService;

	/**
	 * 财务管理-凭证分录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入voucherDetail")
	public R<VoucherDetailVO> detail(VoucherDetailEntity voucherDetail) {
		VoucherDetailEntity detail = voucherDetailService.getOne(Condition.getQueryWrapper(voucherDetail));
		return R.data(VoucherDetailWrapper.build().entityVO(detail));
	}
	/**
	 * 财务管理-凭证分录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入voucherDetail")
	public R<IPage<VoucherDetailVO>> list(VoucherDetailEntity voucherDetail, Query query) {
		IPage<VoucherDetailEntity> pages = voucherDetailService.page(Condition.getPage(query), Condition.getQueryWrapper(voucherDetail));
		return R.data(VoucherDetailWrapper.build().pageVO(pages));
	}

	/**
	 * 财务管理-凭证分录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入voucherDetail")
	public R<IPage<VoucherDetailVO>> page(VoucherDetailVO voucherDetail, Query query) {
		IPage<VoucherDetailVO> pages = voucherDetailService.selectVoucherDetailPage(Condition.getPage(query), voucherDetail);
		return R.data(pages);
	}

	/**
	 * 财务管理-凭证分录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入voucherDetail")
	public R save(@Valid @RequestBody VoucherDetailEntity voucherDetail) {
		return R.status(voucherDetailService.save(voucherDetail));
	}

	/**
	 * 财务管理-凭证分录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入voucherDetail")
	public R update(@Valid @RequestBody VoucherDetailEntity voucherDetail) {
		return R.status(voucherDetailService.updateById(voucherDetail));
	}

	/**
	 * 财务管理-凭证分录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入voucherDetail")
	public R submit(@Valid @RequestBody VoucherDetailEntity voucherDetail) {
		return R.status(voucherDetailService.saveOrUpdate(voucherDetail));
	}

	/**
	 * 财务管理-凭证分录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(voucherDetailService.deleteLogic(Func.toLongList(ids)));
	}


}
