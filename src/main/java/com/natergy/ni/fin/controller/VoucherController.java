/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.natergy.ni.fin.dto.VoucherDetailDTO;
import com.natergy.ni.fin.dto.VoucherRequestFormDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.fin.entity.VoucherEntity;
import com.natergy.ni.fin.vo.VoucherVO;
import com.natergy.ni.fin.wrapper.VoucherWrapper;
import com.natergy.ni.fin.service.IVoucherService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 财务管理-凭证 控制器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/fin/voucher")
@Api(value = "财务管理-凭证", tags = "财务管理-凭证接口")
public class VoucherController extends BladeController {

	private final IVoucherService voucherService;

	/**
	 * 财务管理-凭证 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入voucher")
	public R<VoucherVO> detail(@RequestParam Long id) {
		VoucherVO detail = voucherService.detailById(id);
		return R.data(detail);
	}

	/**
	 * 财务管理-凭证 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入voucher")
	public R<IPage<VoucherVO>> list(VoucherEntity voucher,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
		Query query) {
		QueryWrapper<VoucherEntity> wrapper = Condition.getQueryWrapper(voucher);
		if (startDate != null) {
			wrapper.lambda().ge(VoucherEntity::getDate, startDate);
		}
		if (endDate != null) {
			wrapper.lambda().le(VoucherEntity::getDate, endDate);
		}
		wrapper.lambda().orderByDesc(VoucherEntity::getId);
		IPage<VoucherEntity> pages = voucherService.page(Condition.getPage(query), wrapper);
		return R.data(VoucherWrapper.build().pageVO(pages));
	}

	/**
	 * 财务管理-凭证 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入voucher")
	public R<Void> save(@Valid @RequestBody VoucherRequestFormDTO voucher) {
		if (voucher.getStatus() == null) {
			voucher.setStatus(VoucherEntity.STATUS_DRAFT);
		}
		if (StringUtils.isNotBlank(voucher.getSourceSystem())) {
			voucher.setSourceSystem(VoucherEntity.SOURCE_SYSTEM_MANUAL);
		}
		Assert.isTrue(StringUtils.isNotBlank(voucher.getType()), "凭证字不能为空");
		voucher.setSerialNo(voucher.getType() + "-" + DateUtil.format(new Date(), "yyyyMMdd") + "-"
			+ voucherService.getSerialNo(DateUtil.format(new Date(), "yyyyMMdd")));
		voucher.setFiscalYear(voucher.getDate().getYear());
		voucher.setBorrowAmount(
			voucher.getDetails().stream().map(VoucherDetailDTO::getBorrowAmount).filter(
				Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
		voucher.setLoanAmount(
			voucher.getDetails().stream().map(VoucherDetailDTO::getLoanAmount).filter(
				Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
		return R.status(voucherService.save1(voucher));
	}

	/**
	 * 财务管理-凭证 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入voucher")
	public R<Void> update(@Valid @RequestBody VoucherRequestFormDTO voucher) {
		VoucherEntity voucher1 = voucherService.getById(voucher.getId());
		Assert.isTrue(Objects.equals(voucher1.getStatus(), VoucherEntity.STATUS_DRAFT),
			"凭证状态不是草稿");
		voucher.setFiscalYear(voucher.getDate().getYear());
		voucher.setBorrowAmount(
			voucher.getDetails().stream().map(VoucherDetailDTO::getBorrowAmount).filter(
				Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
		voucher.setLoanAmount(
			voucher.getDetails().stream().map(VoucherDetailDTO::getLoanAmount).filter(
				Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
		return R.status(voucherService.updateById1(voucher));
	}


	/**
	 * 财务管理-凭证 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R<Void> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> idList = Func.toLongList(ids);
		List<VoucherEntity> voucherList = voucherService.listByIds(idList);

		// 校验：只能删除草稿状态的凭证
		for (VoucherEntity voucher : voucherList) {
			Assert.isTrue(
				Objects.equals(voucher.getStatus(), VoucherEntity.STATUS_DRAFT),
				"只能删除状态为草稿的凭证"
			);
		}
		return R.status(voucherService.deleteLogic1(idList));
	}

	/**
	 * 批量提交审核
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "批量提交审核", notes = "传入ids")
	public R<Void> submit(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> idList = Func.toLongList(ids);
		List<VoucherEntity> voucherList = voucherService.listByIds(idList);
		for (VoucherEntity voucher : voucherList) {
			Assert.isTrue(
				Objects.equals(voucher.getStatus(), VoucherEntity.STATUS_DRAFT),
				"只能提交草稿的凭证"
			);
		}
		return R.status(voucherService.submit(idList));
	}

	/**
	 * 批量审核
	 */
	@PostMapping("/audit")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "批量审核", notes = "传入ids")
	public R<Void> audit(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> idList = Func.toLongList(ids);
		List<VoucherEntity> voucherList = voucherService.listByIds(idList);

		// 校验：只能删除草稿状态的凭证
		for (VoucherEntity voucher : voucherList) {
			Assert.isTrue(
				Objects.equals(voucher.getStatus(), VoucherEntity.STATUS_WAIT_AUDIT),
				"只能审核状态为待审核的凭证"
			);
		}
		return R.status(voucherService.audit(idList));
	}

	/**
	 * 批量过账
	 */
	@PostMapping("/posting")
	public R<Void> posting(
		@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> idList = Func.toLongList(ids);
		List<VoucherEntity> voucherList = voucherService.listByIds(idList);

		// 校验：只能删除草稿状态的凭证
		for (VoucherEntity voucher : voucherList) {
			Assert.isTrue(
				Objects.equals(voucher.getStatus(), VoucherEntity.STATUS_AUDITED),
				"只能过账状态为已审核的凭证"
			);
		}
		return R.status(voucherService.posting(idList));
	}

	@PostMapping("/overrule")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "批量驳回", notes = "传入ids")
	public R<Void> overrule(
		@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> idList = Func.toLongList(ids);
		List<VoucherEntity> voucherList = voucherService.listByIds(idList);
		// 校验：只能驳回待审核状态的凭证
		for (VoucherEntity voucher : voucherList) {
			Assert.isTrue(
				Objects.equals(voucher.getStatus(), VoucherEntity.STATUS_WAIT_AUDIT),
				"只能驳回待审核的凭证"
			);
		}
		return R.status(voucherService.overrule(idList));
	}

	@PostMapping("/auditBack")
	@ApiOperationSupport(order = 9)
	public R<Void> auditBack(
		@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> idList = Func.toLongList(ids);
		List<VoucherEntity> voucherList = voucherService.listByIds(idList);
		// 校验：只能驳回待审核状态的凭证
		for (VoucherEntity voucher : voucherList) {
			Assert.isTrue(
				Objects.equals(voucher.getStatus(), VoucherEntity.STATUS_AUDITED),
				"凭证状态错误");
		}
		return R.status(voucherService.auditBack(idList));
	}

	@PostMapping("/removal")
	@ApiOperationSupport(order = 10)
	public R<Void> removal(
		@ApiParam(value = "主键集合", required = true) @RequestParam Long id,
		@RequestParam String remark) {
		VoucherEntity voucher = voucherService.getById(id);
		Assert.isTrue(
			Objects.equals(voucher.getStatus(), VoucherEntity.STATUS_AUDITED),
			"凭证状态错误");
		return R.status(voucherService.removal(id, remark));
	}
}
