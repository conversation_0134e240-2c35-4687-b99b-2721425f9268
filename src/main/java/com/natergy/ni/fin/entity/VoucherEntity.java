/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.entity;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 财务管理-凭证 实体类
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@TableName("ni_fin_voucher")
@ApiModel(value = "Voucher对象", description = "财务管理-凭证")
@EqualsAndHashCode(callSuper = true)
public class VoucherEntity extends TenantEntity {

	private static final long serialVersionUID = 5944972679711538182L;
	/**
	 * 来源系统-付款单
	 */
	public static final String SOURCE_SYSTEM_PAYMENT = "1";
	/**
	 * 来源系统-报销单
	 */
	public static final String SOURCE_SYSTEM_EXPENSE = "2";
	/**
	 * 来源系统-直接出账
	 */
	public static final String SOURCE_SYSTEM_CASH_PAY = "3";
	/**
	 * 来源系统-账减转账
	 */
	public static final String SOURCE_SYSTEM_TRANSFER = "4";
	/**
	 * 来源系统-手工录入
	 */
	public static final String SOURCE_SYSTEM_MANUAL = "9";
	/**
	 * 状态-草稿
	 */
	public static final Integer STATUS_DRAFT = 1;
	/**
	 * 状态-待审核
	 */
	public static final Integer STATUS_WAIT_AUDIT = 2;
	/**
	 * 状态-已审核
	 */
	public static final Integer STATUS_AUDITED = 3;
	/**
	 * 已过帐
	 */
	public static final Integer STATUS_POSTED = 4;
	/**
	 * 状态-已冲销
	 */
	public static final Integer STATUS_REVERSED = 5;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 父主键
	 */
	@ApiModelProperty(value = "父主键")
	private Long parentId;
	/**
	 * 流水号用户可见的凭证流水号，如 记-202506-0001。按月自动生成。
	 */
	@ApiModelProperty(value = "流水号用户可见的凭证流水号，如 记-202506-0001。按月自动生成。")
	@TableField(condition = SqlCondition.LIKE)
	private String serialNo;
	/**
	 * 凭证类型如：“记”、“收”、“付”、“转”
	 */
	@ApiModelProperty(value = "凭证类型如：“记”、“收”、“付”、“转”")
	private String type;
	/**
	 * 业务发生的会计日期。
	 */
	@ApiModelProperty(value = "会计日期")
	private LocalDate date;
	/**
	 * 摘要
	 */
	@TableField(condition = SqlCondition.LIKE)
	@ApiModelProperty(value = "摘要")
	private String summary;
	/**
	 * 借方金额
	 */
	@ApiModelProperty(value = "借方金额")
	private BigDecimal borrowAmount;
	/**
	 * 贷方金额
	 */
	@ApiModelProperty(value = "贷方金额")
	private BigDecimal loanAmount;
	/**
	 * 币种
	 */
	@ApiModelProperty(value = "币种")
	private String currency;
	/**
	 * 汇率
	 */
	@ApiModelProperty(value = "汇率")
	private Float excRate;
	/**
	 * 过账日期,凭证正式计入总账的日期。
	 */
	@ApiModelProperty(value = "过账日期,凭证正式计入总账的日期。")
	private LocalDate postingDate;
	/**
	 * 会计年度，用于关帐和报表
	 */
	@ApiModelProperty(value = "会计年度，用于关帐和报表")
	private Integer fiscalYear;
	/**
	 * 来源系统
	 */
	@ApiModelProperty(value = "来源系统")
	private String sourceSystem;
	/**
	 * 审核人
	 */
	@ApiModelProperty(value = "审核人")
	private Long approver;
	/**
	 * 过账人
	 */
	@ApiModelProperty(value = "过账人")
	private Long poster;
	/**
	 * 线下单据附件数
	 */
	@ApiModelProperty(value = "线下单据附件数")
	private Integer attachNum;

}
