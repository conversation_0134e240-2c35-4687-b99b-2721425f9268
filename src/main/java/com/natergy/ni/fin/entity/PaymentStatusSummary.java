/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 财务管理-调账管理 实体类
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
@TableName("ni_fin_payment_status_summary")
@ApiModel(value = "付款状态汇总表", description = "财务管理-付款状态汇总表")
public class PaymentStatusSummary implements Serializable {


	private static final long serialVersionUID = 6397957682958195144L;
	/**
	 * 未付款
	 */
	public static final Integer PAYMENT_STATUS_UNPAID = 0;
	/**
	 * 部分付款
	 */
	public static final Integer PAYMENT_STATUS_PARTIAL_PAID = 1;
	/**
	 * 已付讫
	 */
	public static final Integer PAYMENT_STATUS_PAID = 2;
	/**
	 * 已申请
	 */
	public static final Integer PAYMENT_STATUS_APPLIED = 3;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	@ApiModelProperty("单据类型")
	private String type;
	@ApiModelProperty("单据id")
	private Long docId;
	@ApiModelProperty("应付金额")
	private BigDecimal amount;
	@ApiModelProperty("已付金额")
	private BigDecimal paidAmount;
	@ApiModelProperty("付款状态")
	private Integer paymentStatus;
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty("最后更新时间")
	private LocalDate lastUpdated;
}
