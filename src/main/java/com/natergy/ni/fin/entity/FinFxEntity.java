/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.plugin.workflow.core.entity.WfEntity;

/**
 * 资金结汇单 实体类
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@TableName("ni_fin_fx")
@ApiModel(value = "FinFx对象", description = "资金结汇单")
@EqualsAndHashCode(callSuper = true)
public class FinFxEntity extends WfEntity {

	private static final long serialVersionUID = -4046236643826773380L;
	/**
	 * 已作废
	 */
	public static final Integer POST_STATUS_TERMINATION = 9;
	/**
	 * 待执行
	 */
	public static final Integer POST_STATUS_DRAFT = 0;
	/**
	 * 已执行
	 */
	public static final Integer POST_STATUS_POST = 1;
	/**
	 * 已冲销
	 */
	public static final Integer POST_STATUS_REVERSE = 2;
	/**
	 * 采购编号
	 */
	@ApiModelProperty(value = "采购编号")
	private String serialNo;
	/**
	 * 采购主题
	 */
	@ApiModelProperty(value = "采购主题")
	private String title;
	/**
	 * 转出账户
	 */
	@ApiModelProperty(value = "转出账户")
	private Long sourceLedgerId;
	/**
	 * 转出币种
	 */
	@ApiModelProperty(value = "转出币种")
	private String sourceCurrency;
	/**
	 * 转出金额
	 */
	@ApiModelProperty(value = "转出金额")
	private BigDecimal sourceAmount;
	/**
	 * 转入账户
	 */
	@ApiModelProperty(value = "转入账户")
	private Long targetLedgerId;
	/**
	 * 转入币种
	 */
	@ApiModelProperty(value = "转入币种")
	private String targetCurrency;
	/**
	 * 转入金额
	 */
	@ApiModelProperty(value = "转入金额")
	private BigDecimal targetAmount;
	/**
	 * 执行汇率
	 */
	@ApiModelProperty(value = "执行汇率")
	private BigDecimal execRate;
	/**
	 * 账面汇率
	 */
	@ApiModelProperty(value = "账面汇率")
	private BigDecimal bookRate;
	/**
	 * 手续费
	 */
	@ApiModelProperty(value = "手续费")
	private BigDecimal bankFee;
	/**
	 * 汇兑损益
	 */
	@ApiModelProperty(value = "汇兑损益")
	private BigDecimal fxGainLoss;
	/**
	 * 操作日期
	 */
	@ApiModelProperty(value = "操作日期")
	private LocalDate opDate;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String remark;

	private String brand;

	/**
	 * 执行状态-待执行，已执行，已冲销
	 */
	private Integer postStatus;

}
