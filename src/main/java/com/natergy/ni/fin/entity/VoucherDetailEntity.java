/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.fin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 财务管理-凭证分录表 实体类
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@TableName("ni_fin_voucher_detail")
@ApiModel(value = "VoucherDetail对象", description = "财务管理-凭证分录表")
@EqualsAndHashCode(callSuper = true)
public class VoucherDetailEntity extends TenantEntity {

	private static final long serialVersionUID = 3008489991688248381L;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 行号
	 */
	@ApiModelProperty(value = "行号")
	private Integer row;
	/**
	 * 会计科目id
	 */
	@ApiModelProperty(value = "会计科目id")
	private Long accountingId;
	/**
	 * 摘要
	 */
	@ApiModelProperty(value = "摘要")
	private String summary;
	/**
	 * 借方金额
	 */
	@ApiModelProperty(value = "借方金额")
	private BigDecimal borrowAmount;
	/**
	 * 贷方金额
	 */
	@ApiModelProperty(value = "贷方金额")
	private BigDecimal loanAmount;
	/**
	 * 源单据类型
	 */
	@ApiModelProperty(value = "源单据类型")
	private String sourceType;
	/**
	 * 源单据id
	 */
	@ApiModelProperty(value = "源单据id")
	private Long sourceId;
	/**
	 * 源单据明细id
	 */
	@ApiModelProperty(value = "源单据明细id")
	private Long sourceItemId;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private Long voucherId;

}
