/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.natergy.ni.tracing.dto.QualityInspectionModifiedDto;
import com.natergy.ni.tracing.entity.QualityInspectionEntity;
import com.natergy.ni.tracing.entity.QualityInspectionModifiedEntity;
import com.natergy.ni.tracing.service.IQualityInspectionModifiedService;
import com.natergy.ni.tracing.vo.QualityInspectionModifiedVO;
import com.natergy.ni.tracing.vo.QualityInspectionVO;
import com.natergy.ni.tracing.wrapper.QualityInspectionModifiedWrapper;
import com.natergy.ni.tracing.wrapper.QualityInspectionWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.log.LogOptRecord;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 溯源-对外展示质检报告 控制器
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/tracing/quality-inspection-modified")
@Api(value = "溯源-对外展示质检报告", tags = "溯源-质检报告接口")
public class QualityInspectionModifiedController extends BladeController {
	public static final String MODULE = "ni_tracing_quality_inspection_modified";

	private final IQualityInspectionModifiedService qualityInspectionModifiedService;

	/**
	 * 溯源-对外展示质检报告 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入qualityInspection")
	public R<QualityInspectionModifiedVO> detail(QualityInspectionModifiedEntity qualityInspection) {
		QualityInspectionModifiedEntity detail = qualityInspectionModifiedService.getOne(Condition.getQueryWrapper(qualityInspection));
		if (detail == null){
			return R.fail("未查询到对外质检报告！");
		}
		return R.data(QualityInspectionModifiedWrapper.build().entityVO(detail));
	}

	/**
	 * 溯源-对外展示质检报告 新增或修改
	 */
	@LogOptRecord(module = MODULE, businessId = "{#qualityInspection.originInspectionId}", context = "数据修改")
	//此处记录修改日志做特殊处理，修改对外质检报告时存质检报告的业务id，区分对外质检报告修改记录和对内质检报告修改记录时使用模块名称区分
	@PostMapping("/submit")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "新增或修改", notes = "传入qualityInspection")
	public R submit(@Valid @RequestBody QualityInspectionModifiedEntity qualityInspection) {
		return R.status(qualityInspectionModifiedService.saveOrUpdate(qualityInspection));
	}

	/**
	 * 溯源-批次-对外质检报告 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入qualityInspection")
	public R<IPage<QualityInspectionModifiedVO>> list(QualityInspectionModifiedDto qualityInspection, Query query) {
		qualityInspection.setBatchCode(qualityInspection.getBatchCode() != null ? qualityInspection.getBatchCode().trim() : null);
		qualityInspection.setCode(qualityInspection.getCode() != null ? qualityInspection.getCode().trim() : null);
		String[] dates = qualityInspection.getBatchEndTime();
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if (dates != null && dates.length > 1) {
			Date start = null;
			Date end = null;
			try {
				start = simpleDateFormat.parse(dates[0]);
				end = simpleDateFormat.parse(dates[1]);
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			qualityInspection.setStartDate(start);
			qualityInspection.setEndDate(end);
		}
		IPage<QualityInspectionModifiedVO> pages = new Page<>();
		if ("outside".equals(qualityInspection.getReportType())){
			pages = qualityInspectionModifiedService.selectQualityInspectionEntityOut(qualityInspection,Condition.getPage(query));
		} else if ("inside".equals(qualityInspection.getReportType())) {
			pages = qualityInspectionModifiedService.selectQualityInspectionEntityIn(qualityInspection,Condition.getPage(query));
		}
		return R.data(pages);
	}
}
