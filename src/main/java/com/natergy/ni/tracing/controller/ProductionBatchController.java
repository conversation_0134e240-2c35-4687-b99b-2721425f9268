/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.natergy.ni.product.entity.ProductPackaging;
import com.natergy.ni.product.entity.ProductSpec;
import com.natergy.ni.product.service.IProductPackagingService;
import com.natergy.ni.product.service.ProductSpecService;
import com.natergy.ni.tracing.dto.ProductionBatchDTO;
import com.natergy.ni.tracing.dto.ProductionEncodeDTO;
import com.natergy.ni.tracing.dto.QualityInspectionSearchParameter;
import com.natergy.ni.tracing.entity.ProductionBatchEntity;
import com.natergy.ni.tracing.service.IBoxInfoService;
import com.natergy.ni.tracing.service.IProductionBatchService;
import com.natergy.ni.tracing.service.ITracingCodeScanLogService;
import com.natergy.ni.tracing.service.ITracingCodeService;
import com.natergy.ni.tracing.vo.ProductionBatchSummaryVO;
import com.natergy.ni.tracing.vo.ProductionBatchVO;
import com.natergy.ni.tracing.vo.ScanResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.log.LogOptRecord;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.DictBiz;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 溯源-生产批次 控制器
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/tracing/production-batch")
@Api(value = "溯源-生产批次", tags = "溯源-生产批次接口")
public class ProductionBatchController extends BladeController {

	private final IProductionBatchService productionBatchService;
	private final IProductPackagingService productPackagingService;
	private final ProductSpecService productSpecService;
	private final IBoxInfoService boxInfoService;
	private final ITracingCodeService tracingCodeService;

	private final ITracingCodeScanLogService tracingCodeScanLogService;

	public static final String MODULE = "ni_tracing_production_batch";
	public static final String MODULE_QR = "ni_tracing_qrcode";

	/**
	 * 溯源-生产批次 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入productionBatch")
	public R<ProductionBatchVO> detail(ProductionBatchEntity productionBatch) {
		ProductionBatchVO productionBatchVO = productionBatchService.getProductionBatchById(
			productionBatch.getId());
		return R.data(productionBatchVO);
	}

	@GetMapping("/detailByBatchCode")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入productionBatch")
	public R<ProductionBatchVO> detail(@RequestParam String batchCode) {
		ProductionBatchVO productionBatchVO = productionBatchService.getProductionBatchByBatchCode(
			batchCode);
		return R.data(productionBatchVO);
	}

	/**
	 * 溯源-生产批次 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入productionBatch")
	public R<IPage<ProductionBatchVO>> list(QualityInspectionSearchParameter parameter,
		Query query) {
		parameter.setBatchCode(
			Func.isNotEmpty(parameter.getBatchCode()) ? parameter.getBatchCode().trim() : null);
		if (!(Func.isNotEmpty(parameter.getStartBatchCode()) && Func.isNotEmpty(
			parameter.getEndBatchCode()))) {
			if (Func.isNotEmpty(parameter.getStartBatchCode())) {
				parameter.setBatchCode(parameter.getStartBatchCode().trim());
			}
			if (Func.isNotEmpty(parameter.getEndBatchCode())) {
				parameter.setBatchCode(parameter.getEndBatchCode().trim());
			}
		} else {
			parameter.setStartBatchCode(parameter.getStartBatchCode().trim());
			parameter.setEndBatchCode(parameter.getEndBatchCode().trim());
		}
		String[] dates = parameter.getBatchTime();
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if (dates != null && dates.length > 1) {
			Date start = null;
			Date end = null;
			try {
				start = simpleDateFormat.parse(dates[0]);
				end = simpleDateFormat.parse(dates[1]);
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			parameter.setStartDate(start);
			parameter.setEndDate(end);
		}
		IPage<ProductionBatchVO> pages = productionBatchService.selectParameterPage(parameter,
			Condition.getPage(query));
		return R.data(pages);
	}

	/**
	 * 溯源-生产批次 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入productionBatch")
	public R<IPage<ProductionBatchVO>> page(ProductionBatchVO productionBatch, Query query) {
		IPage<ProductionBatchVO> pages = productionBatchService.selectProductionBatchPage(
			Condition.getPage(query), productionBatch);
		return R.data(pages);
	}

	/**
	 * 溯源-生产批次 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入productionBatch")
	public R save(@Valid @RequestBody ProductionBatchEntity productionBatch) {
		return R.status(productionBatchService.save(productionBatch));
	}

	/**
	 * 溯源-生产批次 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入productionBatch")
	public R update(@Valid @RequestBody ProductionBatchEntity productionBatch) {
		return R.status(productionBatchService.updateById(productionBatch));
	}

	/**
	 * 溯源-生产批次 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入productionBatch")
	public R submit(@Valid @RequestBody ProductionBatchEntity productionBatch) {
		if (productionBatch.getId() == null) {
			productionBatch.setIsInout(true);
			productionBatch.setPalletNumber(0);
		}
		return R.status(productionBatchService.saveOrUpdate(productionBatch));
	}


	/**
	 * 溯源-生产批次 批量新增“铁桶”批次
	 */
	@PostMapping("/addBatch")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入productionBatch")
	public R addBatch(@Valid @RequestBody ProductionEncodeDTO productionEncodeDTO) {
		return R.status(productionBatchService.addBatch(productionEncodeDTO));
	}

	/**
	 * 溯源-生产批次 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@LogOptRecord(module = MODULE, businessId = "{#ids}", context = "数据删除")
	@Transactional(rollbackFor = Exception.class)
	public R remove(@RequestBody List<Long> ids) {
		// 筛选出isInout=true的ID
		List<Long> validIds = productionBatchService.filterValidIds(ids);
		if (validIds.isEmpty()) {
			return R.fail("没有符合删除条件的记录");
		}
		//删除二维码扫描记录
		boolean codeLog = tracingCodeScanLogService.deleteLog(validIds);
		//删除二维码
		boolean qrcode = tracingCodeService.deleteCode(validIds);
		//删除批次箱体数据
		boolean boxInfo = boxInfoService.deleteBox(validIds);
		//删除生产批次
		boolean batch = productionBatchService.deleteBatch(validIds);
		return R.status(codeLog && batch && boxInfo && qrcode);
	}

	/**
	 * 根据批次查询质检报告信息
	 *
	 * @param productionBatchDTO
	 * @return
	 */
	@GetMapping("/reportDetail")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "质检报告详情", notes = "传入productionBatch")
	public R<List<ScanResultVO>> reportDetail(ProductionBatchDTO productionBatchDTO) {
		List<ScanResultVO> list = productionBatchService.getQualityInspectionReport(productionBatchDTO.getPrintIds(),productionBatchDTO.getQuantity());
		return R.data(list);
	}

	@GetMapping("/getBatch")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "质检报告绑定的批次集合", notes = "传入productionBatch")
	public R<List<ProductionBatchEntity>> getBatch(ProductionBatchEntity productionBatch) {
		List<ProductionBatchEntity> list = productionBatchService.list(
			Condition.getQueryWrapper(productionBatch));
		return R.data(list);
	}

	@PostMapping("/updateEncode")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "编码调整", notes = "传入productionBatch")
	@LogOptRecord(module = MODULE, businessId = "{#productionBatch.batchId}", context = "数据修改")
	public R updateEncode(@RequestBody ProductionEncodeDTO productionBatch) {
		ProductionBatchEntity productionBatchEntity = productionBatchService.getById(
			productionBatch.getBatchId());
		BeanUtil.copy(productionBatch, productionBatchEntity);
		//根据外包装id获取外包装容量
		ProductPackaging productPackaging = productPackagingService.getById(
			productionBatch.getOuterPackagingId());
		productionBatchEntity.setOuterPackagingCapacity(productPackaging.getCapacity());
		//根据规格code查询规格id
		LambdaQueryWrapper<ProductSpec> specWrapper = Wrappers.lambdaQuery(ProductSpec.class);
		specWrapper.eq(ProductSpec::getCode, productionBatch.getSpecificationCode());
		ProductSpec productSpec = productSpecService.getOne(specWrapper, false);
		productionBatchEntity.setSpecificationId(productSpec.getId());
		//根据质量code获取质量id、名称
		List<DictBiz> dictBizList = DictBizCache.getList("ni_product_sku_quality_level");
		for (DictBiz dictBiz : dictBizList) {
			if (dictBiz.getDictKey().equals(productionBatch.getQualityCode())) {
				productionBatchEntity.setQualityId(dictBiz.getId());
				productionBatchEntity.setQualityName(dictBiz.getDictValue());
				break;
			}
		}
		return R.data(productionBatchService.updateById(productionBatchEntity));
	}

	@GetMapping("/generateQrcode")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "生成二维码", notes = "传入ids")
	@LogOptRecord(module = MODULE_QR, businessId = "{ids}", context = "新增数据")
	public void generateQrcode(HttpServletResponse response, String ids) {
		String[] idSplit = ids.split(",");
		productionBatchService.generateQrcode(response, idSplit);
	}

	@GetMapping("/summaries")
	public R<List<ProductionBatchSummaryVO>> summaries(
		@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
		@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
		LocalDateTime startDateTime = startDate.atTime(LocalTime.of(6, 50, 0));
		LocalDateTime endDateTime = LocalDateTime.of(endDate.plusDays(1), LocalTime.of(6, 50, 0));
		List<ProductionBatchSummaryVO> summaryVOS = productionBatchService.summaries(startDateTime,
			endDateTime);
		return R.data(summaryVOS);
	}

	@GetMapping("/v1/summaries")
	public R<List<ProductionBatchSummaryVO>> summaries1(
		@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
		@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
		List<ProductionBatchSummaryVO> summaryVOS = productionBatchService.summaries(startDate,
			endDate);
		return R.data(summaryVOS);
	}
}
