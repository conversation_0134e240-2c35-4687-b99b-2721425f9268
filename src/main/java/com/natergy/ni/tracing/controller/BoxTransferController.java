package com.natergy.ni.tracing.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.natergy.ni.tracing.dto.BoxRepackParameter;
import com.natergy.ni.tracing.dto.BoxTransferParameter;
import com.natergy.ni.tracing.service.IBoxTransferService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/ni/tracing/box-transfer")
@Api(value = "溯源-倒箱/倒垛接口", tags = "溯源-倒箱/倒垛接口")
public class BoxTransferController extends BladeController {

	private final IBoxTransferService boxTransferService;

	/**
	 * 溯源-倒垛
	 */
	@PostMapping("/transfer")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "倒垛", notes = "传入BoxTransferParameter")
	public R transfer(@Valid @RequestBody BoxTransferParameter parameter) {

		boxTransferService.transfer(parameter);

		return R.success(ResultCode.SUCCESS);
	}

	/**
	 * 溯源-倒箱
	 */
	@PostMapping("/repack")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "倒箱", notes = "传入List<BoxRepackParameter>")
	public R repack(@Valid @RequestBody List<BoxRepackParameter> parameters) {

		boxTransferService.repack(parameters);

		return R.success(ResultCode.SUCCESS);
	}

}
