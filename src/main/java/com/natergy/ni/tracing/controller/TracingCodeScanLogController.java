/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.tracing.entity.TracingCodeScanLogEntity;
import com.natergy.ni.tracing.vo.TracingCodeScanLogVO;
import com.natergy.ni.tracing.wrapper.TracingCodeScanLogWrapper;
import com.natergy.ni.tracing.service.ITracingCodeScanLogService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 溯源-二维码扫描记录 控制器
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/tracing/qrcode-scan-log")
@Api(value = "溯源-二维码扫描记录", tags = "溯源-二维码扫描记录接口")
public class TracingCodeScanLogController extends BladeController {

	private final ITracingCodeScanLogService qrCodeScanLogService;

	/**
	 * 溯源-二维码扫描记录 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入qrCodeScanLog")
	public R<TracingCodeScanLogVO> detail(TracingCodeScanLogEntity qrCodeScanLog) {
		TracingCodeScanLogEntity detail = qrCodeScanLogService.getOne(Condition.getQueryWrapper(qrCodeScanLog));
		return R.data(TracingCodeScanLogWrapper.build().entityVO(detail));
	}

	/**
	 * 溯源-二维码扫描记录 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入qrCodeScanLog")
	public R<IPage<TracingCodeScanLogVO>> list(TracingCodeScanLogEntity qrCodeScanLog, Query query) {
		LambdaQueryWrapper<TracingCodeScanLogEntity> queryWrapper = Condition.getQueryWrapper(qrCodeScanLog).lambda();
		queryWrapper.orderByDesc(TracingCodeScanLogEntity::getId);
		IPage<TracingCodeScanLogEntity> pages = qrCodeScanLogService.page(Condition.getPage(query), queryWrapper);
		return R.data(TracingCodeScanLogWrapper.build().pageVO(pages));
	}

	/**
	 * 溯源-二维码扫描记录 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入qrCodeScanLog")
	public R<IPage<TracingCodeScanLogVO>> page(TracingCodeScanLogVO qrCodeScanLog, Query query) {
		IPage<TracingCodeScanLogVO> pages = qrCodeScanLogService.selectQrCodeScanLogPage(Condition.getPage(query), qrCodeScanLog);
		return R.data(pages);
	}

	/**
	 * 溯源-二维码扫描记录 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入qrCodeScanLog")
	public R save(@Valid @RequestBody TracingCodeScanLogEntity qrCodeScanLog) {
		return R.status(qrCodeScanLogService.save(qrCodeScanLog));
	}

	/**
	 * 溯源-二维码扫描记录 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入qrCodeScanLog")
	public R update(@Valid @RequestBody TracingCodeScanLogEntity qrCodeScanLog) {
		return R.status(qrCodeScanLogService.updateById(qrCodeScanLog));
	}

	/**
	 * 溯源-二维码扫描记录 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入qrCodeScanLog")
	public R submit(@Valid @RequestBody TracingCodeScanLogEntity qrCodeScanLog) {
		return R.status(qrCodeScanLogService.saveOrUpdate(qrCodeScanLog));
	}

	/**
	 * 溯源-二维码扫描记录 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(qrCodeScanLogService.deleteLogic(Func.toLongList(ids)));
	}


}
