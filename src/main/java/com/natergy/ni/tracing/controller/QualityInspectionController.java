/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.tracing.dto.AssociateBatchParameter;
import com.natergy.ni.tracing.entity.ProductionBatchEntity;
import com.natergy.ni.tracing.service.IProductionBatchService;
import com.natergy.ni.tracing.service.IQualityInspectionModifiedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.common.log.LogOptRecord;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.tracing.entity.QualityInspectionEntity;
import com.natergy.ni.tracing.vo.QualityInspectionVO;
import com.natergy.ni.tracing.wrapper.QualityInspectionWrapper;
import com.natergy.ni.tracing.service.IQualityInspectionService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;

/**
 * 溯源-质检报告 控制器
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/tracing/quality-inspection")
@Api(value = "溯源-质检报告", tags = "溯源-质检报告接口")
public class QualityInspectionController extends BladeController {

	public static final String MODULE = "ni_tracing_quality_inspection";

	private final IQualityInspectionService qualityInspectionService;

	private final IProductionBatchService productionBatchService;

	/**
	 * 溯源-质检报告 详情
	 */
	@GetMapping("/detailByBatchCode")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入qualityInspection")
	public R<QualityInspectionVO> detail(String batchCode) {
		ProductionBatchEntity batchEntity = productionBatchService.getOne(
			Wrappers.<ProductionBatchEntity>lambdaQuery()
				.eq(ProductionBatchEntity::getBatchCode, batchCode));
		if (batchEntity == null) {
			//可能存在有历史批次的情况
			return R.data(new QualityInspectionVO());
		}
		QualityInspectionEntity detail = qualityInspectionService.getById(
			batchEntity.getInspectionId());
		String innerPackage = detail.getInnerPackage();
		if (innerPackage != null) {
			String[] innerPackages = innerPackage.split("&");
			detail.setInnerPackages(innerPackages);
		}
		return R.data(QualityInspectionWrapper.build().entityVO(detail));
	}

	/**
	 * 溯源-质检报告 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入qualityInspection")
	public R<QualityInspectionVO> detail(QualityInspectionEntity qualityInspection) {
		QualityInspectionEntity detail = qualityInspectionService.getOne(
			Condition.getQueryWrapper(qualityInspection));
		String innerPackage = detail.getInnerPackage();
		if (innerPackage != null) {
			String[] innerPackages = innerPackage.split("&");
			detail.setInnerPackages(innerPackages);
		}
		return R.data(QualityInspectionWrapper.build().entityVO(detail));
	}

	/**
	 * 溯源-质检报告 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入qualityInspection")
	public R<IPage<QualityInspectionVO>> list(QualityInspectionEntity qualityInspection,
		Query query) {
		QueryWrapper<QualityInspectionEntity> queryWrapper = Condition.getQueryWrapper(
			qualityInspection);
		queryWrapper.orderByDesc("code");
		IPage<QualityInspectionEntity> pages = qualityInspectionService.page(
			Condition.getPage(query), queryWrapper);
		List<QualityInspectionEntity> records = pages.getRecords();
		records.stream().forEach(item -> {
			if (item.getInnerPackage() != null) {
				String[] innerPackages = item.getInnerPackage().split("&");
				item.setInnerPackages(innerPackages);
			}
		});
		pages.setRecords(records);
		return R.data(QualityInspectionWrapper.build().pageVO(pages));
	}

	/**
	 * 溯源-质检报告 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入qualityInspection")
	public R<IPage<QualityInspectionVO>> page(QualityInspectionVO qualityInspection, Query query) {
		IPage<QualityInspectionVO> pages = qualityInspectionService.selectQualityInspectionPage(
			Condition.getPage(query), qualityInspection);
		return R.data(pages);
	}

	/**
	 * 溯源-质检报告 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入qualityInspection")
	public R save(@Valid @RequestBody QualityInspectionEntity qualityInspection) {
		//生成质检报告编码
		String code = qualityInspectionService.getNewCode(qualityInspection.getArea());
		qualityInspection.setCode(code);
		return R.status(qualityInspectionService.save(qualityInspection));
	}

	/**
	 * 溯源-质检报告 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入qualityInspection")
	public R update(@Valid @RequestBody QualityInspectionEntity qualityInspection) {
		return R.status(qualityInspectionService.updateById(qualityInspection));
	}

	/**
	 * 溯源-质检报告 新增或修改
	 */
	@LogOptRecord(module = MODULE, businessId = "{#qualityInspection.id}", context = "数据修改")
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入qualityInspection")
	@Transactional
	public R submit(@Valid @RequestBody QualityInspectionEntity qualityInspection) {
		if (qualityInspection.getId() == null) {
			//生成质检报告编码
			String code = qualityInspectionService.getNewCode(qualityInspection.getArea());
			qualityInspection.setCode(code);
		}
		String[] innerPackages = qualityInspection.getInnerPackages();
		if (innerPackages != null) {
			StringBuilder stringBuilder = new StringBuilder();
			for (int i = 0; i < innerPackages.length; i++) {
				stringBuilder.append(innerPackages[i]);
				if (i != innerPackages.length - 1) {
					stringBuilder.append("&");
				}
			}
			qualityInspection.setInnerPackage(stringBuilder.toString());
		}
		return R.status(qualityInspectionService.saveOrUpdate(qualityInspection));
	}

	/**
	 * 溯源-质检报告 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@Transactional(rollbackFor = Exception.class)
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		boolean deleteStatus = qualityInspectionService.deleteLogic(Func.toLongList(ids));
		boolean updateStatus = productionBatchService.deleteQuality(ids);
		return R.status(deleteStatus && updateStatus);
	}

	/**
	 * 溯源-质检报告 关联批次
	 */
	@PostMapping("/associate")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "关联批次", notes = "传入qualityInspection")
	public R associate(@Valid @RequestBody AssociateBatchParameter parameter) {
		qualityInspectionService.associateBatch(parameter);
		return R.success(ResultCode.SUCCESS);
	}

	/**
	 * 质检报告发布
	 *
	 * @param qualityInspection
	 * @return
	 */
	@LogOptRecord(module = MODULE, businessId = "{#qualityInspection.id}", context = "数据发布")
	@PostMapping("/publish")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "发布", notes = "传入qualityInspection")
	public R publish(@Valid @RequestBody QualityInspectionEntity qualityInspection) {
		if ("CN".equals(qualityInspection.getArea())) {
			if (Func.isEmpty(qualityInspection.getBulkDensity())
				|| Func.isEmpty(qualityInspection.getAfterBurningLiCl())
				|| Func.isEmpty(qualityInspection.getParticleSizeQualifiedRate())
				|| Func.isEmpty(qualityInspection.getWaterContent())
				|| Func.isEmpty(qualityInspection.getGasDesorption())
				|| Func.isEmpty(qualityInspection.getTemperatureRise20())
				|| Func.isEmpty(qualityInspection.getFallingPowderBeforeGrinding())
			) {
				return R.fail(
					"堆积密度、烧后LiCl、粒度合格率、含水、气体解吸量(ml/g)、温升20g、磨前落粉存在空值，不允许发布！");
			}
		} else if ("OS".equals(qualityInspection.getArea())) {
			if (Func.isEmpty(qualityInspection.getWaterContent())
				|| Func.isEmpty(qualityInspection.getTemperatureRise10())
				|| Func.isEmpty(qualityInspection.getTemperatureRise30())
				|| Func.isEmpty(qualityInspection.getTemperatureRise50())
				|| Func.isEmpty(qualityInspection.getAdsorptionEN())
			) {
				return R.fail("含水550度、温升10g、温升30g、温升50g、欧标吸附存在空值，不允许发布！");
			}
		} else {
			return R.fail("质检报告错误");
		}

		//如果质检报告未关联批次，不允许发布
		LambdaQueryWrapper<ProductionBatchEntity> wrapper = Wrappers.lambdaQuery(
			ProductionBatchEntity.class);
		wrapper.eq(ProductionBatchEntity::getInspectionId, qualityInspection.getId());
		List<ProductionBatchEntity> list = productionBatchService.list(wrapper);
		if (Func.isEmpty(list)) {
			return R.fail("当前质检报告未关联生产批次，不允许发布！");
		}
		return R.status(qualityInspectionService.updatePublishStatus(qualityInspection));
	}


}
