/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.controller;

import com.natergy.ni.tracing.entity.ProductionBatchEntity;
import com.natergy.ni.tracing.service.IProductionBatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.tracing.entity.TracingCodeEntity;
import com.natergy.ni.tracing.vo.TracingCodeVO;
import com.natergy.ni.tracing.wrapper.TracingCodeWrapper;
import com.natergy.ni.tracing.service.ITracingCodeService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 溯源-二维码信息 控制器
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/tracing/qrcode")
@Api(value = "溯源-二维码信息", tags = "溯源-二维码信息接口")
public class TracingCodeController extends BladeController {

	private final ITracingCodeService qrCodeService;
	private final IProductionBatchService productionBatchService;

	/**
	 * 溯源-二维码信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入qrCode")
	public R<TracingCodeVO> detail(TracingCodeEntity qrCode) {
		TracingCodeEntity detail = qrCodeService.getOne(Condition.getQueryWrapper(qrCode));
		TracingCodeVO vo = TracingCodeWrapper.build().entityVO(detail);
		if (detail != null) {
			ProductionBatchEntity batch = productionBatchService.getById(detail.getBatchId());
			if (batch != null) {
				vo.setBatchCode(batch.getBatchCode());
			}
		}
		return R.data(vo);
	}

	/**
	 * 溯源-二维码信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入qrCode")
	public R<IPage<TracingCodeVO>> list(TracingCodeEntity qrCode, Query query) {
		IPage<TracingCodeEntity> pages = qrCodeService.page(Condition.getPage(query),
			Condition.getQueryWrapper(qrCode));
		return R.data(TracingCodeWrapper.build().pageVO(pages));
	}

	/**
	 * 溯源-二维码信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入qrCode")
	public R<IPage<TracingCodeVO>> page(TracingCodeVO qrCode, Query query) {
		IPage<TracingCodeVO> pages = qrCodeService.selectQrCodePage(Condition.getPage(query),
			qrCode);
		return R.data(pages);
	}

	/**
	 * 溯源-二维码信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入qrCode")
	public R save(@Valid @RequestBody TracingCodeEntity qrCode) {
		return R.status(qrCodeService.save(qrCode));
	}

	/**
	 * 溯源-二维码信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入qrCode")
	public R update(@Valid @RequestBody TracingCodeEntity qrCode) {
		return R.status(qrCodeService.updateById(qrCode));
	}

	/**
	 * 溯源-二维码信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入qrCode")
	public R submit(@Valid @RequestBody TracingCodeEntity qrCode) {
		return R.status(qrCodeService.saveOrUpdate(qrCode));
	}

	/**
	 * 溯源-二维码信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(qrCodeService.deleteLogic(Func.toLongList(ids)));
	}


}
