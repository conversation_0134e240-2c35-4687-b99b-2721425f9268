/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.tracing.entity.BoxTransferLogEntity;
import com.natergy.ni.tracing.vo.BoxTransferLogVO;
import com.natergy.ni.tracing.wrapper.BoxTransferLogWrapper;
import com.natergy.ni.tracing.service.IBoxTransferLogService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 溯源-倒箱记录 控制器
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/tracing/box-transfer-log")
@Api(value = "溯源-倒箱记录", tags = "溯源-倒箱记录接口")
public class BoxTransferLogController extends BladeController {

	private final IBoxTransferLogService boxTransferLogService;

	/**
	 * 溯源-倒箱记录 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入boxTransferLog")
	public R<BoxTransferLogVO> detail(BoxTransferLogEntity boxTransferLog) {
		BoxTransferLogEntity detail = boxTransferLogService.getOne(Condition.getQueryWrapper(boxTransferLog));
		return R.data(BoxTransferLogWrapper.build().entityVO(detail));
	}
	/**
	 * 溯源-倒箱记录 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入boxTransferLog")
	public R<IPage<BoxTransferLogVO>> list(BoxTransferLogEntity boxTransferLog, Query query) {
		IPage<BoxTransferLogEntity> pages = boxTransferLogService.page(Condition.getPage(query), Condition.getQueryWrapper(boxTransferLog));
		return R.data(BoxTransferLogWrapper.build().pageVO(pages));
	}

	/**
	 * 溯源-倒箱记录 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入boxTransferLog")
	public R<IPage<BoxTransferLogVO>> page(BoxTransferLogVO boxTransferLog, Query query) {
		IPage<BoxTransferLogVO> pages = boxTransferLogService.selectBoxTransferLogPage(Condition.getPage(query), boxTransferLog);
		return R.data(pages);
	}

	/**
	 * 溯源-倒箱记录 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入boxTransferLog")
	public R save(@Valid @RequestBody BoxTransferLogEntity boxTransferLog) {
		return R.status(boxTransferLogService.save(boxTransferLog));
	}

	/**
	 * 溯源-倒箱记录 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入boxTransferLog")
	public R update(@Valid @RequestBody BoxTransferLogEntity boxTransferLog) {
		return R.status(boxTransferLogService.updateById(boxTransferLog));
	}

	/**
	 * 溯源-倒箱记录 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入boxTransferLog")
	public R submit(@Valid @RequestBody BoxTransferLogEntity boxTransferLog) {
		return R.status(boxTransferLogService.saveOrUpdate(boxTransferLog));
	}

	/**
	 * 溯源-倒箱记录 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(boxTransferLogService.deleteLogic(Func.toLongList(ids)));
	}


}
