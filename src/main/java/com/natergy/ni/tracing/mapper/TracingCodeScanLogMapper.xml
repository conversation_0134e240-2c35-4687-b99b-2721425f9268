<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.tracing.mapper.TracingCodeScanLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="qrCodeScanLogResultMap" type="com.natergy.ni.tracing.entity.TracingCodeScanLogEntity">
        <result column="qrcode_id" property="qrcodeId"/>
        <result column="box_id" property="boxId"/>
        <result column="scan_date" property="scanDate"/>
        <result column="scan_ip" property="scanIp"/>
        <result column="scan_ua" property="scanUa"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectQrCodeScanLogPage" resultMap="qrCodeScanLogResultMap">
        select * from ni_tracing_qrcode_scan_log where is_deleted = 0
    </select>
    <delete id="deleteLog">
        DELETE sl FROM ni_tracing_qrcode_scan_log sl
        INNER JOIN ni_tracing_qrcode qr ON sl.qrcode_id = qr.id
        INNER JOIN ni_tracing_production_batch ntpb ON ntpb.id = qr.batch_id
        WHERE ntpb.id in
        <foreach collection="validIds" item="validId" open="(" separator="," close=")">
            #{validId}
        </foreach>
    </delete>
    <select id="deleteLogCount" resultType="java.lang.Long">
        SELECT COUNT(sl.id)
        FROM ni_tracing_qrcode_scan_log sl
        INNER JOIN ni_tracing_qrcode qr ON sl.qrcode_id = qr.id
        INNER JOIN ni_tracing_production_batch ntpb ON ntpb.id = qr.batch_id
        WHERE ntpb.id IN
        <foreach collection="validIds" item="validId" open="(" separator="," close=")">
            #{validId}
        </foreach>
    </select>


</mapper>
