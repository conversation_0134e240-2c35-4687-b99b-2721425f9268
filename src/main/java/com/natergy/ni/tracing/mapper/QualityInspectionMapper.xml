<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.tracing.mapper.QualityInspectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="qualityInspectionResultMap" type="com.natergy.ni.tracing.entity.QualityInspectionEntity">
        <result column="code" property="code"/>
        <result column="result" property="result"/>
        <result column="date" property="date"/>
        <result column="inspector_id" property="inspectorId"/>
        <result column="inspector_name" property="inspectorName"/>
        <result column="water_content" property="waterContent"/>
        <result column="after_burning_na_cl" property="afterBurningNaCL"/>
        <result column="after_burning_li_cl" property="afterBurningLiCL"/>
        <result column="effective_water_adsorption" property="effectiveWaterAdsorption"/>
        <result column="strength" property="strength"/>
        <result column="coefficient_of_variation" property="coefficientOfVariation"/>
        <result column="bulk_density" property="bulkDensity"/>
        <result column="particle_size" property="particleSize"/>
        <result column="particle_size_qualified_rate" property="particleSizeQualifiedRate"/>
        <result column="temperature_rise_10" property="temperatureRise10"/>
        <result column="temperature_rise_20" property="temperatureRise20"/>
        <result column="temperature_rise_30" property="temperatureRise30"/>
        <result column="temperature_rise_50" property="temperatureRise50"/>
        <result column="falling_powder_5g" property="fallingPowder5g"/>
        <result column="falling_powder_before_grinding" property="fallingPowderBeforeGrinding"/>
        <result column="falling_powder_after_grinding" property="fallingPowderAfterGrinding"/>
        <result column="waste" property="waste"/>
        <result column="inspiratory_volume_170t" property="inspiratoryVolume170t"/>
        <result column="inspiratory_volume_70t" property="inspiratoryVolume70t"/>
        <result column="inspiratory_volume_96h" property="inspiratoryVolume96h"/>
        <result column="gas_desorption" property="gasDesorption"/>
        <result column="ph" property="ph"/>
        <result column="koh" property="koh"/>
        <result column="adsorption_en" property="adsorptionEN"/>
        <result column="attach" property="attach"/>
        <result column="area" property="area"/>
        <result column="outer_packaging" property="outerPackaging"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="inner_package" property="innerPackage"/>
        <result column="remark" property="remark"/>
        <result column="spec" property="spec"/>
        <result column="shift" property="shift"/>
        <result column="brand_name" property="brandName"/>
        <result column="ship_date" property="shipDate"/>
        <result column="ship_code" property="shipCode"/>
        <result column="stock_id" property="stockId"/>
        <result column="unit" property="unit"/>
        <result column="quantity" property="quantity"/>
        <result column="weight" property="weight"/>
        <result column="solid_bulk_density" property="solidBulkDensity"/>
        <result column="en_bulk_density" property="enBulkDensity"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_user" property="createUser"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="selectQualityInspectionPage" resultMap="qualityInspectionResultMap">
        select *
        from ni_tracing_quality_inspection
        where is_deleted = 0
    </select>

    <select id="getNewCode" resultType="java.lang.Integer">
        SELECT
                ISNULL(MAX(CAST(SUBSTRING(code, 9, LEN(code)-8) AS INT)), 0) + 1
        FROM ni_tracing_quality_inspection
        WHERE code LIKE concat(#{code,jdbcType=VARCHAR},'%');
    </select>


</mapper>
