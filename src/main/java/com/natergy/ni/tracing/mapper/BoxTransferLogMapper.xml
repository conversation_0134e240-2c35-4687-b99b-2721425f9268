<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.tracing.mapper.BoxTransferLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="boxTransferLogResultMap" type="com.natergy.ni.tracing.entity.BoxTransferLogEntity">
        <result column="transfer_type" property="transferType"/>
        <result column="target_batch_id" property="targetBatchId"/>
        <result column="source_batch_id" property="sourceBatchId"/>
        <result column="target_box_id" property="targetBoxId"/>
        <result column="source_box_id" property="sourceBoxId"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_time" property="updateTime"/>
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="boxTransferLogVoMap" type="com.natergy.ni.tracing.vo.BoxTransferLogVO"
               extends="boxTransferLogResultMap">
        <result property="boxCode" column="box_code" />
        <result property="targetBatchCode" column="target_batch_code" />
        <result property="sourceBatchCode" column="source_batch_code" />
        <result property="targetOuterPackagingName" column="target_outer_packaging_name" />
        <result property="sourceOuterPackagingName" column="source_outer_packaging_name" />
    </resultMap>

    <select id="selectBoxTransferLogPage" resultMap="boxTransferLogVoMap">
        select l.id,
               l.transfer_type,
               l.target_batch_id,
               l.source_batch_id,
               l.target_box_id,
               l.source_box_id,
               l.transfer_date,
               l.operator_id,
               l.operator_name,
               l.tenant_id,
               l.status,
               l.is_deleted,
               l.create_user,
               l.create_dept,
               l.create_time,
               l.update_user,
               l.update_time,
               tba.batch_code AS target_batch_code,
               sba.batch_code AS source_batch_code,
               tbo.box_code,
               tbo.outer_packaging_name AS target_outer_packaging_name,
               sbo.outer_packaging_name AS source_outer_packaging_name
        from ni_tracing_box_transfer_log l
        left join ni_tracing_production_batch tba on tba.id = l.target_batch_id
        left join ni_tracing_production_batch sba on sba.id = l.source_batch_id
        left join ni_tracing_box_info tbo on tbo.id = l.target_box_id
        left join ni_tracing_box_info sbo on sbo.id = l.source_box_id
        <where>
            <if test="filter.boxCode != null and filter.boxCode != ''">
                tbo.box_code = #{filter.boxCode}
            </if>
            <if test="filter.targetBatchCode != null and filter.targetBatchCode != ''">
                tbo.box_code = #{filter.targetBatchCode}
            </if>
            <if test="filter.sourceBatchCode != null and filter.sourceBatchCode != ''">
                tbo.box_code = #{filter.sourceBatchCode}
            </if>
            AND l.is_deleted = 0
        </where>
    </select>


</mapper>
