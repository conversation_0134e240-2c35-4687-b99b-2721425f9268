/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 溯源-质检报告 对外展示 实体类
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@TableName("ni_tracing_quality_inspection_modified")
@ApiModel(value = "QualityInspectionModifiedEntity对象", description = "溯源-质检报告")
@EqualsAndHashCode(callSuper = true)
public class QualityInspectionModifiedEntity extends TenantEntity {

    @ApiModelProperty(value = "原始质检报告id")
    @TableField(value = "origin_inspection_id")
    private Long originInspectionId;

    @ApiModelProperty(value = "是否合格")
    @TableField(value = "result")
    private Boolean result;

    @ApiModelProperty(value = "质检日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "date")
    private LocalDate date;

    @ApiModelProperty(value = "含水")
    @TableField(value = "water_content")
    private String waterContent;

    @ApiModelProperty(value = "烧后NaCL")
    @TableField(value = "after_burning_na_cl")
    private String afterBurningNaCl;

    @ApiModelProperty(value = "烧后LiC")
    @TableField(value = "after_burning_li_cl")
    private String afterBurningLiCl;

    @ApiModelProperty(value = "有效水吸附")
    @TableField(value = "effective_water_adsorption")
    private String effectiveWaterAdsorption;

    @ApiModelProperty(value = "强度")
    @TableField(value = "strength")
    private String strength;

    @ApiModelProperty(value = "变异系数")
    @TableField(value = "coefficient_of_variation")
    private String coefficientOfVariation;

    @ApiModelProperty(value = "堆积密度")
    @TableField(value = "bulk_density")
    private String bulkDensity;

    @ApiModelProperty(value = "粒度")
    @TableField(value = "particle_size")
    private String particleSize;

    @ApiModelProperty(value = "粒度合格率(%)")
    @TableField(value = "particle_size_qualified_rate")
    private String particleSizeQualifiedRate;

    @ApiModelProperty(value = "温升10g")
    @TableField(value = "temperature_rise_10")
    private String temperatureRise10;

    @ApiModelProperty(value = "温升20g")
    @TableField(value = "temperature_rise_20")
    private String temperatureRise20;

    @ApiModelProperty(value = "温升30g")
    @TableField(value = "temperature_rise_30")
    private String temperatureRise30;

    @ApiModelProperty(value = "温升50g")
    @TableField(value = "temperature_rise_50")
    private String temperatureRise50;

    @ApiModelProperty(value = "5g落粉")
    @TableField(value = "falling_powder_5g")
    private String fallingPowder5g;

    @ApiModelProperty(value = "磨前落粉")
    @TableField(value = "falling_powder_before_grinding")
    private String fallingPowderBeforeGrinding;

    @ApiModelProperty(value = "磨后落粉")
    @TableField(value = "falling_powder_after_grinding")
    private String fallingPowderAfterGrinding;

    @ApiModelProperty(value = "渣子")
    @TableField(value = "waste")
    private String waste;

    @ApiModelProperty(value = "170度吸气量")
    @TableField(value = "inspiratory_volume_170t")
    private String inspiratoryVolume170t;

    @ApiModelProperty(value = "70度吸气量")
    @TableField(value = "inspiratory_volume_70t")
    private String inspiratoryVolume70t;

    @ApiModelProperty(value = "96H吸气量")
    @TableField(value = "inspiratory_volume_96h")
    private String inspiratoryVolume96h;

    @ApiModelProperty(value = "气体解吸量(ml/g)")
    @TableField(value = "gas_desorption")
    private String gasDesorption;

    @ApiModelProperty(value = "PH值")
    @TableField(value = "ph")
    private String ph;

    @ApiModelProperty(value = "KOH")
    @TableField(value = "koh")
    private String koh;

    @ApiModelProperty(value = "欧标吸附")
    @TableField(value = "adsorption_en")
    private String adsorptionEN;

	@ApiModelProperty(value = "敦实堆积密度")
	@TableField(value = "solid_bulk_density")
	private String solidBulkDensity;

	@ApiModelProperty(value = "欧标堆积密度")
	@TableField(value = "en_bulk_density")
	private String enBulkDensity;
}
