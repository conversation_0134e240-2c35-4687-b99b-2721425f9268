/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 溯源-倒箱记录 实体类
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@Data
@TableName("ni_tracing_box_transfer_log")
@ApiModel(value = "BoxTransferLog对象", description = "溯源-倒箱记录")
@EqualsAndHashCode(callSuper = true)
public class BoxTransferLogEntity extends TenantEntity {

	/**
	 * 操作类型;1:倒箱, 2:倒垛
	 */
	@ApiModelProperty(value = "操作类型;1:倒箱, 2:倒垛")
	private Integer transferType;
	/**
	 * 新批次id
	 */
	@ApiModelProperty(value = "新批次id")
	private Long targetBatchId;
	/**
	 * 旧批次id
	 */
	@ApiModelProperty(value = "旧批次id")
	private Long sourceBatchId;
	/**
	 * 新包装箱id
	 */
	@ApiModelProperty(value = "新包装箱id")
	private Long targetBoxId;
	/**
	 * 旧包装箱id
	 */
	@ApiModelProperty(value = "旧包装箱id")
	private Long sourceBoxId;
	/**
	 * 倒箱/倒垛日期
	 */
	@ApiModelProperty(value = "倒箱/倒垛日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime transferDate;
	/**
	 * 操作人id
	 */
	@ApiModelProperty(value = "操作人id")
	private Long operatorId;
	/**
	 * 操作人姓名,冗余
	 */
	@ApiModelProperty(value = "操作人姓名,冗余")
	private String operatorName;

}
