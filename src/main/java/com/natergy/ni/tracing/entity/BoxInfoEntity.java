/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.lang.Double;
import java.lang.Boolean;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 溯源-批次箱体信息 实体类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@TableName("ni_tracing_box_info")
@ApiModel(value = "BoxInfo对象", description = "溯源-批次箱体信息")
@EqualsAndHashCode(callSuper = true)
public class BoxInfoEntity extends TenantEntity {

	/**
	 * 批次id
	 */
	@ApiModelProperty(value = "批次id")
	private Long batchId;
	/**
	 * 批次号,冗余
	 */
	@ApiModelProperty(value = "批次号")
	private String batchCode;

	/**
	 * 对外批次号
	 */
	@ApiModelProperty(value = "对外批次号")
	private String displayBatchCode;
	/**
	 * 箱号
	 */
	@ApiModelProperty(value = "箱号")
	@NotNull
	@NotEmpty
	private String boxCode;

	/**
	 * 对外箱号
	 */
	@ApiModelProperty(value = "对外箱号")
	@NotNull
	@NotEmpty
	private String displayBoxCode;
	/**
	 * 生产日期
	 */
	@ApiModelProperty(value = "生产日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotNull
	private LocalDateTime productionDate;
	/**
	 * 序号, 按垛递增,与箱号匹配
	 */
	@ApiModelProperty(value = "序号, 按垛递增,与箱号匹配")
	@NotNull
	private Integer boxNumber;
	/**
	 * 品牌id
	 */
	@ApiModelProperty(value = "品牌id")
	@NotNull
	private Long brandId;
	/**
	 * 品牌名称,冗余
	 */
	@ApiModelProperty(value = "品牌名称,冗余")
	@NotNull
	@NotEmpty
	private String brandName;
	/**
	 * 品牌编码,冗余
	 */
	@ApiModelProperty(value = "品牌编码,冗余")
	@NotNull
	@NotEmpty
	private String brandCode;
	/**
	 * 内包装id
	 */
	@ApiModelProperty(value = "内包装id")
	@NotNull
	private Long innerPackagingId;
	/**
	 * 内包装名称,冗余
	 */
	@ApiModelProperty(value = "内包装名称,冗余")
	@NotNull
	@NotEmpty
	private String innerPackagingName;
	/**
	 * 内包装编码,冗余
	 */
	@ApiModelProperty(value = "内包装编码,冗余")
	@NotNull
	@NotEmpty
	private String innerPackagingCode;
	/**
	 * 外包装id
	 */
	@ApiModelProperty(value = "外包装id")
	@NotNull
	private Long outerPackagingId;
	/**
	 * 外包装名称,冗余
	 */
	@ApiModelProperty(value = "外包装名称,冗余")
	@NotNull
	@NotEmpty
	private String outerPackagingName;
	/**
	 * 外包装容量,冗余
	 */
	@ApiModelProperty(value = "外包装容量,冗余")
	@NotNull
	private BigDecimal outerPackagingCapacity;
	/**
	 * 规格id
	 */
	@ApiModelProperty(value = "规格id")
	@NotNull
	private Long specificationId;
	/**
	 * 规格名称,冗余
	 */
	@ApiModelProperty(value = "规格名称,冗余")
	@NotNull
	@NotEmpty
	private String specificationName;
	/**
	 * 规格编码,冗余
	 */
	@ApiModelProperty(value = "规格编码,冗余")
	@NotNull
	@NotEmpty
	private String specificationCode;
	/**
	 * 质量id
	 */
	@ApiModelProperty(value = "质量id")
	@NotNull
	private Long qualityId;
	/**
	 * 质量名称,冗余
	 */
	@ApiModelProperty(value = "质量名称,冗余")
	@NotNull
	@NotEmpty
	private String qualityName;
	/**
	 * 是否作废, 倒箱后,此箱作废
	 */
	@ApiModelProperty(value = "是否作废, 倒箱后,此箱作废")
	private Integer isInvalid;
	/**
	 * 作废时间
	 */
	@ApiModelProperty(value = "作废时间")
	private LocalDateTime invalidDate;
	/**
	 * 原箱id
	 */
	@ApiModelProperty(value = "原箱id")
	private Long sourceBoxId;
	/**
	 * 原箱编码,冗余
	 */
	@ApiModelProperty(value = "原箱编码,冗余")
	private String sourceBoxCode;
	/**
	 * 渠道; 0: 正常 1: 倒箱 2: 倒垛
	 */
	@ApiModelProperty(value = "渠道; 0: 正常 1: 倒箱 2: 倒垛")
	@NotNull
	private Integer channel;
	/**
	 * 打标次数
	 */
	@ApiModelProperty(value = "打标次数")
	@NotNull
	private Integer markingCount;
	/**
	 * 打标内容
	 */
	@ApiModelProperty(value = "打标内容")
	@NotNull
	@NotEmpty
	private String markingContent;
}
