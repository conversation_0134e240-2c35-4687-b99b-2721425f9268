package com.natergy.ni.tracing.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 溯源-质检报告模板指标
 */
@Data
@TableName("ni_tracing_report_index")
@EqualsAndHashCode(callSuper = true)
public class TracingReportIndex extends TenantEntity {

	@ApiModelProperty(value = "模板标识")
	@TableField(value = "template_name")
	private String templateName;
	@ApiModelProperty(value = "模板说明")
	@TableField(value = "template_illustrate")
	private String templateIllustrate;
	@ApiModelProperty(value = "检测标准-松散堆积密度")
	@TableField(value = "bulk_density_standard")
	private String bulkDensityStandard;
	@ApiModelProperty(value = "检测标准-静态水吸附量")
	@TableField(value = "after_burningLiCl_standard")
	private String afterBurningLiClStandard;
	@ApiModelProperty(value = "检测标准-粒度")
	@TableField(value = "particle_size_standard")
	private String particleSizeStandard;
	@ApiModelProperty(value = "检测标准-包装品水含量")
	@TableField(value = "waterContent_standard")
	private String waterContentStandard;
	@ApiModelProperty(value = "检测标准-气体解吸量")
	@TableField(value = "gas_desorption_standard")
	private String gasDesorptionStandard;
	@ApiModelProperty(value = "检测标准-温升")
	@TableField(value = "temperature_rise_20_standard")
	private String temperatureRise20Standard;
	@ApiModelProperty(value = "检测标准-粉尘量")
	@TableField(value = "falling_powder_before_grinding_standard")
	private String fallingPowderBeforeGrindingStandard;
	@ApiModelProperty(value = "合格说明")
	@TableField(value = "qualified")
	private String qualified;
	@TableField(exist = false)
	private String particleSizeStandardText;

}
