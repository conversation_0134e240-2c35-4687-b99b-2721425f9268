/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.tracing.entity.BoxTransferLogEntity;
import com.natergy.ni.tracing.vo.BoxTransferLogVO;
import java.util.Objects;

/**
 * 溯源-倒箱记录 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
public class BoxTransferLogWrapper extends BaseEntityWrapper<BoxTransferLogEntity, BoxTransferLogVO>  {

	public static BoxTransferLogWrapper build() {
		return new BoxTransferLogWrapper();
 	}

	@Override
	public BoxTransferLogVO entityVO(BoxTransferLogEntity boxTransferLog) {
		BoxTransferLogVO boxTransferLogVO = Objects.requireNonNull(BeanUtil.copy(boxTransferLog, BoxTransferLogVO.class));

		//User createUser = UserCache.getUser(boxTransferLog.getCreateUser());
		//User updateUser = UserCache.getUser(boxTransferLog.getUpdateUser());
		//boxTransferLogVO.setCreateUserName(createUser.getName());
		//boxTransferLogVO.setUpdateUserName(updateUser.getName());

		return boxTransferLogVO;
	}


}
