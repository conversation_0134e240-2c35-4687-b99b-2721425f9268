/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.natergy.ni.tracing.entity.QualityInspectionEntity;
import com.natergy.ni.tracing.vo.QualityInspectionVO;
import java.util.Objects;

/**
 * 溯源-质检报告 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public class QualityInspectionWrapper extends BaseEntityWrapper<QualityInspectionEntity, QualityInspectionVO>  {

	public static QualityInspectionWrapper build() {
		return new QualityInspectionWrapper();
 	}

	@Override
	public QualityInspectionVO entityVO(QualityInspectionEntity qualityInspection) {
		QualityInspectionVO qualityInspectionVO = Objects.requireNonNull(BeanUtil.copy(qualityInspection, QualityInspectionVO.class));

		//User createUser = UserCache.getUser(qualityInspection.getCreateUser());
		//User updateUser = UserCache.getUser(qualityInspection.getUpdateUser());
		//qualityInspectionVO.setCreateUserName(createUser.getName());
		//qualityInspectionVO.setUpdateUserName(updateUser.getName());

		return qualityInspectionVO;
	}


}
