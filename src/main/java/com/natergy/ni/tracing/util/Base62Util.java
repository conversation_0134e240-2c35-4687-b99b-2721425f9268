package com.natergy.ni.tracing.util;

public class Base62Util {

	private static final String ALPHABET = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	private static final int BASE = ALPHABET.length();

	public static String encode(long value) {
		StringBuilder sb = new StringBuilder();
		while (value > 0) {
			sb.append(ALPHABET.charAt((int) (value % BASE)));
			value /= BASE;
		}
		return sb.reverse().toString();
	}

	public static long decode(String str) {
		long value = 0;
		for (int i = 0; i < str.length(); i++) {
			value = value * BASE + ALPHABET.indexOf(str.charAt(i));
		}
		return value;
	}

}
