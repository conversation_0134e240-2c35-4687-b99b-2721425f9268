package com.natergy.ni.tracing.util;

public class SnowflakeIdWorker {

	public static SnowflakeIdWorker build(long workerId, long datacenterId) {
		return new SnowflakeIdWorker(workerId, datacenterId);
	}

	// 起始时间戳，用于计算时间偏移量
	private final long twepoch = 173566080000L;

	// 工作机器ID所占的位数
	private final long workerIdBits = 5L;

	// 数据中心ID所占的位数
	private final long datacenterIdBits = 5L;

	// 最大工作机器ID
	private final long maxWorkerId = -1L ^ (-1L << workerIdBits);

	// 最大数据中心ID
	private final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);

	// 序列号所占的位数
	private final long sequenceBits = 12L;

	// 工作机器ID向左移的位数
	private final long workerIdShift = sequenceBits;

	// 数据中心ID向左移的位数
	private final long datacenterIdShift = sequenceBits + workerIdBits;

	// 时间戳向左移的位数
	private final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;

	// 序列号掩码
	private final long sequenceMask = -1L ^ (-1L << sequenceBits);

	// 工作机器ID
	private long workerId;

	// 数据中心ID
	private long datacenterId;

	// 毫秒内序列号
	private long sequence = 0L;

	// 上次生成ID的时间戳
	private long lastTimestamp = -1L;

	public SnowflakeIdWorker(long workerId, long datacenterId) {
		if (workerId > maxWorkerId || workerId < 0) {
			throw new IllegalArgumentException(String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
		}
		if (datacenterId > maxDatacenterId || datacenterId < 0) {
			throw new IllegalArgumentException(String.format("datacenter Id can't be greater than %d or less than 0", maxDatacenterId));
		}
		this.workerId = workerId;
		this.datacenterId = datacenterId;
	}

	public synchronized long nextId() {
		long timestamp = timeGen();

		if (timestamp < lastTimestamp) {
			throw new RuntimeException(String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
		}

		if (lastTimestamp == timestamp) {
			sequence = (sequence + 1) & sequenceMask;
			if (sequence == 0) {
				timestamp = tilNextMillis(lastTimestamp);
			}
		} else {
			sequence = 0L;
		}

		lastTimestamp = timestamp;

		return ((timestamp - twepoch) << timestampLeftShift) | (datacenterId << datacenterIdShift) | (workerId << workerIdShift) | sequence;
	}

	protected long tilNextMillis(long lastTimestamp) {
		long timestamp = timeGen();
		while (timestamp <= lastTimestamp) {
			timestamp = timeGen();
		}
		return timestamp;
	}

	protected long timeGen() {
		return System.currentTimeMillis();
	}
}
