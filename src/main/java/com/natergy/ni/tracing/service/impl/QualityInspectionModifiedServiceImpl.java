/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.tracing.dto.QualityInspectionModifiedDto;
import com.natergy.ni.tracing.entity.QualityInspectionModifiedEntity;
import com.natergy.ni.tracing.mapper.QualityInspectionModifiedMapper;
import com.natergy.ni.tracing.service.IQualityInspectionModifiedService;
import com.natergy.ni.tracing.vo.QualityInspectionModifiedVO;
import com.natergy.ni.tracing.vo.QualityInspectionVO;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 溯源-质检报告 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
@AllArgsConstructor
public class QualityInspectionModifiedServiceImpl extends BaseServiceImpl<QualityInspectionModifiedMapper,
	QualityInspectionModifiedEntity> implements IQualityInspectionModifiedService {

	/**
	 * 查询对外质检报告列表
	 * @param qualityInspection
	 * @param page
	 * @return
	 */
	@Override
	public IPage<QualityInspectionModifiedVO> selectQualityInspectionEntityOut(QualityInspectionModifiedDto qualityInspection, IPage<QualityInspectionModifiedVO> page) {
		return page.setRecords(baseMapper.selectQualityInspectionEntityOut(page, qualityInspection));
	}

	@Override
	public IPage<QualityInspectionModifiedVO> selectQualityInspectionEntityIn(QualityInspectionModifiedDto qualityInspection, IPage<QualityInspectionModifiedVO> page) {
		return page.setRecords(baseMapper.selectQualityInspectionEntityIn(page, qualityInspection));
	}
}
