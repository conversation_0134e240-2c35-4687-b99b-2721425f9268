/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.service;

import com.natergy.ni.tracing.dto.*;
import com.natergy.ni.tracing.entity.ProductionBatchEntity;
import com.natergy.ni.tracing.vo.ProductionBatchSummaryVO;
import com.natergy.ni.tracing.vo.ProductionBatchVO;
import com.natergy.ni.tracing.vo.ScanResultVO;

import java.time.LocalDateTime;
import java.util.List;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.servlet.http.HttpServletResponse;

/**
 * 溯源-生产批次 服务类
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
public interface IProductionBatchService extends BaseService<ProductionBatchEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param productionBatch
	 * @return
	 */
	IPage<ProductionBatchVO> selectProductionBatchPage(IPage<ProductionBatchVO> page, ProductionBatchVO productionBatch);

	/**
	 * 根据批次id查询当前批次质检报告的
	 *
	 * @param printIds
	 * @return
	 */
    List<ScanResultVO> getQualityInspectionReport(Long[] printIds,Integer quantity);
	/**
	 * 按天出批次的统计信息
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
    List<ProductionBatchSummaryVO> summaries(LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 校验sku是否存在
	 * @param para
	 * @return
	 */
	boolean verifySku(VerifySkuExistParameter para);

	/**
	 * @param skuId 旧ni sku id
	 * @return
	 */
	ProductSkuAttributeIdDTO getSkuAttributeId(String skuId);

	/**
	 * 删除质检报告，同时解除批次与质检报告的关联
	 * @param ids
	 * @return
	 */
	boolean deleteQuality(String ids);
	/**
	 *
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	List<ProductionBatchEntity> selectDateRangeList(LocalDateTime startDate, LocalDateTime endDate);

    IPage<ProductionBatchVO> selectParameterPage(QualityInspectionSearchParameter parameter, IPage<ProductionBatchVO> page);

	int selectBatchFgTransactionSum(String batchNo);

	ProductionBatchVO getProductionBatchById(Long id);

	void generateQrcode(HttpServletResponse response, String[] ids);

    String getArea(Long id);

	boolean addBatch(ProductionEncodeDTO productionEncodeDTO);

	List<Long> filterValidIds(List<Long> idList);

    boolean deleteBatch(List<Long> validIds);

	ProductionBatchVO getProductionBatchByBatchCode(String batchCode);
}
