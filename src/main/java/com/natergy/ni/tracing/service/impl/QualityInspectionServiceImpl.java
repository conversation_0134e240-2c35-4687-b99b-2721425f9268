/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.natergy.ni.tracing.dto.AssociateBatchParameter;
import com.natergy.ni.tracing.entity.ProductionBatchEntity;
import com.natergy.ni.tracing.entity.QualityInspectionEntity;
import com.natergy.ni.tracing.entity.QualityInspectionModifiedEntity;
import com.natergy.ni.tracing.service.IProductionBatchService;
import com.natergy.ni.tracing.service.IQualityInspectionModifiedService;
import com.natergy.ni.tracing.vo.QualityInspectionVO;
import com.natergy.ni.tracing.mapper.QualityInspectionMapper;
import com.natergy.ni.tracing.service.IQualityInspectionService;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 溯源-质检报告 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
@AllArgsConstructor
public class QualityInspectionServiceImpl extends BaseServiceImpl<QualityInspectionMapper, QualityInspectionEntity> implements IQualityInspectionService {

	private final IProductionBatchService productionBatchService;

	private final IQualityInspectionModifiedService qualityInspectionModifiedService;

	@Override
	public IPage<QualityInspectionVO> selectQualityInspectionPage(IPage<QualityInspectionVO> page, QualityInspectionVO qualityInspection) {
		return page.setRecords(baseMapper.selectQualityInspectionPage(page, qualityInspection));
	}

	/**
	 * 关联生产批次
	 *
	 * @param parameter
	 */
	@Override
	public void associateBatch(AssociateBatchParameter parameter) {
		List<ProductionBatchEntity> productionBatchEntities = productionBatchService.listByIds(parameter.getBatchIds());

		productionBatchEntities.forEach(b -> {
			b.setInspectionId(parameter.getInspectionId());
		});

		productionBatchService.updateBatchById(productionBatchEntities);
	}

	@Override
	public String getNewCode(String area) {
		LocalDate currentDate = LocalDate.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMM");
		String formattedDate = currentDate.format(formatter);
		String prefix;
		if ("CN".equals(area)){
			prefix = "GNBG";
		}else {
			prefix = "GWBG";
		}
		int number = baseMapper.getNewCode(prefix + formattedDate);
		return prefix + formattedDate + String.format("%04d", number);
	}

	/**
	 * 发布质检报告：修改发布状态，新增对外质检报告
	 * @param qualityInspection
	 * @return
	 */
	@Override
	@Transactional
	public boolean updatePublishStatus(QualityInspectionEntity qualityInspection) {
		qualityInspection.setPublishStatus(CommonConstant.STATUS_ONE);
		boolean update = super.updateById(qualityInspection);

		boolean save = true;
		QualityInspectionModifiedEntity exist = qualityInspectionModifiedService.getOne(
			new LambdaQueryWrapper<QualityInspectionModifiedEntity>()
				.eq(QualityInspectionModifiedEntity::getOriginInspectionId, qualityInspection.getId())
		);
		if (exist == null) {
			QualityInspectionModifiedEntity modifiedEntity = BeanUtil.copy(qualityInspection, QualityInspectionModifiedEntity.class);
			modifiedEntity.setId(null);
			modifiedEntity.setOriginInspectionId(qualityInspection.getId());
			save = qualityInspectionModifiedService.save(modifiedEntity);
		}
		return save && update;
	}

	@Override
	@Transactional
	public boolean saveOrUpdate(QualityInspectionEntity entity) {

		entity.setInspectorId(AuthUtil.getUserId());
		entity.setInspectorName(AuthUtil.getUserName());
		entity.setTenantId(BladeConstant.ADMIN_TENANT_ID);
		boolean result = super.saveOrUpdate(entity);
		return result;
	}
}
