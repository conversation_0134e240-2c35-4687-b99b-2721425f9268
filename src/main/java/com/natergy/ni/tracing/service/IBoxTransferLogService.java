/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.service;

import com.natergy.ni.tracing.entity.BoxTransferLogEntity;
import com.natergy.ni.tracing.vo.BoxTransferLogVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 溯源-倒箱记录 服务类
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
public interface IBoxTransferLogService extends BaseService<BoxTransferLogEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param boxTransferLog
	 * @return
	 */
	IPage<BoxTransferLogVO> selectBoxTransferLogPage(IPage<BoxTransferLogVO> page, BoxTransferLogVO boxTransferLog);


}
