/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.service;

import com.natergy.ni.tracing.entity.TracingCodeEntity;
import com.natergy.ni.tracing.vo.ScanResultVO;
import com.natergy.ni.tracing.vo.TracingCodeVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 溯源-二维码信息 服务类
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
public interface ITracingCodeService extends BaseService<TracingCodeEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param qrCode
	 * @return
	 */
	IPage<TracingCodeVO> selectQrCodePage(IPage<TracingCodeVO> page, TracingCodeVO qrCode);

	/**
	 * 根据二维码内容查询
	 *
	 * @param qrCodeContent 二维码内容
	 * @return 结果
	 */
	TracingCodeEntity selectQrCodeByContent(String qrCodeContent);

	/**
	 * 查询二维码对应的质检报告详细信息(国内)
	 * @param qrcodeContent
	 * @return
	 */
	ScanResultVO getQualityInspectionReportCN(String qrcodeContent);

	/**
	 * 查询二维码对应的质检报告详细信息(国外)
	 * @param qrcodeContent
	 * @return
	 */
	ScanResultVO getQualityInspectionReportOS(String qrcodeContent);

    boolean deleteCode(List<Long> validIds);
}
