package com.natergy.ni.tracing.service;

import com.natergy.ni.tracing.dto.DeleteProductBatchParameter;
import com.natergy.ni.tracing.dto.ProductionBatchDTO;
import com.natergy.ni.tracing.dto.ProductionBatchWithBoxInfoDTO;
import com.natergy.ni.tracing.dto.UpdateProductBatchAllowDeliveryParameter;

/**
 * 溯源数据上传服务
 */
public interface IDataUploadService {

	ProductionBatchWithBoxInfoDTO addProductionBatch(ProductionBatchWithBoxInfoDTO productionBatch);

	void updateBatchAllowDelivery(UpdateProductBatchAllowDeliveryParameter productionBatch);

	void deleteBatch(DeleteProductBatchParameter parameter);
}
