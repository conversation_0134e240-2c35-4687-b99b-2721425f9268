/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.natergy.ni.tracing.entity.TracingCodeEntity;
import com.natergy.ni.tracing.vo.ScanResultVO;
import com.natergy.ni.tracing.vo.TracingCodeVO;
import com.natergy.ni.tracing.mapper.TracingCodeMapper;
import com.natergy.ni.tracing.service.ITracingCodeService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 溯源-二维码信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Service
public class TracingCodeServiceImpl extends BaseServiceImpl<TracingCodeMapper, TracingCodeEntity> implements ITracingCodeService {

	@Override
	public IPage<TracingCodeVO> selectQrCodePage(IPage<TracingCodeVO> page, TracingCodeVO qrCode) {
		return page.setRecords(baseMapper.selectQrCodePage(page, qrCode));
	}

	/**
	 * 根据二维码内容查询
	 *
	 * @param qrCodeContent 二维码内容
	 * @return 结果
	 */
	@Override
	public TracingCodeEntity selectQrCodeByContent(String qrCodeContent) {
		LambdaQueryWrapper<TracingCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(TracingCodeEntity::getQrcodeContent, qrCodeContent);
		return getOne(queryWrapper,false);
	}

	@Override
	public ScanResultVO getQualityInspectionReportCN(String qrcodeContent) {
		return baseMapper.getQualityInspectionReportCN(qrcodeContent);
	}

	@Override
	public ScanResultVO getQualityInspectionReportOS(String qrcodeContent) {
		return baseMapper.getQualityInspectionReportOS(qrcodeContent);
	}

	@Override
	public boolean deleteCode(List<Long> validIds) {
		LambdaQueryWrapper<TracingCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.in(TracingCodeEntity::getBatchId,validIds);
		Long count = baseMapper.selectCount(queryWrapper);
		if (count > 0){
			return baseMapper.deleteCode(validIds);
		}else {
			return true;
		}
	}
}
