package com.natergy.ni.tracing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.natergy.ni.base.constant.PropertyConst;
import com.natergy.ni.product.entity.ProductPackaging;
import com.natergy.ni.product.mapper.ProductPackagingMapper;
import com.natergy.ni.tracing.dto.BoxChannel;
import com.natergy.ni.tracing.dto.BoxRepackParameter;
import com.natergy.ni.tracing.dto.BoxTransferParameter;
import com.natergy.ni.tracing.dto.TransferType;
import com.natergy.ni.tracing.entity.BoxInfoEntity;
import com.natergy.ni.tracing.entity.BoxTransferLogEntity;
import com.natergy.ni.tracing.entity.ProductionBatchEntity;
import com.natergy.ni.tracing.entity.TracingCodeEntity;
import com.natergy.ni.tracing.service.*;
import com.natergy.ni.tracing.util.Base62Util;
import com.natergy.ni.tracing.util.SnowflakeIdWorker;
import lombok.AllArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Service
@AllArgsConstructor
public class BoxTransferServiceImpl implements IBoxTransferService {

	private final IProductionBatchService productionBatchService;

	private final IBoxInfoService boxInfoService;

	private final ITracingCodeService qrCodeService;

	private final ProductPackagingMapper productPackagingMapper;

	private final IBoxTransferLogService boxTransferLogService;

	/**
	 * 倒垛
	 * <p>
	 * 倒垛时, 箱号,批次,二维码不变, 更新批次数据
	 *
	 * @param parameter 参数
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void transfer(BoxTransferParameter parameter) {

		ProductionBatchEntity targetBatch = productionBatchService.getById(parameter.getTargetBatchId());

		if (targetBatch == null) {
			throw new ServiceException("目标批次不存在");
		}

		List<BoxInfoEntity> boxInfoEntityList = boxInfoService.listByIds(parameter.getBoxIds());

		if (boxInfoEntityList == null || boxInfoEntityList.isEmpty()) {
			throw new ServiceException("没有有效的箱体数据");
		} else {
			List<BoxTransferLogEntity> logList = new ArrayList<>();

			for (BoxInfoEntity boxInfoEntity : boxInfoEntityList) {

				Long sourceBatchId = boxInfoEntity.getBatchId();

				boxInfoEntity.setBatchId(targetBatch.getId());
				boxInfoEntity.setBatchCode(targetBatch.getBatchCode());
				boxInfoEntity.setDisplayBatchCode(targetBatch.getDisplayBatchCode());
				boxInfoEntity.setChannel(BoxChannel.TRANSFER.value());

				BoxTransferLogEntity log = new BoxTransferLogEntity();
				log.setTransferType(TransferType.TRANSFER.value());
				log.setTargetBatchId(targetBatch.getId());
				log.setSourceBatchId(sourceBatchId);
				log.setTargetBoxId(boxInfoEntity.getId());
				log.setSourceBoxId(boxInfoEntity.getId());
				log.setTransferDate(LocalDateTime.now());
				log.setOperatorId(AuthUtil.getUserId());
				log.setOperatorName(AuthUtil.getUserName());
				log.setTenantId(BladeConstant.ADMIN_TENANT_ID);

				logList.add(log);
			}

			boxInfoService.updateBatchById(boxInfoEntityList);

			boxTransferLogService.saveBatch(logList);

		}
	}

	/**
	 * 倒箱
	 * <p>
	 * 倒箱时, 旧数据作废;
	 * 插入新数据: 箱号,批次不变, 更新外包装数据, 生成新的二维码
	 *
	 * @param parameters 参数
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void repack(List<BoxRepackParameter> parameters) {
		if (parameters == null || parameters.isEmpty()) {
			throw new ServiceException("没有有效的箱体数据");
		}

		for (BoxRepackParameter parameter : parameters) {
			BoxInfoEntity oldBoxInfo = boxInfoService.getById(parameter.getBoxId());

			if (oldBoxInfo != null) {

				if (oldBoxInfo.getIsInvalid().intValue() == PropertyConst.YesNo.YES.value()) {
					throw new ServiceException(oldBoxInfo.getBoxCode() + " 已倒箱, 不能重复操作");
				}

				LambdaQueryWrapper<ProductPackaging> queryWrapper = new LambdaQueryWrapper<>();
				queryWrapper.eq(ProductPackaging::getInnerPark, 0);
				// queryWrapper.eq(ProductPackaging::getStatus, 1);
				queryWrapper.eq(ProductPackaging::getId, parameter.getNewOuterPackagingId());

				ProductPackaging outerPackaging = productPackagingMapper.selectOne(queryWrapper);

				if (outerPackaging == null) {
					throw new ServiceException("外包装数据无效");
				}

				// 插入新数据
				BoxInfoEntity newBoxInfo = BeanUtil.copyProperties(oldBoxInfo, BoxInfoEntity.class);

				assert newBoxInfo != null;
				newBoxInfo.setId(null);
				newBoxInfo.setOuterPackagingId(outerPackaging.getId());
				newBoxInfo.setOuterPackagingName(outerPackaging.getName());
				newBoxInfo.setOuterPackagingCapacity(outerPackaging.getCapacity());
				newBoxInfo.setMarkingCount(1);

				newBoxInfo.setSourceBoxId(oldBoxInfo.getId());
				newBoxInfo.setSourceBoxCode(oldBoxInfo.getBoxCode());
				newBoxInfo.setChannel(BoxChannel.REPACK.value());

				boxInfoService.save(newBoxInfo);

				// 插入新二维码
				TracingCodeEntity qrCode = new TracingCodeEntity();
				String content = Base62Util.encode(
					SnowflakeIdWorker.build(
						new Random().nextInt(32),
						new Random().nextInt(32)
					).nextId()
				);
				qrCode.setQrcodeContent(content);
				qrCode.setBoxId(newBoxInfo.getId());
				qrCode.setBatchId(oldBoxInfo.getBatchId());
				qrCode.setScanCount(0);
				qrCode.setTenantId(BladeConstant.ADMIN_TENANT_ID);

				qrCodeService.save(qrCode);

				// 作废旧数据
				oldBoxInfo.setIsInvalid(PropertyConst.YesNo.YES.value());
				oldBoxInfo.setInvalidDate(LocalDateTime.now());

				boxInfoService.updateById(oldBoxInfo);

				// 插入日志
				BoxTransferLogEntity log = new BoxTransferLogEntity();
				log.setTransferType(TransferType.REPACK.value());
				log.setTargetBatchId(newBoxInfo.getBatchId());
				log.setSourceBatchId(oldBoxInfo.getBatchId());
				log.setTargetBoxId(newBoxInfo.getId());
				log.setSourceBoxId(oldBoxInfo.getId());
				log.setTransferDate(LocalDateTime.now());
				log.setOperatorId(AuthUtil.getUserId());
				log.setOperatorName(AuthUtil.getUserName());
				log.setTenantId(BladeConstant.ADMIN_TENANT_ID);

				boxTransferLogService.save(log);
			} else {
				throw new ServiceException("箱体数据无效");
			}
		}
	}
}
