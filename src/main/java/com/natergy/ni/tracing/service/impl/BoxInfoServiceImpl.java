/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.tracing.service.impl;

import com.natergy.ni.tracing.entity.BoxInfoEntity;
import com.natergy.ni.tracing.vo.BoxInfoVO;
import com.natergy.ni.tracing.mapper.BoxInfoMapper;
import com.natergy.ni.tracing.service.IBoxInfoService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 溯源-批次箱体信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Service
public class BoxInfoServiceImpl extends BaseServiceImpl<BoxInfoMapper, BoxInfoEntity> implements IBoxInfoService {

	@Override
	public IPage<BoxInfoVO> selectBoxInfoPage(IPage<BoxInfoVO> page, BoxInfoVO boxInfo) {
		return page.setRecords(baseMapper.selectBoxInfoPage(page, boxInfo));
	}

	@Override
	public boolean deleteBox(List<Long> validIds) {
		return baseMapper.deleteBox(validIds);
	}
}
