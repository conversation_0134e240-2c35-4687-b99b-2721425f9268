/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.feedback.entity;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 问题各负责人解决记录 实体类
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@TableName("ni_feedback_solving_record")
@ApiModel(value = "FeedbackSolvingRecord对象", description = "问题各负责人解决记录")
@EqualsAndHashCode(callSuper = true)
public class FeedbackSolvingRecordEntity extends TenantEntity {

	private static final long serialVersionUID = 7155730483629388797L;

	// 自定义 readObject 方法
	private void readObject(ObjectInputStream ois) throws IOException, ClassNotFoundException {
		// 先调用默认的反序列化方法
		ois.defaultReadObject();
		if (Objects.isNull(this.expectedPlan)) {
			this.expectedPlan = StringUtils.EMPTY;
		}
	}


	/**
	 * 问题id
	 */
	@ApiModelProperty(value = "问题id")
	private Long feedbackId;
	/**
	 * 负责人id
	 */
	@ApiModelProperty(value = "负责人id")
	private Long responsibilityPersonId;
	/**
	 * 预估解决时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "预估解决时间")
	private LocalDate estimatedTime;
	/**
	 * 预期方案
	 */
	@ApiModelProperty(value = "预期方案")
	private String expectedPlan;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remarks;
	/**
	 * 问题原因
	 */
	@ApiModelProperty(value = "问题原因")
	private String problemCause;
	/**
	 * 解决方案
	 */
	@ApiModelProperty(value = "解决方案")
	private String solution;
	/**
	 * 流程实例id
	 */
	@ApiModelProperty(value = "流程实例id")
	private String processInstanceId;
	/**
	 * 负责人名称
	 */
	@ApiModelProperty(value = "负责人名称")
	private String responsibilityPersonName;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "实际解决日期")
	private LocalDate resolveDate;

}
