<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.natergy.ni.feedback.mapper.FeedbackSolvingRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="feedbackSolvingRecordResultMap" type="com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity">
        <result column="feedback_id" property="feedbackId"/>
        <result column="responsibility_person_id" property="responsibilityPersonId"/>
        <result column="estimated_time" property="estimatedTime"/>
        <result column="expected_plan" property="expectedPlan"/>
        <result column="remarks" property="remarks"/>
        <result column="problem_cause" property="problemCause"/>
        <result column="solution" property="solution"/>
        <result column="status" property="status"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="responsibility_person_name" property="responsibilityPersonName"/>
        <result column="resolve_date" property="resolveDate"/>
        <result column="id" property="id"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>


    <select id="selectFeedbackSolvingRecordPage" resultMap="feedbackSolvingRecordResultMap">
        select * from ni_feedback_solving_record where is_deleted = 0
    </select>


</mapper>
