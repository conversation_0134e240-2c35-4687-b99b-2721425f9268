package org.springblade.datasource;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springblade.datasource.config.DynamicContextHolder;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @since 2023/2/18 14:41
 */
@Slf4j
@Aspect
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class DynamicDataSourceAspect {

	@Pointcut("@annotation(org.springblade.datasource.DataSource) " +
		"|| @within(org.springblade.datasource.DataSource)")
	public void dataSourcePointCut() {

	}

	@Around("dataSourcePointCut()")
	public Object around(ProceedingJoinPoint point) throws Throwable {
		MethodSignature signature = (MethodSignature) point.getSignature();
		DataSource methodDataSource = AnnotationUtils.findAnnotation(signature.getMethod(),
			DataSource.class);
		Class<?> tagClass = signature.getDeclaringType();
		boolean annotation = tagClass.isAnnotationPresent(DataSource.class);
		DataSource dataSource = null;
		if (annotation) {
			dataSource = tagClass.getAnnotation(DataSource.class);
		}
		if (dataSource != null || methodDataSource != null) {
			String value;
			if (methodDataSource != null) {
				value = methodDataSource.value();
			} else {
				value = dataSource.value();
			}

			DynamicContextHolder.push(value);
			log.debug("set datasource is {}", value);
		}
		try {
			return point.proceed();
		} finally {
			DynamicContextHolder.poll();
			log.debug("clean datasource");
		}
	}
}
