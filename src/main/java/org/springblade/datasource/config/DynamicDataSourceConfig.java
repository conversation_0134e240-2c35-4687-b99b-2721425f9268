package org.springblade.datasource.config;


import com.alibaba.druid.pool.DruidDataSource;
import lombok.RequiredArgsConstructor;
import org.springblade.datasource.properties.DataSourceProperties;
import org.springblade.datasource.properties.DynamicDataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @since 2023/2/18 14:47
 */
@Configuration
@EnableConfigurationProperties(DynamicDataSourceProperties.class)
@RequiredArgsConstructor
public class DynamicDataSourceConfig {

  private final DynamicDataSourceProperties properties;


  @Bean
  @ConfigurationProperties(prefix = "spring.datasource.druid")
  public DataSourceProperties dataSourceProperties() {
    return new DataSourceProperties();
  }

  @Bean
  public DynamicDataSource dynamicDataSource(DataSourceProperties dataSourceProperties) {
    DynamicDataSource dynamicDataSource = new DynamicDataSource();
    dynamicDataSource.setTargetDataSources(getDynamicDataSource());

    //默认数据源
    DruidDataSource defaultDataSource = DynamicDataSourceFactory.buildDruidDataSource(
        dataSourceProperties);
    dynamicDataSource.setDefaultTargetDataSource(defaultDataSource);

    return dynamicDataSource;
  }

  private Map<Object, Object> getDynamicDataSource() {
    Map<String, DataSourceProperties> dataSourcePropertiesMap = properties.getDatasource();
    Map<Object, Object> targetDataSources = new HashMap<>(dataSourcePropertiesMap.size());
    dataSourcePropertiesMap.forEach((k, v) -> {
      DruidDataSource druidDataSource = DynamicDataSourceFactory.buildDruidDataSource(v);
      targetDataSources.put(k, druidDataSource);
    });

    return targetDataSources;
  }

}
