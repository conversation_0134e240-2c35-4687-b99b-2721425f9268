package org.springblade.datasource.properties;

import java.util.LinkedHashMap;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @since 2023/2/18 14:56
 */
@ConfigurationProperties(prefix = "spring.dynamic")
public class DynamicDataSourceProperties {

  private Map<String, DataSourceProperties> datasource = new LinkedHashMap<>();

  public Map<String, DataSourceProperties> getDatasource() {
    return datasource;
  }

  public void setDatasource(Map<String, DataSourceProperties> datasource) {
    this.datasource = datasource;
  }
}
