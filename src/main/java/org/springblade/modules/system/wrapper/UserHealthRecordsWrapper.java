/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.wrapper;

import com.google.common.base.Joiner;
import java.util.List;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserHealthRecordsEntity;
import org.springblade.modules.system.vo.UserHealthRecordsVO;

import java.util.Objects;

/**
 * 健康记录 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public class UserHealthRecordsWrapper extends
    BaseEntityWrapper<UserHealthRecordsEntity, UserHealthRecordsVO> {

  public static UserHealthRecordsWrapper build() {
    return new UserHealthRecordsWrapper();
  }

  @Override
  public UserHealthRecordsVO entityVO(UserHealthRecordsEntity userHealthRecords) {
    UserHealthRecordsVO userHealthRecordsVO = Objects.requireNonNull(
        BeanUtil.copy(userHealthRecords, UserHealthRecordsVO.class));
    User user = UserCache.getUser(userHealthRecords.getUserId());
    if (user != null) {
      userHealthRecordsVO.setUserName(user.getName());
      List<String> deptNames = SysCache.getDeptNames(user.getDeptId());
      if (!deptNames.isEmpty()) {
        userHealthRecordsVO.setDeptName(Joiner.on("/").join(deptNames));
      }
    }
    //User updateUser = UserCache.getUser(userHealthRecords.getUpdateUser());
    //userHealthRecordsVO.setCreateUserName(createUser.getName());
    //userHealthRecordsVO.setUpdateUserName(updateUser.getName());

    return userHealthRecordsVO;
  }


}
