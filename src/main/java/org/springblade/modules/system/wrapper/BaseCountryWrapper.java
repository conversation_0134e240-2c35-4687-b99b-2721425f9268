/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.system.entity.BaseCountryEntity;
import org.springblade.modules.system.vo.BaseCountryVO;
import java.util.Objects;

/**
 * 国家 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-11-01
 */
public class BaseCountryWrapper extends BaseEntityWrapper<BaseCountryEntity, BaseCountryVO>  {

	public static BaseCountryWrapper build() {
		return new BaseCountryWrapper();
 	}

	@Override
	public BaseCountryVO entityVO(BaseCountryEntity baseCountry) {
		BaseCountryVO baseCountryVO = Objects.requireNonNull(BeanUtil.copy(baseCountry, BaseCountryVO.class));

		//User createUser = UserCache.getUser(baseCountry.getCreateUser());
		//User updateUser = UserCache.getUser(baseCountry.getUpdateUser());
		//baseCountryVO.setCreateUserName(createUser.getName());
		//baseCountryVO.setUpdateUserName(updateUser.getName());

		return baseCountryVO;
	}


}
