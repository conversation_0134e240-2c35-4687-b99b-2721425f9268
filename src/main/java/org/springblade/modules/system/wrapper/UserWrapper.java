/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.wrapper;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DictCache;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.enums.DictEnum;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.Tenant;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.vo.ContractItem;
import org.springblade.modules.system.vo.UserVO;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class UserWrapper extends BaseEntityWrapper<User, UserVO> {

  private static final IAttachService attachService;

  static {
    attachService = SpringUtil.getBean(IAttachService.class);
  }

  public static UserWrapper build() {
    return new UserWrapper();
  }

  public UserVO setAttributeForVO(UserVO userVO) {
    Tenant tenant = SysCache.getTenant(userVO.getTenantId());
    List<String> roleName = SysCache.getRoleNames(userVO.getRoleId());
    List<String> deptName = SysCache.getDeptNames(userVO.getDeptId());
    List<String> postName = SysCache.getPostNames(userVO.getPostId());
    userVO.setTenantName(tenant.getTenantName());
    userVO.setRoleName(Func.join(roleName));
    userVO.setDeptName(Func.join(deptName));
    userVO.setPostName(Func.join(postName));
    userVO.setSexName(DictCache.getValue(DictEnum.SEX, userVO.getSex()));
    userVO.setUserTypeName(DictCache.getValue(DictEnum.USER_TYPE, userVO.getUserType()));
    userVO.setPassword(null);
    String licensePlate = userVO.getLicensePlate();
    if (Objects.nonNull(licensePlate)) {
      userVO.setLicensePlate(licensePlate.toUpperCase());
    }
    return userVO;
  }

  @Override
  public UserVO entityVO(User user) {
    UserVO userVO = Objects.requireNonNull(BeanUtil.copy(user, UserVO.class));
    String contract = user.getContract();
    if (!StringUtils.isEmpty(contract)) {
      List<ContractItem> contractItems = JSONArray.parseArray(contract, ContractItem.class);
      contractItems.stream().max((e1, e2) -> e1.getCreateSN().compareTo(e2.getCreateSN()))
          .ifPresent(item -> {
            userVO.setSignedContract(1);
            userVO.setContractSN(item.getCreateSN());
            userVO.setContractStart(item.getContractStart());
            userVO.setContractEnd(item.getContractEnd());
          });
    }
    return setAttributeForVO(userVO);
  }

  public UserVO transferContract(User user, Query query, IAttachService attachService) {
    UserVO userVO = new UserVO();
    userVO.setId(user.getId());
    String contract = user.getContract();
    if (!StringUtils.isEmpty(contract)) {
      userVO.setContract(contract);
      List<ContractItem> contractItems = JSONArray.parseArray(contract, ContractItem.class);
      Collections.reverse(contractItems);
      Integer current = query.getCurrent();
      Integer size = query.getSize();
      List<ContractItem> page = contractItems.stream().skip((long) (current - 1) * size).limit(size)
          .collect(Collectors.toList());
      List<Long> attachIds = page.stream().map(ContractItem::getLinkId)
          .collect(Collectors.toList());
      if (!attachIds.isEmpty()) {
        Map<Long, Attach> attaches = attachService
            .list(Wrappers.<Attach>lambdaQuery().in(Attach::getId, attachIds)).stream()
            .collect(Collectors.toMap(Attach::getId, Function.identity()));
        page.forEach(item -> {
          item.setIdByLink();
          Long linkId = item.getLinkId();
          if (attaches.containsKey(linkId)) {
            Attach attach = attaches.get(linkId);
            item.setOriginalName(attach.getOriginalName());
            item.setCreateUserName(UserCache.getUser(attach.getCreateUser()).getName());
            item.setCreateTime(attach.getCreateTime());
            item.setAttachSize(attach.getAttachSize());
          }
        });
      }
      userVO.setContracts(page);
      userVO.setContractTotal(contractItems.size());
    }
    return userVO;
  }

}
