/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.wrapper;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.WelFlowChartEntity;
import org.springblade.modules.system.vo.WelFlowChartVO;

/**
 * 系统管理-首页流程图配置 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-10-28
 */
public class WelFlowChartWrapper extends BaseEntityWrapper<WelFlowChartEntity, WelFlowChartVO> {

	public static WelFlowChartWrapper build() {
		return new WelFlowChartWrapper();
	}

	@Override
	public WelFlowChartVO entityVO(WelFlowChartEntity niWelFlowChart) {
		WelFlowChartVO welFlowChartVO = Objects.requireNonNull(
			BeanUtil.copy(niWelFlowChart, WelFlowChartVO.class));
		User createUser = UserCache.getUser(niWelFlowChart.getCreateUser());
		welFlowChartVO.setCreateUserName(createUser.getName());
		welFlowChartVO.setCreateDeptName(SysCache.getDeptName(niWelFlowChart.getCreateDept()));
		return welFlowChartVO;
	}

	public List<WelFlowChartVO> treeNodeVO(List<WelFlowChartEntity> list) {
		List<WelFlowChartVO> collect = list.stream().map(this::entityVO)
			.collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

}
