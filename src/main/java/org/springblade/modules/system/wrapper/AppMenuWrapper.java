/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.wrapper;

import org.apache.commons.lang3.StringUtils;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.AppMenu;
import org.springblade.modules.system.vo.AppMenuVO;

import java.util.Objects;

import org.springblade.core.tool.node.ForestNodeMerger;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 小程序菜单 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public class AppMenuWrapper extends BaseEntityWrapper<AppMenu, AppMenuVO> {
	private static final IAttachService attachService;

	static {
		attachService = SpringUtil.getBean(IAttachService.class);
	}

	public static AppMenuWrapper build() {
		return new AppMenuWrapper();
	}

	@Override
	public AppMenuVO entityVO(AppMenu appMenu) {
		AppMenuVO appMenuVO = Objects.requireNonNull(BeanUtil.copy(appMenu, AppMenuVO.class));

		//User createUser = UserCache.getUser(appMenu.getCreateUser());
		//User updateUser = UserCache.getUser(appMenu.getUpdateUser());
		//appMenuVO.setCreateUserName(createUser.getName());
		//appMenuVO.setUpdateUserName(updateUser.getName());
		if (StringUtils.isNotBlank(appMenu.getIcon()) && appMenu.getIcon().matches("[0-9]+")) {
			appMenuVO.setIconUrl(attachService.getLinkById(Long.valueOf(appMenu.getIcon())));
		} else {
			appMenuVO.setIconUrl(appMenu.getIcon());
		}
		return appMenuVO;
	}

	public List<AppMenuVO> treeNodeVO(List<AppMenu> list) {
		List<AppMenuVO> collect = list.stream().map(this::entityVO).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

}
