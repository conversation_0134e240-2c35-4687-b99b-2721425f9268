
package org.springblade.modules.system.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.validation.Valid;

import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.node.TreeNode;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.UserAppMenuEntity;
import org.springblade.modules.system.param.UserAppMenuParams;
import org.springblade.modules.system.service.IUserAppMenuService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.system.entity.AppMenu;
import org.springblade.modules.system.vo.AppMenuVO;
import org.springblade.modules.system.wrapper.AppMenuWrapper;
import org.springblade.modules.system.service.IAppMenuService;
import org.springblade.core.boot.ctrl.BladeController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springblade.core.tool.constant.BladeConstant;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 小程序菜单 控制器
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/app-menu")
@Api(value = "app菜单", tags = "app菜单")
public class AppMenuController extends BladeController {

	private final IAppMenuService appMenuService;

	private final IAttachService attachService;

	private final IUserAppMenuService iUserAppMenuService;

	/**
	 * 小程序菜单 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入appMenu")
	public R<AppMenuVO> detail(AppMenu appMenu) {
		AppMenu detail = appMenuService.getOne(Condition.getQueryWrapper(appMenu));
		return R.data(AppMenuWrapper.build().entityVO(detail));
	}

	/**
	 * 小程序菜单 树列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入notice")
	public R<List<AppMenuVO>> list(AppMenu appMenu, BladeUser bladeUser) {
		QueryWrapper<AppMenu> queryWrapper = Condition.getQueryWrapper(appMenu);
		List<AppMenu> list = appMenuService.list(
			(!bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda()
				.eq(AppMenu::getTenantId, bladeUser.getTenantId()) : queryWrapper);
		return R.data(AppMenuWrapper.build().treeNodeVO(list));
	}

	/**
	 * 小程序菜单 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入appMenu")
	public R<IPage<AppMenuVO>> page(AppMenuVO appMenu, Query query) {
		IPage<AppMenuVO> pages = appMenuService.selectAppMenuPage(Condition.getPage(query), appMenu);
		return R.data(pages);
	}

	/**
	 * 小程序菜单 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入appMenu")
	public R save(@Valid @RequestBody AppMenu appMenu) {
		return R.status(appMenuService.save(appMenu));
	}

	/**
	 * 小程序菜单 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入appMenu")
	public R update(@Valid @RequestBody AppMenu appMenu) {
		return R.status(appMenuService.updateById(appMenu));
	}

	/**
	 * 小程序菜单 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入appMenu")
	public R submit(@Valid @RequestBody AppMenu appMenu) {
		return R.status(appMenuService.saveOrUpdate(appMenu));
	}

	/**
	 * 小程序菜单 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(appMenuService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 小程序菜单 树形结构
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<AppMenuVO>> tree(String tenantId, BladeUser bladeUser) {
		List<AppMenuVO> tree = appMenuService.tree(
			Func.toStrWithEmpty(tenantId, bladeUser.getTenantId()));
		return R.data(tree);
	}

	/**
	 * 获取权限分配树形结构
	 */
	@GetMapping("/grant-tree")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "权限分配树形结构", notes = "权限分配树形结构")
	public R<List<TreeNode>> grantTree(BladeUser user) {
		List<TreeNode> tree = appMenuService.grantTree(user);
		return R.data(tree);
	}

	/**
	 * 获取权限分配树形结构
	 */
	@GetMapping("/role-tree-keys")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "角色所分配的树", notes = "角色所分配的树")
	public R<List<String>> roleTreeKeys(String roleIds) {
		List<String> menus = appMenuService.roleTreeKeys(roleIds);
		return R.data(menus);
	}


	/**
	 * 前端菜单数据
	 */
	@GetMapping("/menus")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "app菜单数据", notes = "app菜单数据")
	public R<List<AppMenuVO>> menus(BladeUser user) {
		List<AppMenuVO> list = appMenuService.menus((user == null) ? null : user.getRoleId());
		return R.data(list);
	}

	@GetMapping("/userAppMenus")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "个人菜单数据", notes = "app菜单数据")
	public R<List<AppMenuVO>> userAppMenu(BladeUser user) {
		List<UserAppMenuEntity> homeList = iUserAppMenuService.getUserAppMenuByUserId(user.getUserId());

		List<AppMenuVO> allMenus = appMenuService.menus(user.getRoleId());

		if (homeList.isEmpty()) {

			List<AppMenuVO> collect = allMenus.stream().limit(8).collect(Collectors.toList());

			return R.data(collect);
		}

		List<AppMenuVO> collect = allMenus.stream()
			.filter(allMenuItem -> {

				Optional<UserAppMenuEntity> first = homeList.stream().filter(homeItem -> Objects.equals(homeItem.getMenuId(), allMenuItem.getId())).findFirst();

				first.ifPresent(userAppMenuEntity -> allMenuItem.setSort(userAppMenuEntity.getSort()));

				return first.isPresent();

			})
			.sorted(Comparator.comparing(AppMenuVO::getSort))
			.collect(Collectors.toList());

		return R.data(collect);
	}


	@PostMapping("/saveUserAppMenu")
	@ApiOperation(value = "保存个人自定菜单", notes = "保存个人自定菜单")
	@Transactional(rollbackFor = Exception.class)
	public R saveUserAppMenu(@Valid @RequestBody UserAppMenuParams userAppMenuParams) {

		Long userId = AuthUtil.getUserId();

		List<UserAppMenuEntity> collect = userAppMenuParams.getUserAppMenuParams().stream().map(item -> {

			UserAppMenuEntity userAppMenuEntity = new UserAppMenuEntity();

			userAppMenuEntity.setMenuId(item.getId());

			userAppMenuEntity.setSort(item.getSort());

			return userAppMenuEntity;

		}).collect(Collectors.toList());

		appMenuService.saveUserAppMenu(collect, userId);

		return R.success("保存成功");
	}
}
