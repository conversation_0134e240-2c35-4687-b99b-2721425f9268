/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;

import static org.springblade.core.cache.constant.CacheConstant.SYS_CACHE;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DictCache;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.enums.DictEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.constant.AuthConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.Post;
import org.springblade.modules.system.entity.Role;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserDept;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IPostService;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.service.IUserDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.vo.DeptAndUserVO;
import org.springblade.modules.system.vo.DeptVO;
import org.springblade.modules.system.vo.UserVO;
import org.springblade.modules.system.wrapper.DeptWrapper;
import org.springblade.modules.system.wrapper.UserWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@Slf4j
@NonDS
@RestController
@RequiredArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/dept")
@Api(value = "部门", tags = "部门")
public class DeptController extends BladeController {

	private final IDeptService deptService;
	private final IUserService userService;
	private final IUserDeptService userDeptService;

	private final IPostService postService;
	private final IRoleService roleService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入dept")
	public R<DeptVO> detail(Dept dept) {
		Dept detail = deptService.getOne(Condition.getQueryWrapper(dept));
		return R.data(DeptWrapper.build().entityVO(detail));
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入dept")
	public R<List<DeptVO>> list(@ApiIgnore @RequestParam Map<String, Object> dept,
								BladeUser bladeUser) {
		QueryWrapper<Dept> queryWrapper = Condition.getQueryWrapper(dept, Dept.class);
		List<Dept> list = deptService.list(
			(!bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda()
				.eq(Dept::getTenantId, bladeUser.getTenantId()) : queryWrapper);
		return R.data(DeptWrapper.build().listNodeVO(list));
	}

	/**
	 * 懒加载列表
	 */
	@GetMapping("/lazy-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "传入dept")
	public R<List<DeptVO>> lazyList(@ApiIgnore @RequestParam Map<String, Object> dept,
									Long parentId,
									BladeUser bladeUser) {
		List<DeptVO> list = deptService.lazyList(bladeUser.getTenantId(), parentId, dept);
		return R.data(DeptWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * 懒加载列表
	 */
	@GetMapping("/lazy-list-with-user-num")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "传入dept")
	public R<List<DeptVO>> lazyListWithUserNum(@ApiIgnore @RequestParam Map<String, Object> dept,
											   Long parentId,
											   BladeUser bladeUser) {
		List<DeptVO> list = deptService.lazyList(bladeUser.getTenantId(), parentId, dept);
		list.forEach(deptVO -> {
			List<Long> deptIds = SysCache.getDeptChildIds(deptVO.getId());
			Future<Long> num = userService.countByDeptIds(deptIds);
			try {
				deptVO.setUserNum(num.get());
			} catch (Exception e) {
				log.error("获取用户数量失败!", e);
			}
		});
		return R.data(DeptWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * 获取部门树形结构
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<DeptVO>> tree(String tenantId, BladeUser bladeUser,
								@RequestParam(required = false) String deptCategory,
								@RequestParam(required = false) String code,
								@RequestParam(required = false) String parentCode) {
		List<DeptVO> tree = deptService.tree(
			Func.toStrWithEmpty(tenantId, bladeUser.getTenantId()), deptCategory, code, parentCode,
			null);
		return R.data(tree);
	}

	/**
	 * 懒加载获取部门树形结构
	 */
	@GetMapping("/lazy-tree")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "懒加载树形结构", notes = "树形结构")
	public R<List<DeptVO>> lazyTree(String tenantId, Long parentId, BladeUser bladeUser) {
		List<DeptVO> tree = deptService.lazyTree(
			Func.toStrWithEmpty(tenantId, bladeUser.getTenantId()),
			parentId);
		return R.data(tree);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入dept")
	public R submit(@Valid @RequestBody Dept dept) {
		if (deptService.submit(dept)) {
			CacheUtil.clear(SYS_CACHE);
			// 返回懒加载树更新节点所需字段
			Kv kv = Kv.create().set("id", String.valueOf(dept.getId()))
				.set("tenantId", dept.getTenantId())
				.set("deptCategoryName",
					DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory()));
			return R.data(kv);
		}
		return R.fail("操作失败");
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(deptService.removeDept(ids));
	}

	/**
	 * 下拉数据源
	 */
	@PreAuth(AuthConstant.PERMIT_ALL)
	@GetMapping("/select")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "下拉数据源", notes = "传入id集合")
	public R<List<Dept>> select(Long userId, String deptId) {
		if (Func.isNotEmpty(userId)) {
			User user = UserCache.getUser(userId);
			deptId = user.getDeptId();
		}
		List<Dept> list = deptService.list(
			Wrappers.<Dept>lambdaQuery().in(Dept::getId, Func.toLongList(deptId)));
		return R.data(list);
	}


	@GetMapping("/post/select")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "指定部门下的岗位下拉数据源", notes = "")
	public R<List<Post>> selectPost(String deptIds) {
		List<UserDept> userDeptList = userDeptService.list(
			Wrappers.<UserDept>lambdaQuery().in(UserDept::getDeptId, deptIds.split(",")));
		List<Long> postIds = new ArrayList<>();
		if (userDeptList != null && !userDeptList.isEmpty()) {
			List<User> users = userService.listByIds(
				userDeptList.stream().map(UserDept::getUserId).collect(Collectors.toList()));
			List<String> postIdss = users.stream().map(User::getPostId)
				.collect(Collectors.toList());
			postIdss.forEach(postId -> {
				postIds.addAll(Func.toLongList(",", postId));
			});
		}
		return R.data(postService.list(
			Wrappers.<Post>lambdaQuery().in(!postIds.isEmpty(), Post::getId, postIds)));
	}

	@GetMapping("/role/select")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "指定部门下的角色下拉数据源", notes = "")
	public R<List<Role>> selectRole(String deptIds) {
		List<UserDept> userDeptList = userDeptService.list(
			Wrappers.<UserDept>lambdaQuery().in(UserDept::getDeptId, deptIds.split(",")));
		List<Long> roleIds = new ArrayList<>();
		if (userDeptList != null && !userDeptList.isEmpty()) {
			List<User> users = userService.listByIds(
				userDeptList.stream().map(UserDept::getUserId).collect(Collectors.toList()));
			roleIds.addAll(users.stream()
				.flatMap(item -> Stream.of(item.getRoleId().split(",")))
				.filter(StringUtils::isNumeric).distinct().map(Long::valueOf)
				.collect(Collectors.toList()));
		}
		return R.data(roleService.list(
			Wrappers.<Role>lambdaQuery().in(!roleIds.isEmpty(), Role::getId, roleIds)));
	}


	/**
	 * 获取部门员工组成的树形结构数据
	 *
	 * @return 树形结构数据
	 */
	@GetMapping("/getTreeDeptAndUser")
	public R<List<DeptAndUserVO>> getTreeDeptAndUser() {
		List<DeptAndUserVO> deptAndUser = deptService.getDeptAndUser();

		return R.data(deptAndUser);
	}

	@GetMapping("/childrenByCode")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "传入dept")
	public R<List<DeptVO>> children(@RequestParam String code) {
		String deptIds = SysCache.getDeptIdsByCodes(BladeConstant.ADMIN_TENANT_ID, code);
		List<Dept> list = Func.toLongList(deptIds).stream().flatMap(deptId -> deptService.getDeptChild(deptId).stream()).collect(Collectors.toList());
		return R.data(DeptWrapper.build().listNodeVO(list));
	}


	/**
	 * 获取当前负责人id，如果是小组，则获取上一级部门的负责人的id
	 *
	 * @return 当前部门主管id
	 */
	@GetMapping("/getDeptLeaderId")
	public R<Long> getDeptLeaderId() {

		String deptId = AuthUtil.getDeptId();

		Dept dept = deptService.getById(deptId);

		// 判断是否为小组
		if (Objects.equals(4, dept.getDeptCategory())) {
			dept = deptService.getById(dept.getParentId());
		}

		//如果是总经办，就直接返回给当前用户的id
		if (Objects.equals("总经办", dept.getDeptName())) {
			return R.data(AuthUtil.getUserId());
		}

		return R.data(dept.getLeaderId());
	}

	/**
	 * 用于查询下拉框
	 * @return
	 */
	@GetMapping("options")
	public R<List<Dept>> selectOption(){
		return R.data(deptService.getAllDeptList());
	}
}
