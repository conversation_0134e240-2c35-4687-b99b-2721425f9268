/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;


import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.google.common.collect.ImmutableMap;
import com.natergy.ni.pa.event.UserLeaveEvent;
import io.jsonwebtoken.lang.Assert;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.*;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.excel.UserExcel;
import org.springblade.modules.system.excel.UserImporter;
import org.springblade.modules.system.service.IUserDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.service.UserSecPwdService;
import org.springblade.modules.system.service.impl.UserDeptServiceImpl;
import org.springblade.modules.system.vo.UserVO;
import org.springblade.modules.system.wrapper.UserWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.core.cache.constant.CacheConstant.USER_CACHE;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@RequestMapping(AppConstant.APPLICATION_USER_NAME)
@RequiredArgsConstructor
@Api(value = "用户", tags = "用户")
public class UserController {

	public static final String MODULE = "blade_user";
	private final IAttachService attachService;

	private final IUserService userService;
	private final IUserDeptService userDeptService;
	private final UserSecPwdService userSecPwdService;

	private final BladeRedis bladeRedis;

	private final ApplicationEventPublisher eventPublisher;

	/**
	 * 查询单条
	 */
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查看详情", notes = "传入id")
	@GetMapping("/detail")
	public R<UserVO> detail(User user) {
		User detail = userService.getOne(Condition.getQueryWrapper(user));
		if (detail == null) {
			return R.data(new UserVO());
		}
		UserVO userVO = UserWrapper.build().entityVO(detail);
		try {
			if (StringUtils.isNotBlank(detail.getAvatar()) && detail.getAvatar()
				.matches("[0-9]+")) {
				userVO.setAvatarUrl(attachService.getLinkById(Long.valueOf(detail.getAvatar())));
			} else {
				userVO.setAvatarUrl(detail.getAvatar());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		userVO.setTopDeptName(SysCache.getTopDeptName(userVO.getDeptId()));
		userVO.setTopDeptId(SysCache.getTopDeptId(userVO.getDeptId()));
		List<Attach> attaches = attachService.listByBusiness(MODULE, String.valueOf(user.getId()));

		if (Objects.nonNull(attaches)) {
			userVO.setAttachment(attaches.stream().map(attach ->
					ImmutableMap.<String, Object>builder().put("label", attach.getOriginalName())
						.put("value", String.valueOf(attach.getId())).build())
				.collect(Collectors.toList()));
		}

		return R.data(userVO);
	}

	/**
	 * 查询单条
	 */
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查看详情", notes = "传入id")
	@GetMapping("/info")
	public R<UserVO> info(BladeUser user) {
		User detail = userService.getById(user.getUserId());
		UserVO res = UserWrapper.build().entityVO(detail);
		UserVO userVO = UserWrapper.build().entityVO(detail);
		if (StringUtils.isNotBlank(detail.getAvatar()) && detail.getAvatar().matches("[0-9]+")) {
			userVO.setAvatarUrl(attachService.getLinkById(Long.valueOf(detail.getAvatar())));
		} else {
			userVO.setAvatarUrl(detail.getAvatar());
		}
		res.setAvatar(res.getAvatarUrl());
		res.setTopDeptName(SysCache.getTopDeptName(user.getDeptId()));
		return R.data(res);
	}

	/**
	 * 用户列表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "account", value = "账号名", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "realName", value = "姓名", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入account和realName")
	public R<IPage<UserVO>> list(@ApiIgnore @RequestParam Map<String, Object> user, Query query,
		BladeUser bladeUser) {
		QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user, User.class);
		IPage<User> pages = userService.page(Condition.getPage(query),
			(!bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda()
				.eq(User::getTenantId, bladeUser.getTenantId()) : queryWrapper);

		IPage<UserVO> res = UserWrapper.build().pageVO(pages);
		List<Long> avatarIds = res.getRecords().stream().map(User::getAvatar)
			.filter(avatar -> StringUtils.isNotBlank(avatar) && avatar.matches("[0-9]+"))
			.map(Long::valueOf).collect(
				Collectors.toList());
		List<Attach> attaches = attachService.listByIds(avatarIds);
		Map<Long, String> attachMap = attaches.stream()
			.collect(Collectors.toMap(Attach::getId, Attach::getLink));
		res.getRecords().forEach(item -> {
			if (StringUtils.isNotBlank(item.getAvatar()) && item.getAvatar().matches("[0-9]+")) {
				item.setAvatarUrl(attachMap.get(Long.valueOf(item.getAvatar())));
			} else {
				item.setAvatarUrl(item.getAvatar());
			}
		});
		return R.data(res);
	}

	/**
	 * 自定义用户列表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "account", value = "账号名", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "realName", value = "姓名", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入account和realName")
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R<IPage<UserVO>> page(@ApiIgnore UserVO user, Query query, Long deptId,
		BladeUser bladeUser) {
		IPage<User> pages = userService.selectUserPageByVO(Condition.getPage(query), user, deptId,
			(bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID) ? StringPool.EMPTY
				: bladeUser.getTenantId()));
		IPage<UserVO> res = UserWrapper.build().pageVO(pages);
		List<Long> avatarIds = res.getRecords().stream().map(User::getAvatar)
			.filter(avatar -> StringUtils.isNotBlank(avatar) && avatar.matches("[0-9]+"))
			.map(Long::valueOf).collect(
				Collectors.toList());
		if (!avatarIds.isEmpty()) {
			List<Attach> attaches = attachService.listByIds(avatarIds);
			Map<Long, String> attachMap = attaches.stream()
				.collect(Collectors.toMap(Attach::getId, Attach::getLink));
			res.getRecords().forEach(item -> {
				if (StringUtils.isNotBlank(item.getAvatar()) && item.getAvatar()
					.matches("[0-9]+")) {
					item.setAvatarUrl(attachMap.get(Long.valueOf(item.getAvatar())));
				} else {
					item.setAvatarUrl(item.getAvatar());
				}
			});
		}
		//todo 数据清洗

//	  res.getRecords().forEach(item->{
//		  if (item.getIdCard().length()>6) {
//			  String provinceCode = item.getIdCard().substring(0,2);
//			  String cityCode = item.getIdCard().substring(2,4);
//			  String districtCode = item.getIdCard().substring(4,6);
//			  System.out.println(item.getRealName()+": "+provinceCode+","+provinceCode+cityCode+","+provinceCode+cityCode+districtCode);
//			  item.setRegionCode(Arrays.asList(provinceCode,provinceCode+cityCode,provinceCode+cityCode+districtCode));
//		  }
//		  if (item.getHouseholdRegistration() != null) {
//			  item.setRegionDetail(item.getHouseholdRegistration());
//		  }
//		  userService.saveOrUpdate(item);
//	  });
		return R.data(res);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增或修改", notes = "传入User")
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R submit(@Valid @RequestBody UserVO user) {
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		userService.relateUserInfo(user);
		if (StringUtils.isBlank(user.getPassword())) {
			// 获取默认密码配置
			user.setPassword(DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD));
		}
		user.setContract(
			JSONArray.toJSONString(
				Collections.singletonList(userService.iniContract(user, attachService)))
		);
		boolean res = userService.submit(user);
		String contractName = user.getContractName();
		if (!StringUtils.isEmpty(contractName)) {
			attachService.update(
				Wrappers.<Attach>lambdaUpdate().eq(Attach::getName, contractName)
					.set(Attach::getBusinessKey, user.getId())
			);
		}
		return R.status(res);
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入User")
	public R update(@Valid @RequestBody UserVO user) {
		userService.relateUserInfo(user);
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		userService.setVoContractJson(user, attachService);
		boolean res = userService.updateUser(user);
		if (res) {
			if (user.getStatus().equals(User.STATUS_QUIT)) {
				LocalDateTime leaveTime =
					user.getLeaveTime() != null ? user.getLeaveTime() : LocalDateTime.now();
				eventPublisher.publishEvent(new UserLeaveEvent(user.getId(), leaveTime));
			}
		}
		return R.status(res);
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "删除", notes = "传入id集合")
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R remove(@RequestParam String ids) {
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		return R.status(userService.removeUser(ids));
	}

	/**
	 * 设置菜单权限
	 */
	@PostMapping("/grant")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "权限设置", notes = "传入roleId集合以及menuId集合")
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R grant(@ApiParam(value = "userId集合", required = true) @RequestParam String userIds,
		@ApiParam(value = "roleId集合", required = true) @RequestParam String roleIds) {
		boolean temp = userService.grant(userIds, roleIds);
		return R.status(temp);
	}

	@PostMapping("/grantPlus")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "权限设置(累加)", notes = "传入roleId集合以及menuId集合")
	public R grantPlus(@ApiParam(value = "userId集合", required = true) @RequestParam String userIds,
		@ApiParam(value = "roleId集合", required = true) @RequestParam String roleIds) {
		boolean temp = userService.grantPlus(Func.toLongList(userIds), Func.toLongList(roleIds));
		return R.status(temp);
	}
	/**
	 * 重置密码
	 */
	@PostMapping("/reset-password")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "初始化密码", notes = "传入userId集合")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R resetPassword(
		@ApiParam(value = "userId集合", required = true) @RequestParam String userIds) {
		boolean temp = userService.resetPassword(userIds);
		return R.status(temp);
	}

	/**
	 * 修改密码
	 */
	@PostMapping("/update-password")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "修改密码", notes = "传入密码")
	public R updatePassword(BladeUser user,
		@ApiParam(value = "旧密码", required = true) @RequestParam String oldPassword,
		@ApiParam(value = "新密码", required = true) @RequestParam String newPassword,
		@ApiParam(value = "新密码", required = true) @RequestParam String newPassword1) {
		boolean temp = userService.updatePassword(user.getUserId(), oldPassword, newPassword,
			newPassword1);
		return R.status(temp);
	}

	/**
	 * 修改基本信息
	 */
	@PostMapping("/update-info")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "修改基本信息", notes = "传入User")
	public R updateInfo(@Valid @RequestBody User user) {
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		return R.status(userService.updateUserInfo(user));
	}

	/**
	 * 用户列表
	 */
	@GetMapping("/user-list")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "用户列表", notes = "传入user")
	public R<List<User>> userList(UserVO user, @RequestParam(required = false) String deptIds,
		@RequestParam(required = false) String deptCodes,
		BladeUser bladeUser) {
		if (StringUtils.isBlank(deptIds) && StringUtils.isNotBlank(deptCodes)) {
			deptIds = SysCache.getDeptIdsByCodes(AuthUtil.getTenantId(), deptCodes);
		}
		List<User> list = userService.selectUserListByVO(user, deptIds,
			(bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID) ? StringPool.EMPTY
				: bladeUser.getTenantId()));
		return R.data(list);
	}

	/**
	 * 导入用户
	 */
	@PostMapping("import-user")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "导入用户", notes = "传入excel")
	public R importUser(MultipartFile file, Integer isCovered) {
		UserImporter userImporter = new UserImporter(userService, isCovered == 1);
		ExcelUtil.save(file, userImporter, UserExcel.class);
		return R.success("操作成功");
	}

	/**
	 * 导出用户
	 */
	@GetMapping("export-user")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "导出用户", notes = "传入user")
	public void exportUser(@ApiIgnore @RequestParam Map<String, Object> user, BladeUser bladeUser,
		HttpServletResponse response) {
		QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user, User.class);
		if (!AuthUtil.isAdministrator()) {
			queryWrapper.lambda().eq(User::getTenantId, bladeUser.getTenantId());
		}
		queryWrapper.lambda().eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<UserExcel> list = userService.exportUser(queryWrapper);
		ExcelUtil.export(response, "用户数据" + DateUtil.time(), "用户数据表", list,
			UserExcel.class);
	}

	/**
	 * 导出模板
	 */
	@GetMapping("export-template")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导出模板")
	public void exportUser(HttpServletResponse response) {
		List<UserExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "用户数据模板", "用户数据表", list, UserExcel.class);
	}


	/**
	 * 第三方注册用户
	 */
	@PostMapping("/register-guest")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "第三方注册用户", notes = "传入user")
	public R registerGuest(User user, Long oauthId) {
		return R.status(userService.registerGuest(user, oauthId));
	}

	/**
	 * 配置用户平台信息
	 */
	@PostMapping("/update-platform")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "配置用户平台信息", notes = "传入user")
	public R updatePlatform(Long userId, Integer userType, String userExt) {
		return R.status(userService.updatePlatform(userId, userType, userExt));
	}

	/**
	 * 查看平台详情
	 */
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "查看平台详情", notes = "传入id")
	@GetMapping("/platform-detail")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R<UserVO> platformDetail(User user) {
		return R.data(userService.platformDetail(user));
	}


	/**
	 * 用户列表查询
	 */
	@ApiImplicitParams({
		@ApiImplicitParam(name = "name", value = "人员姓名", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "postName", value = "职位名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "current", value = "当前页数", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "size", value = "每页数量", paramType = "query", dataType = "int")
	})
	@ApiOperationSupport(order = 18)
	@ApiOperation(value = "用户列表查询", notes = "用户列表查询")
	@GetMapping("/search/user")
	public R<IPage<UserVO>> userSearch(@ApiIgnore UserVO user, @ApiIgnore Query query) {
		return R.data(userService.selectUserSearch(user, query));
	}

	/**
	 * 用户列表查询
	 */
	@ApiImplicitParams({
		@ApiImplicitParam(name = "name", value = "人员姓名", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "current", value = "当前页数", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "size", value = "每页数量", paramType = "query", dataType = "int")
	})
	@ApiOperationSupport(order = 18)
	@ApiOperation(value = "在职用户列表查询", notes = "在职用户列表查询")
	@GetMapping("/search/otj/user")
	public R<IPage<UserVO>> otjUserSearch(@ApiIgnore @RequestParam String name,
		Integer status, @ApiIgnore Query query) {
		return R.data(userService.selectUserSearch(name, status, query));
	}

	/**
	 * 用户解锁
	 */
	@PostMapping("/unlock")
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "账号解锁", notes = "传入id")
	public R unlock(String userIds) {
		if (StringUtil.isBlank(userIds)) {
			return R.fail("请至少选择一个用户");
		}
		List<User> userList = userService.list(
			Wrappers.<User>lambdaQuery().in(User::getId, Func.toLongList(userIds)));
		userList.forEach(user -> bladeRedis.del(
			CacheNames.tenantKey(user.getTenantId(), CacheNames.USER_FAIL_KEY, user.getAccount())));
		return R.success("操作成功");
	}

	/**
	 * 用户列表
	 *
	 * @param user
	 * @param bladeUser
	 * @return
	 */
	@GetMapping("/user-vo-list")
	@ApiOperationSupport(order = 20)
	@ApiOperation(value = "用户列表", notes = "传入user")
	public R<List<UserVO>> getUserList(@ApiIgnore @RequestParam Map<String, Object> user,
		BladeUser bladeUser) {
		String key = (String) user.get("key");
		user.remove("key");
		QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user, User.class);
		if (!AuthUtil.isAdministrator()) {
			queryWrapper.lambda().eq(User::getTenantId, bladeUser.getTenantId());
		}
		if (StringUtils.isNotBlank(key)) {
			queryWrapper.lambda().and(w -> w.like(User::getName, key).or()
				.like(User::getAccount, key).or()
				.like(User::getRealName, key));
		}
		queryWrapper.lambda().eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<User> list = userService.list(queryWrapper);
		if (list == null || list.isEmpty()) {
			return R.data(new ArrayList<>());
		}
		List<UserVO> res = UserWrapper.build().listVO(list);
		List<Long> avatarIds = list.stream().map(User::getAvatar)
			.filter(avatar -> StringUtils.isNotBlank(avatar) && avatar.matches("[0-9]+"))
			.map(Long::valueOf).collect(
				Collectors.toList());
		if (!avatarIds.isEmpty()) {
			List<Attach> attaches = attachService.listByIds(avatarIds);
			Map<Long, String> attachMap = attaches.stream()
				.collect(Collectors.toMap(Attach::getId, Attach::getLink));
			res.forEach(item -> {
				if (StringUtils.isNotBlank(item.getAvatar()) && item.getAvatar()
					.matches("[0-9]+")) {
					item.setAvatarUrl(attachMap.get(Long.valueOf(item.getAvatar())));
				} else {
					item.setAvatarUrl(item.getAvatar());
				}

			});
		}
		return R.data(res);
	}

	/**
	 * 用户列表（快速）
	 *
	 * @param user
	 * @return
	 */
	@GetMapping("/get-user-info")
	@ApiOperationSupport(order = 20)
	@ApiOperation(value = "用户列表", notes = "传入user")
	public R<List<User>> getUserInfo(@ApiIgnore @RequestParam Map<String, Object> user,
		BladeUser bladeUser) {
		QueryWrapper<User> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().select(User::getId, User::getRealName, User::getDeptId)
			.eq(User::getStatus, User.STATUS_ON)
			.eq(!AuthUtil.isAdministrator(), User::getTenantId, bladeUser.getTenantId())
			.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<User> list = userService.list(queryWrapper);
		return R.data(list);
	}

	/**
	 * 重号检查
	 *
	 * @return
	 */
	@GetMapping("/search-same-phone")
	@ApiOperationSupport(order = 21)
	@ApiOperation(value = "重号检查", notes = "传入user")
	public R<String> searchSamePhone(User user) {
		return R.data(userService.searchSamePhone(user));
	}


	/**
	 * 用户基本信息
	 *
	 * @return
	 */
	@GetMapping("/user-base-info")
	@ApiOperationSupport(order = 22)
	@ApiOperation(value = "用户基本信息", notes = "传入tenantId")
	public R<IPage<UserVO>> userBaseInfoPage(UserVO user, Query query) {
		return R.data(UserWrapper.build()
			.pageVO(userService.userBaseInfoPage(query, user)));
	}

	/**
	 * 用户基本信息
	 *
	 * @return
	 */
	@GetMapping("/address-list")
	@ApiOperationSupport(order = 23)
	@ApiOperation(value = "通讯录")
	public R<List<Map<String, Object>>> addressList() {
		return R.data(userService.getAddressList());
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/updateAvatar")
	@ApiOperationSupport(order = 25)
	@ApiOperation(value = "上传用户头像", notes = "传入User")
	public R updateAvatar(@Valid @RequestParam Long id, @RequestParam String avatar) {
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		boolean res = userService
			.update(
				Wrappers.lambdaUpdate(User.class).eq(User::getId, id).set(User::getAvatar, avatar));
		return R.status(res);
	}

	/**
	 * 修改二级密码
	 */
	@PostMapping("/changeSecPwd")
	@ApiOperationSupport(order = 26)
	@ApiOperation(value = "修改二级密码", notes = "")
	public R changeSecPwd(
		@ApiParam(value = "新密码", required = true) @RequestParam String newPassword,
		@ApiParam(value = "新密码", required = true) @RequestParam String newPassword1,
		BladeUser user) {
		boolean res = userSecPwdService.updatePassword(user.getUserId(), newPassword, newPassword1);
		return R.status(res);
	}

	@GetMapping("/export-qr")
	@ApiOperationSupport(order = 27)
	@ApiOperation(value = "导出二维码", notes = "传入user")
	public void exportQr(@ApiIgnore @RequestParam Map<String, Object> user, BladeUser bladeUser,
		HttpServletResponse response) throws UnsupportedEncodingException {
		String ids = (String) user.get("ids");
		user.remove("ids");
		Long deptId = null;
		if (user.get("deptId") != null) {
			deptId = Func.toLong(user.get("deptId"));
			user.remove("deptId");
		}
		QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user, User.class);
		queryWrapper.lambda().in(StringUtils.isNotBlank(ids), User::getId, Func.toLongList(ids));
		if (!AuthUtil.isAdministrator()) {
			queryWrapper.lambda().eq(User::getTenantId, bladeUser.getTenantId());
		}
		queryWrapper.lambda().eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<Long> deptIds=null;
		if (deptId != null) {
			 deptIds = SysCache.getDeptChildIds(deptId);

		}
		userService.generateQRImage(queryWrapper,deptIds, response);
	}

	@GetMapping("/user-contract/{id}")
	@ApiOperationSupport(order = 28)
	@ApiOperation(value = "用户合同查询", notes = "传入userId")
	public UserVO getUserContract(@PathVariable("id") Long userId, Query query) {
		User user = UserCache.getUser(userId);
		return UserWrapper.build().transferContract(user, query, attachService);
	}

	@PostMapping("/add-user-contract")
	@ApiOperationSupport(order = 29)
	@ApiOperation(value = "用户合同", notes = "传入userId")
	public R addContract(@RequestBody UserVO userVO) {
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		if (userService.addContract(userVO, attachService)) {
			return R.success("操作成功");
		}
		return R.success("操作失败");
	}

	@PostMapping("/update-user-contract")
	@ApiOperationSupport(order = 30)
	@ApiOperation(value = "用户合同", notes = "传入userId")
	public R updateContract(@RequestBody UserVO userVO) {
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		if (userService.updateContract(userVO, attachService)) {
			return R.success("操作成功");
		}
		return R.success("操作失败");
	}

	@PostMapping("/remove-user-contract")
	@ApiOperationSupport(order = 30)
	@ApiOperation(value = "用户合同", notes = "传入userId")
	public R removeContract(Long userId, @RequestParam(required = false) String ids, String sns) {
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		if (userService.removeContract(userId, ids, sns, attachService)) {
			return R.success("操作成功");
		}
		return R.success("操作失败");
	}

	@PostMapping("/change-user-contract")
	@ApiOperationSupport(order = 31)
	@ApiOperation(value = "用户合同", notes = "传入userId")
	public R changeContract(Long userId, String sn) {
		Assert.notNull(sn, "sn不能为null");
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(USER_CACHE, false);
		if (userService.changeContract(userId, sn)) {
			return R.success("操作成功");
		}
		return R.success("操作失败");
	}


	@GetMapping("/getUserSubscribeStatus")
	@ApiOperation(value = "查询用户的订阅状态")
	public R getUserSubscribeStatus() {
		Integer userSubscribeStatus = userService.getUserSubscribeStatusById(AuthUtil.getUserId());
		return R.data(Objects.isNull(userSubscribeStatus));
	}


	@PostMapping("/checkin")
	@ApiOperation(value = "用户签到")
	public R<String> checkin() {
		Long userId = AuthUtil.getUserId();

		User user = userService.getById(userId);

		if (Objects.isNull(user.getCheckinDatetime())) {
			user.setCheckinDatetime(new Date());
			userService.updateById(user);
		}

		return R.success("签到成功");
	}

	@PostMapping("/markInProvince")
	@ApiOperation(value = "用户签到")
	public R markInProvince(@RequestParam List<Long> ids, Boolean inProvince) {
		return R.data(userService.markInProvince(ids, inProvince));
	}

	@PostMapping("/changeYY")
	@ApiOperation(value = "标记用户是否演绎员工")
	public R changeYY(@RequestParam String ids, Boolean yy) {
		return R.data(userService.update(
			Wrappers.<User>lambdaUpdate().in(User::getId, Func.toLongList(ids))
				.set(User::getYy, yy)));
	}
}
