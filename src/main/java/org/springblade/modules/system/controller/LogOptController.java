/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.LogOpt;
import org.springblade.modules.system.service.LogOptService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_LOG_NAME + "/opt")
public class LogOptController {

	private final LogOptService logService;

	/**
	 * 查询单条
	 */
	@GetMapping("/detail")
	public R<LogOpt> detail(LogOpt log) {
		return R.data(logService.getOne(Condition.getQueryWrapper(log)));
	}

	/**
	 * 查询多条(分页)
	 */
	@GetMapping("/list")
	public R<IPage<LogOpt>> list(@ApiIgnore @RequestParam Map<String, Object> log, Query query) {
		String startTime = (String) log.get("startTime");
		String endTime = (String) log.get("endTime");
		String businessId = (String) log.get("businessId");
		String module= (String) log.get("module");
		log.remove("startTime");
		log.remove("endTime");
		log.remove("businessId");
		log.remove("module");
		QueryWrapper<LogOpt> wrapper = Condition.getQueryWrapper(log, LogOpt.class);
		if (StringUtils.isNotBlank(startTime)) {
			wrapper.lambda()
				.ge(LogOpt::getCreateTime, DateUtil.parse(startTime, "yyyy-MM-dd HH:mm:ss"));
		}
		if (StringUtils.isNotBlank(endTime)) {
			wrapper.lambda()
				.le(LogOpt::getCreateTime, DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss"));
		}
		if (StringUtils.isNotBlank(businessId)) {
			wrapper.in("value", Func.toStrList(businessId));
		}
    if (StringUtils.isNotBlank(module)) {
      wrapper.in("module", Func.toStrList(module));
    }
		IPage<LogOpt> pages = logService.selectLogPage(
			Condition.getPage(query.setDescs("create_time")),
			wrapper);
		return R.data(pages);
	}

}
