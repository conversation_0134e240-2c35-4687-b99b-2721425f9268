/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.cache.constant.CacheConstant;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.VersionEntity;
import org.springblade.modules.system.service.IVersionReadService;
import org.springblade.modules.system.service.IVersionService;
import org.springframework.boot.info.GitProperties;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统管理-版本管理 控制器
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/version")
@Api(value = "系统管理-版本管理", tags = "系统管理-版本管理接口")
public class VersionController extends BladeController {

  private final IVersionService versionService;

  private final IVersionReadService readService;
  private final BladeRedis bladeRedis;

  @GetMapping("/currentVersion")
  @ApiOperation(value = "获取当前版本号", notes = "")
  public R<VersionEntity> currentVersion(@RequestParam(defaultValue = "1") String type,
      BladeUser user) {
    VersionEntity version = SysCache.getCurrentVersion(type);
    if (version != null) {
      version.setRead(readService.isRead(version.getId(), user.getUserId()));
    }
    return R.data(version);
  }

  /**
   * 系统管理-版本管理 详情
   */
  @GetMapping("/detail")
  @ApiOperationSupport(order = 1)
  @ApiOperation(value = "详情", notes = "传入niVersion")
  public R<VersionEntity> detail(VersionEntity niVersion) {
    VersionEntity detail = versionService.getOne(Condition.getQueryWrapper(niVersion));
    return R.data(detail);
  }

  /**
   * 系统管理-版本管理 分页
   */
  @GetMapping("/list")
  @ApiOperationSupport(order = 2)
  @ApiOperation(value = "分页", notes = "传入niVersion")
  public R<IPage<VersionEntity>> list(VersionEntity niVersion, Query query) {
    IPage<VersionEntity> pages = versionService.page(Condition.getPage(query),
        Condition.getQueryWrapper(niVersion));
    VersionEntity current = versionService.getCurrentVersion(VersionEntity.TYPE_PC);
    pages.getRecords().forEach(item -> {
      User user = UserCache.getUser(item.getCreateUser());
      item.setCreateUserName(user.getRealName());
      if (current != null && current.getId() != null && current.getId().equals(item.getId())) {
        item.setCurrent(true);
      }
    });
    return R.data(pages);
  }

  /**
   * 系统管理-版本管理 新增
   */
  @PostMapping("/save")
  @ApiOperationSupport(order = 4)
  @ApiOperation(value = "新增", notes = "传入niVersion")
  public R save(@Valid @RequestBody VersionEntity niVersion) {
    return R.status(versionService.save(niVersion));
  }

  /**
   * 系统管理-版本管理 修改
   */
  @PostMapping("/update")
  @ApiOperationSupport(order = 5)
  @ApiOperation(value = "修改", notes = "传入niVersion")
  public R update(@Valid @RequestBody VersionEntity niVersion) {
    return R.status(versionService.updateById(niVersion));
  }

  /**
   * 系统管理-版本管理 新增或修改
   */
  @PostMapping("/submit")
  @ApiOperationSupport(order = 6)
  @ApiOperation(value = "新增或修改", notes = "传入niVersion")
  public R submit(@Valid @RequestBody VersionEntity niVersion) {
    CacheUtil.evict(CacheConstant.SYS_CACHE, SysCache.VERSION_CURRENT, niVersion.getType(), true);
    return R.status(versionService.saveOrUpdate(niVersion));
  }

  /**
   * 系统管理-版本管理 发布
   */
  @PostMapping("/release")
  @ApiOperationSupport(order = 7)
  @ApiOperation(value = "发布", notes = "传入ids")
  public R release(@ApiParam(value = "主键集合", required = true) @RequestParam Long id) {
    VersionEntity version = versionService.getById(id);
    Assert.isTrue(version != null, "版本查询失败");
    CacheUtil.evict(CacheConstant.SYS_CACHE, SysCache.VERSION_CURRENT, version.getType(), true);
    return R.status(versionService.releaseById(id));
  }

  /**
   * 系统管理-版本管理 删除
   */
  @PostMapping("/releaseBack")
  @ApiOperationSupport(order = 7)
  @ApiOperation(value = "撤销发布", notes = "传入ids")
  public R releaseBack(@ApiParam(value = "主键集合", required = true) @RequestParam Long id) {
    VersionEntity version = versionService.getById(id);
    Assert.isTrue(version != null, "版本查询失败");
    CacheUtil.evict(CacheConstant.SYS_CACHE, SysCache.VERSION_CURRENT, version.getType(), true);
    return R.status(versionService.releaseBackById(id));
  }

  @PostMapping("/remove")
  @ApiOperationSupport(order = 9)
  @ApiOperation(value = "逻辑删除", notes = "传入ids")
  public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
    return R.status(versionService.deleteLogic(Func.toLongList(ids)));
  }

  @PostMapping("/read")
  @ApiOperationSupport(order = 9)
  @ApiOperation(value = "逻辑删除", notes = "传入ids")
  public R read(@ApiParam(value = "主键集合", required = true) @RequestParam Long id,
      BladeUser user) {
    return R.status(readService.read(id, user.getUserId()));
  }

  @PostMapping("/isRead")
  @ApiOperationSupport(order = 9)
  @ApiOperation(value = "逻辑删除", notes = "传入ids")
  public R<Boolean> isRead(@ApiParam(value = "主键集合", required = true) @RequestParam Long id,
      BladeUser user) {
    return R.data(readService.isRead(id, user.getUserId()));
  }
}
