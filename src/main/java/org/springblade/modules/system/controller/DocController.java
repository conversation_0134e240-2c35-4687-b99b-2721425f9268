/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.UserCache;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.DocEntity;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IDocService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统管理-操作手册 控制器
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/doc")
@Api(value = "系统管理-操作手册", tags = "系统管理-操作手册接口")
public class DocController extends BladeController {

	private final IDocService docService;
	private final IAttachService attachService;

	/**
	 * 系统管理-操作手册 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入doc")
	public R<DocEntity> detail(DocEntity doc) {
		DocEntity detail = docService.getOne(Condition.getQueryWrapper(doc));
		Attach attach = attachService.getById(detail.getAttachId());
		detail.setAttachName(attach != null ? attach.getOriginalName() : null);
		return R.data(detail);
	}

	/**
	 * 系统管理-操作手册 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入doc")
	public R<IPage<DocEntity>> page(DocEntity doc, Query query) {
		IPage<DocEntity> pages = docService.page(Condition.getPage(query),
			Condition.getQueryWrapper(doc));
		pages.getRecords().forEach(item -> {
			User user = UserCache.getUser(item.getCreateUser());
			item.setCreateUserName(user != null ? user.getRealName() : null);
		});
		return R.data(pages);
	}

	/**
	 * 系统管理-操作手册 自定义分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入doc")
	public R<List<DocEntity>> list(DocEntity doc) {
		List<DocEntity> res = docService.list(Condition.getQueryWrapper(doc));
		return R.data(res);
	}

	/**
	 * 系统管理-操作手册 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入doc")
	public R save(@Valid @RequestBody DocEntity doc) {
		return R.status(docService.save(doc));
	}

	/**
	 * 系统管理-操作手册 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入doc")
	public R update(@Valid @RequestBody DocEntity doc) {
		return R.status(docService.updateById(doc));
	}

	/**
	 * 系统管理-操作手册 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入doc")
	public R submit(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(docService.submit(Func.toLongList(ids)));
	}

	/**
	 * 系统管理-操作手册 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(docService.deleteLogic(Func.toLongList(ids)));
	}


}
