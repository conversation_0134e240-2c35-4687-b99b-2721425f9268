/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;

import static org.springblade.modules.system.entity.UserHealthRecordsEntity.TYPE_EMPLOYEE;
import static org.springblade.modules.system.entity.UserHealthRecordsEntity.TYPE_ONBOARDING;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.annotation.DataPermissions;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserHealthRecordsEntity;
import org.springblade.modules.system.excel.UserHealthRecordExcel;
import org.springblade.modules.system.excel.UserHealthRecordImporter;
import org.springblade.modules.system.service.IUserHealthRecordsService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.vo.UserHealthRecordsVO;
import org.springblade.modules.system.vo.UserHealthWeightStatsVO;
import org.springblade.modules.system.wrapper.UserHealthRecordsWrapper;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 健康记录 控制器
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@RestController
@AllArgsConstructor
@RequestMapping("userHealthRecords/userHealthRecords")
@Api(value = "健康记录", tags = "健康记录接口")
public class UserHealthRecordsController extends BladeController {

  private final IUserHealthRecordsService userHealthRecordsService;
  private final IUserService userService;

  /**
   * 健康记录 详情
   */
  @GetMapping("/detail")
  @ApiOperationSupport(order = 1)
  @ApiOperation(value = "详情", notes = "传入userHealthRecords")
  public R<UserHealthRecordsVO> detail(UserHealthRecordsEntity userHealthRecords) {
    UserHealthRecordsEntity detail = userHealthRecordsService.getOne(
        Condition.getQueryWrapper(userHealthRecords));
    return R.data(UserHealthRecordsWrapper.build().entityVO(detail));
  }

  /**
   * 健康记录 分页
   */
  @GetMapping("/list")
  @ApiOperationSupport(order = 2)
  @ApiOperation(value = "分页", notes = "传入userHealthRecords")
  public R<IPage<UserHealthRecordsVO>> list(UserHealthRecordsEntity userHealthRecords,
      Query query) {
    IPage<UserHealthRecordsEntity> pages = userHealthRecordsService.page(Condition.getPage(query),
        Condition.getQueryWrapper(userHealthRecords));
    return R.data(UserHealthRecordsWrapper.build().pageVO(pages));
  }

  /**
   * 健康记录 自定义分页
   */
  @GetMapping("/page")
  @ApiOperationSupport(order = 3)
  @ApiOperation(value = "分页", notes = "传入userHealthRecords")
  public R<IPage<UserHealthRecordsVO>> page(UserHealthRecordsVO userHealthRecords, Query query) {
    IPage<UserHealthRecordsVO> pages = userHealthRecordsService.selectUserHealthRecordsPage(
        Condition.getPage(query), userHealthRecords);
    return R.data(pages);
  }

  @GetMapping("/v1/page")
  @ApiOperationSupport(order = 3)
  @ApiOperation(value = "分页", notes = "传入userHealthRecords")
  public R<IPage<UserHealthRecordsVO>> page1(UserHealthRecordsVO userHealthRecords, Query query) {
    //只查入职和在职体检，体重登记不查
    userHealthRecords.setType(TYPE_ONBOARDING + "," + TYPE_EMPLOYEE);
    IPage<UserHealthRecordsVO> pages = userHealthRecordsService.selectUserHealthRecordsPage1(
        Condition.getPage(query), userHealthRecords);
    return R.data(pages);
  }

  /**
   * 健康记录 新增
   */
  @PostMapping("/save")
  @ApiOperationSupport(order = 4)
  @ApiOperation(value = "新增", notes = "传入userHealthRecords")
  public R save(@Valid @RequestBody UserHealthRecordsEntity userHealthRecords) {
    // 如果是体重，则获取上次的血压、血糖、血脂、血脂状态
    if (userHealthRecords.getType().equals(UserHealthRecordsEntity.TYPE_WEIGHT)) {
      return R.status(userHealthRecordsService.saveWeight(userHealthRecords));
    }
    return R.status(userHealthRecordsService.save(userHealthRecords));
  }

  /**
   * 健康记录 修改
   */
  @PostMapping("/update")
  @ApiOperationSupport(order = 5)
  @ApiOperation(value = "修改", notes = "传入userHealthRecords")
  public R update(@Valid @RequestBody UserHealthRecordsEntity userHealthRecords) {
    return R.status(userHealthRecordsService.updateById(userHealthRecords));
  }

  /**
   * 健康记录 删除
   */
  @PostMapping("/remove")
  @ApiOperationSupport(order = 7)
  @ApiOperation(value = "逻辑删除", notes = "传入ids")
  public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
    return R.status(userHealthRecordsService.deleteLogic(Func.toLongList(ids)));
  }


  /**
   * 个人健康记录 详情
   */
  @GetMapping("/personHealthDetail")
  @ApiOperationSupport(order = 1)
  @ApiOperation(value = "详情", notes = "传入userHealthRecords")
  public R<UserHealthRecordsVO> personHealthdetail() {
    Long userId = AuthUtil.getUserId();
    UserHealthRecordsEntity detail = userHealthRecordsService.getLastByUserId(userId);
    if (Objects.isNull(detail)) {
      return R.data(null);
    }
    return R.data(UserHealthRecordsWrapper.build().entityVO(detail));
  }

  @GetMapping("/getLastByUserId")
  @ApiOperationSupport(order = 1)
  @ApiOperation(value = "详情", notes = "获取最新的检查记录，除体检外的")
  public R<UserHealthRecordsVO> getLastByUserId(@RequestParam Long userId) {
    UserHealthRecordsEntity detail = userHealthRecordsService.getLastByUserId(userId);
    if (Objects.isNull(detail)) {
      return R.data(null);
    }
    return R.data(UserHealthRecordsWrapper.build().entityVO(detail));
  }

  @GetMapping("/weightPage")
  @ApiOperationSupport(order = 3)
  @ApiOperation(value = "体重记录分页", notes = "传入userHealthRecords")
  public R<IPage<UserHealthRecordsVO>> weightPage(@RequestParam(required = false) String userName,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate queryStartDate,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate queryEndDate,
      @RequestParam(required = false) String weightStatus,
      @DataPermissions("mini-user-weight-page") List<Long> permissionUserIdList,
      Query query) {
    LambdaQueryWrapper<UserHealthRecordsEntity> wrapper = Wrappers.<UserHealthRecordsEntity>lambdaQuery()
        .ge(queryStartDate != null, UserHealthRecordsEntity::getExaminationDate, queryStartDate)
        .le(queryEndDate != null, UserHealthRecordsEntity::getExaminationDate, queryEndDate)
        .eq(StringUtils.isNotBlank(weightStatus), UserHealthRecordsEntity::getWeightStatus,
            weightStatus)
        .eq(UserHealthRecordsEntity::getType, UserHealthRecordsEntity.TYPE_WEIGHT);
    if (StringUtils.isNotBlank(userName)) {
      List<User> users = userService.list(
          Wrappers.<User>lambdaQuery().like(User::getRealName, userName)
              .eq(User::getStatus, User.STATUS_ON));
      wrapper.in(UserHealthRecordsEntity::getUserId, users.stream().map(User::getId).collect(
          Collectors.toList()));
    }
    if (!AuthUtil.isAdmin() && !AuthUtil.isAdministrator() && (permissionUserIdList == null
        || permissionUserIdList.isEmpty())) {
      wrapper.eq(UserHealthRecordsEntity::getUserId, AuthUtil.getUserId());
    } else if (!AuthUtil.isAdmin() && !AuthUtil.isAdministrator()) {
      wrapper.in(UserHealthRecordsEntity::getUserId, permissionUserIdList);
    }
    IPage<UserHealthRecordsEntity> pages = userHealthRecordsService.page(
        Condition.getPage(query), wrapper);
    return R.data(UserHealthRecordsWrapper.build().pageVO(pages));
  }

  @GetMapping("/weightStats")
  @ApiOperationSupport(order = 3)
  @ApiOperation(value = "体重记录统计", notes = "传入userHealthRecords")
  public R<UserHealthWeightStatsVO> weightStats(
      @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
      @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate
  ) {
    UserHealthWeightStatsVO stats = userHealthRecordsService.weightStats(startDate, endDate);
    return R.data(stats);
  }

  /**
   * 导出模板
   */
  @GetMapping("/export-template")
  @ApiOperationSupport(order = 14)
  @ApiOperation(value = "导出模板")
  public void exportTemplate(HttpServletResponse response) {
    List<UserHealthRecordExcel> list = new ArrayList<>();
    ExcelUtil.export(response, "健康档案导入模板", "健康档案", list, UserHealthRecordExcel.class);
  }

  /**
   * 导入
   */
  @PostMapping("/importData")
  @ApiOperationSupport(order = 7)
  public R importData(MultipartFile file, String type) {
    UserHealthRecordImporter importer = new UserHealthRecordImporter(userHealthRecordsService,
        type);
    ExcelUtil.save(file, importer, UserHealthRecordExcel.class);
    return R.success("操作成功");
  }

}
