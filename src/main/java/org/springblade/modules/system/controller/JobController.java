package org.springblade.modules.system.controller;


import io.swagger.annotations.Api;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.quartz.SchedulerException;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.tool.api.R;
import org.springblade.modules.system.service.IJobService;
import org.springblade.modules.system.vo.JobVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/job")
@Api(value = "系统管理-操作手册", tags = "系统管理-操作手册接口")
public class JobController {

	private final IJobService quartzService;

	@GetMapping("/detail")
	public R<JobVO> detail(JobVO param) throws SchedulerException {
		return R.data(quartzService.jobDetail(param));
	}

	@PostMapping("/save")
	public R save(@RequestBody JobVO param) throws SchedulerException {
		return R.status(quartzService.addJob(param));
	}

	@PostMapping("/update")
	public R update(@RequestBody JobVO param) throws SchedulerException {
		return R.status(quartzService.updateJob(param));
	}

	@PostMapping("/pause")
	public R pauseJob(JobVO param) throws SchedulerException {
		quartzService.pauseJob(param);
		return R.success("操作成功！");
	}

	/**
	 * 恢复定时任务
	 * @param param
	 * @return
	 * @throws SchedulerException
	 */
	@PostMapping("/resume")
	public R resumeJob(JobVO param) throws SchedulerException {
		quartzService.resumeJob(param);
		return R.success("操作成功！");
	}

	@PostMapping("/remove")
	public R remove(JobVO param) throws SchedulerException {
		quartzService.deleteJob(param);
		return R.success("删除成功！");
	}

	@GetMapping("/list")
	public R<List<JobVO>> jobList() throws SchedulerException {
		return R.data(quartzService.jobList());
	}


}
