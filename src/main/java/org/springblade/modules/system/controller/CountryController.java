/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.BaseCountryEntity;
import org.springblade.modules.system.service.IBaseCountryService;
import org.springblade.modules.system.vo.BaseCountryVO;
import org.springblade.modules.system.wrapper.BaseCountryWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 国家 控制器
 *
 * <AUTHOR>
 * @since 2022-11-01
 */
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/country")
@Api(value = "国家", tags = "国家接口")
public class CountryController extends BladeController {

	private final IBaseCountryService baseCountryService;

	/**
	 * 国家 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入baseCountry")
	public R<BaseCountryVO> detail(BaseCountryEntity baseCountry) {
		BaseCountryEntity detail = baseCountryService.getOne(
			Condition.getQueryWrapper(baseCountry));
		return R.data(BaseCountryWrapper.build().entityVO(detail));
	}

	/**
	 * 国家 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入baseCountry")
	public R<List<BaseCountryVO>> list(BaseCountryVO baseCountry) {
		String query = baseCountry.getQuery();
		baseCountry.setQuery(null);
		QueryWrapper<BaseCountryEntity> wrapper = Condition.getQueryWrapper(baseCountry);
		wrapper.apply(
			"name like concat('%',{0},'%') or english like concat('%',upper({0}),'%') or code like concat('%',upper({0}),'%')",
			query);
		List<BaseCountryEntity> list = baseCountryService.list(wrapper);
		return R.data(BaseCountryWrapper.build().listVO(list));
	}

	/**
	 * 国家 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入baseCountry")
	public R<IPage<BaseCountryVO>> page(BaseCountryEntity baseCountry, Query query) {
		QueryWrapper<BaseCountryEntity> wrapper = Condition.getQueryWrapper(baseCountry);
		wrapper.lambda().and(StringUtils.isNotBlank(baseCountry.getKeyword()), w ->
			w.like(BaseCountryEntity::getName, baseCountry.getKeyword()).or()
				.like(BaseCountryEntity::getEnglish, baseCountry.getKeyword()).or()
				.like(BaseCountryEntity::getCode, baseCountry.getKeyword()));
		IPage<BaseCountryEntity> pages = baseCountryService.page(Condition.getPage(query), wrapper);
		return R.data(BaseCountryWrapper.build().pageVO(pages));
	}

	/**
	 * 国家 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入baseCountry")
	public R save(@Valid @RequestBody BaseCountryEntity baseCountry) {
		return R.status(baseCountryService.save(baseCountry));
	}

	/**
	 * 国家 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入baseCountry")
	public R update(@Valid @RequestBody BaseCountryEntity baseCountry) {
		return R.status(baseCountryService.updateById(baseCountry));
	}

	/**
	 * 国家 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入baseCountry")
	public R submit(@Valid @RequestBody BaseCountryEntity baseCountry) {
		return R.status(baseCountryService.saveOrUpdate(baseCountry));
	}

	/**
	 * 国家 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(baseCountryService.deleteLogic(Func.toLongList(ids)));
	}


}
