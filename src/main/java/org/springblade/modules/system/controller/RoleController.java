/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.constant.AuthConstant;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.Role;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.vo.GrantVO;
import org.springblade.modules.system.vo.RoleVO;
import org.springblade.modules.system.wrapper.RoleWrapper;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static org.springblade.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/role")
@Api(value = "角色", tags = "角色")
public class RoleController extends BladeController {

  private final IRoleService roleService;

  /**
   * 详情
   */
  @GetMapping("/detail")
  @ApiOperationSupport(order = 1)
  @ApiOperation(value = "详情", notes = "传入role")
  public R<RoleVO> detail(Role role) {
    Role detail = roleService.getOne(Condition.getQueryWrapper(role));
    return R.data(RoleWrapper.build().entityVO(detail));
  }

  /**
   * 列表
   */
  @GetMapping("/list")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "roleName", value = "参数名称", paramType = "query", dataType = "string"),
      @ApiImplicitParam(name = "roleAlias", value = "角色别名", paramType = "query", dataType = "string")
  })
  @ApiOperationSupport(order = 2)
  @ApiOperation(value = "列表", notes = "传入role")
  public R<List<RoleVO>> list(@ApiIgnore @RequestParam Map<String, Object> role,
      BladeUser bladeUser) {
    QueryWrapper<Role> queryWrapper = Condition.getQueryWrapper(role, Role.class);
    queryWrapper.orderByAsc("sort");
    List<Role> list = roleService.list(
        (!bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda()
            .eq(Role::getTenantId, bladeUser.getTenantId()) : queryWrapper);
    return R.data(RoleWrapper.build().listNodeVO(list));
  }

  /**
   * 获取角色树形结构
   */
  @GetMapping("/tree")
  @ApiOperationSupport(order = 3)
  @ApiOperation(value = "树形结构", notes = "树形结构")
  public R<List<RoleVO>> tree(String tenantId, BladeUser bladeUser) {
    List<RoleVO> tree = roleService.tree(Func.toStrWithEmpty(tenantId, bladeUser.getTenantId()));
    return R.data(tree);
  }

  /**
   * 获取指定角色树形结构
   */
  @GetMapping("/tree-by-id")
  @ApiOperationSupport(order = 4)
  @ApiOperation(value = "树形结构", notes = "树形结构")
  public R<List<RoleVO>> treeById(Long roleId, BladeUser bladeUser) {
    Role role = SysCache.getRole(roleId);
    List<RoleVO> tree = roleService.tree(
        Func.notNull(role) ? role.getTenantId() : bladeUser.getTenantId());
    return R.data(tree);
  }

  /**
   * 新增或修改
   */
  @PostMapping("/submit")
  @ApiOperationSupport(order = 5)
  @ApiOperation(value = "新增或修改", notes = "传入role")
  public R submit(@Valid @RequestBody Role role) {
    CacheUtil.clear(SYS_CACHE);
    CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
    return R.status(roleService.submit(role));
  }

  /**
   * 删除
   */
  @PostMapping("/remove")
  @ApiOperationSupport(order = 6)
  @ApiOperation(value = "删除", notes = "传入ids")
  public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
    CacheUtil.clear(SYS_CACHE);
    CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
    return R.status(roleService.removeRole(ids));
  }

  /**
   * 设置角色权限
   */
  @PostMapping("/grant")
  @ApiOperationSupport(order = 7)
  @ApiOperation(value = "权限设置", notes = "传入roleId集合以及menuId集合")
  public R grant(@RequestBody GrantVO grantVO) {
    CacheUtil.clear(SYS_CACHE);
    CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
    boolean temp = roleService.grant(grantVO.getRoleIds(), grantVO.getMenuIds(),
        grantVO.getAppMenuIds(), grantVO.getDataScopeIds(), grantVO.getApiScopeIds(),grantVO.getChatIds());
    return R.status(temp);
  }

  /**
   * 下拉数据源
   */
  @PreAuth(AuthConstant.PERMIT_ALL)
  @GetMapping("/select")
  @ApiOperationSupport(order = 8)
  @ApiOperation(value = "下拉数据源", notes = "传入id集合")
  public R<List<Role>> select(Long userId, String roleId) {
    if (Func.isNotEmpty(userId)) {
      User user = UserCache.getUser(userId);
      roleId = user.getRoleId();
    }
    List<Role> list = roleService.list(
        Wrappers.<Role>lambdaQuery().in(Role::getId, Func.toLongList(roleId)));
    return R.data(list);
  }

}
