/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.PrintTemplateEntity;
import org.springblade.modules.system.service.IPrintTemplateService;
import org.springblade.modules.system.vo.PrintTemplateVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.wildfly.common.Assert;

/**
 * 打印模板 控制器
 *
 * <AUTHOR>
 * @since 2023-10-14
 */
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/printTemplate")
@Api(value = "打印模板", tags = "打印模板接口")
public class PrintTemplateController extends BladeController {

	private final IPrintTemplateService printTemplateService;

	/**
	 * 打印模板 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入bladePrintTemplate")
	public R<PrintTemplateEntity> detail(PrintTemplateEntity bladePrintTemplate) {
		PrintTemplateEntity detail = printTemplateService.getOne(
			Condition.getQueryWrapper(bladePrintTemplate));
		return R.data(detail);
	}

	/**
	 * 打印模板 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入bladePrintTemplate")
	public R<IPage<PrintTemplateEntity>> list(PrintTemplateEntity bladePrintTemplate, Query query) {
		IPage<PrintTemplateEntity> pages = printTemplateService.page(Condition.getPage(query),
			Condition.getQueryWrapper(bladePrintTemplate));
		return R.data(pages);
	}

	/**
	 * 打印模板 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入bladePrintTemplate")
	public R<IPage<PrintTemplateVO>> page(PrintTemplateVO bladePrintTemplate, Query query) {
		IPage<PrintTemplateVO> pages = printTemplateService.selectBladePrintTemplatePage(
			Condition.getPage(query), bladePrintTemplate);
		return R.data(pages);
	}

	/**
	 * 打印模板 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入bladePrintTemplate")
	public R save(@Valid @RequestBody PrintTemplateEntity bladePrintTemplate) {
		return R.status(printTemplateService.save(bladePrintTemplate));
	}

	/**
	 * 打印模板 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入bladePrintTemplate")
	public R update(@Valid @RequestBody PrintTemplateEntity bladePrintTemplate) {
		return R.status(printTemplateService.updateById(bladePrintTemplate));
	}

	/**
	 * 打印模板 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入bladePrintTemplate")
	public R submit(@Valid @RequestBody PrintTemplateEntity bladePrintTemplate) {
		if (bladePrintTemplate.getId() == null || bladePrintTemplate.getId() <= 0) {
			long count = printTemplateService.count(Wrappers.<PrintTemplateEntity>lambdaQuery()
				.eq(PrintTemplateEntity::getCode, bladePrintTemplate.getCode()));
			Preconditions.checkArgument(count == 0, "该模板编码已存在，请确认后再操作");
			bladePrintTemplate.setVersion(1);
		}
		return R.status(printTemplateService.saveOrUpdate(bladePrintTemplate));
	}

	/**
	 * 打印模板 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(printTemplateService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 打印模板 新增或修改
	 */
	@PostMapping("/design")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "设计", notes = "传入bladePrintTemplate")
	public R design(@Valid @RequestBody PrintTemplateEntity bladePrintTemplate,
		@RequestParam(defaultValue = "false") Boolean cover) {
		return R.status(printTemplateService.design(bladePrintTemplate, cover));
	}

	/**
	 * 打印模板 新增或修改
	 */
	@PostMapping("/master")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "设为主版本", notes = "传入bladePrintTemplate")
	public R master(@ApiParam(value = "主键集合", required = true) @RequestParam Long id) {
		return R.status(printTemplateService.master(id));
	}

	/**
	 * 打印模板 新增或修改
	 */
	@PostMapping("/loadTemplate")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "设为主版本", notes = "传入bladePrintTemplate")
	public R<PrintTemplateEntity> loadTemplate(
		@ApiParam(value = "主键集合", required = true) @RequestParam String code) {
		return R.data(printTemplateService.loadTemplate(code));
	}



}
