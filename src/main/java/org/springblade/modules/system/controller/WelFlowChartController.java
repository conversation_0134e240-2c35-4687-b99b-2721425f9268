/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.WelFlowChartEntity;
import org.springblade.modules.system.service.IWelFlowChartService;
import org.springblade.modules.system.vo.WelFlowChartVO;
import org.springblade.modules.system.wrapper.WelFlowChartWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统管理-首页流程图配置 控制器
 *
 * <AUTHOR>
 * @since 2023-10-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("WelFlowChart/niWelFlowChart")
@Api(value = "系统管理-首页流程图配置", tags = "系统管理-首页流程图配置接口")
public class WelFlowChartController extends BladeController {

	private final IWelFlowChartService welFlowChartService;

	/**
	 * 系统管理-首页流程图配置 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入niWelFlowChart")
	public R<WelFlowChartVO> detail(WelFlowChartEntity niWelFlowChart) {
		WelFlowChartEntity detail = welFlowChartService.getOne(
			Condition.getQueryWrapper(niWelFlowChart));
		return R.data(WelFlowChartWrapper.build().entityVO(detail));
	}

	/**
	 * 系统管理-首页流程图配置 树列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入notice")
	public R<List<WelFlowChartVO>> list(WelFlowChartEntity niWelFlowChart, BladeUser bladeUser) {
		QueryWrapper<WelFlowChartEntity> queryWrapper = Condition.getQueryWrapper(niWelFlowChart);
		List<WelFlowChartEntity> list = welFlowChartService.list(
			(!bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda()
				.eq(WelFlowChartEntity::getTenantId, bladeUser.getTenantId()) : queryWrapper);
		return R.data(WelFlowChartWrapper.build().treeNodeVO(list));
	}

	/**
	 * 系统管理-首页流程图配置 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入niWelFlowChart")
	public R<IPage<WelFlowChartVO>> page(WelFlowChartVO niWelFlowChart, Query query) {
		IPage<WelFlowChartVO> pages = welFlowChartService.selectNiWelFlowChartPage(
			Condition.getPage(query), niWelFlowChart);
		return R.data(pages);
	}

	/**
	 * 系统管理-首页流程图配置 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入niWelFlowChart")
	public R save(@Valid @RequestBody WelFlowChartEntity niWelFlowChart) {
		return R.status(welFlowChartService.save(niWelFlowChart));
	}

	/**
	 * 系统管理-首页流程图配置 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入niWelFlowChart")
	public R update(@Valid @RequestBody WelFlowChartEntity niWelFlowChart) {
		return R.status(welFlowChartService.updateById(niWelFlowChart));
	}

	/**
	 * 系统管理-首页流程图配置 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入niWelFlowChart")
	public R submit(@Valid @RequestBody WelFlowChartEntity niWelFlowChart) {
		return R.status(welFlowChartService.saveOrUpdate(niWelFlowChart));
	}

	/**
	 * 系统管理-首页流程图配置 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(welFlowChartService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 系统管理-首页流程图配置 树形结构
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<WelFlowChartVO>> tree(String tenantId, BladeUser bladeUser) {
		List<WelFlowChartVO> tree = welFlowChartService.tree(
			Func.toStrWithEmpty(tenantId, bladeUser.getTenantId()));
		return R.data(tree);
	}

}
