package org.springblade.modules.system.resolver;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.natergy.ni.meal.entity.MenuEntity;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.system.annotation.DataPermissions;
import org.springblade.modules.system.entity.Menu;
import org.springblade.modules.system.service.IMenuService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/14
 */

@Component
public class DataPermissionsResolver implements HandlerMethodArgumentResolver {

	private final IUserService userService;

	private final IMenuService menuService;

	public DataPermissionsResolver(IUserService userService, IMenuService menuService) {
		this.userService = userService;
		this.menuService = menuService;
	}

	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		return parameter.hasParameterAnnotation(DataPermissions.class);
	}

	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		DataPermissions annotation = parameter.getParameterAnnotation(DataPermissions.class);
		if (Objects.isNull(annotation)) {
			return Collections.emptyList();
		}
		//拿到菜单的code
		String menuCode = annotation.value();

		Menu menuData = menuService.getOne(new LambdaQueryWrapper<Menu>()
			.select(Menu::getId)
			.eq(Menu::getCode, menuCode));
    if(menuData == null){
      return Collections.emptyList();
    }
		return userService.getUserByRole(menuData.getId(), AuthUtil.getUser().getRoleId());

	}


}
