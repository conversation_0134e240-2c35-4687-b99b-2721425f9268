/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.crm.dto.UserRegionDTO;
import com.natergy.ni.crm.vo.RelatedUserVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.modules.auth.enums.UserEnum;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserInfo;
import org.springblade.modules.system.entity.UserOauth;
import org.springblade.modules.system.excel.UserExcel;
import org.springblade.modules.system.vo.ContractItem;
import org.springblade.modules.system.vo.DeptAndUserVO;
import org.springblade.modules.system.vo.UserVO;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IUserService extends BaseService<User> {

	/**
	 * 新增用户
	 *
	 * @param user
	 * @return
	 */
	boolean submit(User user);

	/**
	 * 修改用户
	 *
	 * @param user
	 * @return
	 */
	boolean updateUser(User user);

	/**
	 * 修改用户基本信息
	 *
	 * @param user
	 * @return
	 */
	boolean updateUserInfo(User user);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param user
	 * @param deptId
	 * @param tenantId
	 * @return
	 */
	IPage<User> selectUserPage(IPage<User> page, User user, Long deptId, String tenantId);

	IPage<User> selectUserPageByVO(IPage<User> page, UserVO user, Long deptId, String tenantId);

	/**
	 * 自定义分页
	 *
	 * @param user
	 * @param query
	 * @return
	 */
	IPage<UserVO> selectUserSearch(UserVO user, Query query);

	/**
	 * 根据账号获取用户
	 *
	 * @param tenantId
	 * @param account
	 * @return
	 */
	User userByAccount(String tenantId, String account);

	/**
	 * 用户信息
	 *
	 * @param userId
	 * @return
	 */
	UserInfo userInfo(Long userId);

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param account
	 * @param password
	 * @return
	 */
	UserInfo userInfo(String tenantId, String account, String password);

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param account
	 * @param password
	 * @param userEnum
	 * @return
	 */
	UserInfo userInfo(String tenantId, String account, String password, UserEnum userEnum);

	/**
	 * 用户信息
	 *
	 * @param userOauth
	 * @return
	 */
	UserInfo userInfo(UserOauth userOauth);

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param openId
	 * @param userEnum
	 * @return
	 */
	UserInfo userInfoByMiniProgramOpenId(String tenantId, String openId, UserEnum userEnum);

	/**
	 * 给用户设置角色
	 *
	 * @param userIds
	 * @param roleIds
	 * @return
	 */
	boolean grant(String userIds, String roleIds);

	/**
	 * 初始化密码
	 *
	 * @param userIds
	 * @return
	 */
	boolean resetPassword(String userIds);

	/**
	 * 修改密码
	 *
	 * @param userId
	 * @param oldPassword
	 * @param newPassword
	 * @param newPassword1
	 * @return
	 */
	boolean updatePassword(Long userId, String oldPassword, String newPassword,
		String newPassword1);

	/**
	 * 删除用户
	 *
	 * @param userIds
	 * @return
	 */
	boolean removeUser(String userIds);

	/**
	 * 导入用户数据
	 *
	 * @param data
	 * @param isCovered
	 * @return
	 */
	void importUser(List<UserExcel> data, Boolean isCovered);

	/**
	 * 导出用户数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<UserExcel> exportUser(Wrapper<User> queryWrapper);

	/**
	 * 注册用户
	 *
	 * @param user
	 * @param oauthId
	 * @return
	 */
	boolean registerGuest(User user, Long oauthId);

	/**
	 * 配置用户平台
	 *
	 * @param userId
	 * @param userType
	 * @param userExt
	 * @return
	 */
	boolean updatePlatform(Long userId, Integer userType, String userExt);

	/**
	 * 用户详细信息
	 *
	 * @param user
	 * @return
	 */
	UserVO platformDetail(User user);

	/**
	 * 校验并关联考勤信息
	 *
	 * @param user
	 */
	void relateUserInfo(User user);

	List<User> selectUserListByVO(UserVO user, String deptId, String tenantId);

	/**
	 * 根据考勤系统的cid查询用户
	 *
	 * @param cid
	 * @return
	 */
	User getByCid(String cid);

	/**
	 * 根据微信小程序的openid获取用户
	 *
	 * @param tenantId
	 * @param openid
	 * @return
	 */
	User getByMiniProgramOpenId(String tenantId, String openid);

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param phone
	 * @param userEnum
	 * @return
	 */
	UserInfo userInfoByPhone(String tenantId, String phone, UserEnum userEnum);

	String searchSamePhone(User user);

	/**
	 * 用户基本信息
	 *
	 * @return
	 */
	IPage<User> userBaseInfoPage(Query query, UserVO user);

	List<Map<String, Object>> getAddressList();

	void generateQRImage(QueryWrapper<User> queryWrapper, List<Long> deptIds,
		HttpServletResponse response)
		throws UnsupportedEncodingException;

	Boolean addContract(UserVO vo, IAttachService attachService);

	Boolean updateContract(UserVO vo, IAttachService attachService);

	Boolean removeContract(Long userId, String ids, String sns, IAttachService attachService);

	Boolean changeContract(Long userId, String sn);

	ContractItem iniContract(UserVO vo, IAttachService attachService);

	void setVoContractJson(UserVO user, IAttachService attachService);

	/**
	 * 用户取消关注小程序之后，将对应的openid清空掉
	 *
	 * @param openId openId
	 */
	void unSubscribe(String openId);

	/**
	 * 查询用户id的订阅状态
	 *
	 * @param userId 用户id
	 * @return null代表没有订阅，1代表订阅量了
	 */
	Integer getUserSubscribeStatusById(Long userId);


	IPage<UserVO> selectUserSearch(String name, Integer status, Query query);


	/**
	 * 根据角色ID 查询出来对应的数据权限组，根据数据权限组中的配置获取用户列表 并将所有符合的用户ID排重后封装成一个集合返回
	 *
	 * @param roleIds 角色ID
	 * @return 用户IDs
	 */
	List<Long> getUserByRole(Long menuId, String roleIds);


	/**
	 * 获取全部用户数据，只获取名称和id
	 *
	 * @return 用户列表
	 */
	List<User> getAllUserList();


	List<String> getCheckinUserList();


	/**
	 * 根据角色id获取用户id
	 *
	 * @param roleId 角色id
	 * @return 用户id集合
	 */
	List<Long> getUserIdByRoleId(Long roleId);

	Future<Long> countByDeptIds(List<Long> deptIds);

	Map<Long, Long> countGroupByDeptIds(Set<Long> deptIds);


	/**
	 * 所有在职的员工
	 *
	 * @return 用户列表
	 */
	List<DeptAndUserVO> getAllUser();


	/**
	 * 递归查询销售部门的所有人员
	 *
	 * @param page      分页参数
	 * @param searchKey 搜索关键字
	 * @param userIds   用户id集合
	 * @return 用户列表
	 */
	IPage<RelatedUserVO> selectRecursionSalespersonList(IPage<RelatedUserVO> page, String searchKey,
		List<Long> userIds);

	/**
	 * 绑定头像
	 *
	 * @param userList 用户集合
	 */

	void bindAvatar(List<RelatedUserVO> userList);

	/**
	 * 绑定用户的大区数据
	 *
	 * @param userList   用户数据
	 * @param regionList 大区数据
	 */
	void bindRegion(List<RelatedUserVO> userList, List<UserRegionDTO> regionList);

	/**
	 * 标记省内外人员
	 */
	Boolean markInProvince(List<Long> ids, Boolean inProvince);

	/**
	 * 获取权限用户
	 */
	List<Long> getScopeUserId(String DATA_SCOPE_CODE);

	/**
	 * 根据部门id递归获取在职用户列表
	 *
	 * @param deptIds
	 * @return
	 */
	List<User> listByDeptIds(List<Long> deptIds);

	boolean grantPlus(List<Long> userIds, List<Long> roleIds);
}
