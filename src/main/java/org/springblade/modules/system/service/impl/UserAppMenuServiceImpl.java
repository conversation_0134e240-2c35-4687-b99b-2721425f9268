
package org.springblade.modules.system.service.impl;

import org.springblade.modules.system.entity.AppMenu;
import org.springblade.modules.system.entity.UserAppMenuEntity;
import org.springblade.modules.system.mapper.UserAppMenuMapper;
import org.springblade.modules.system.service.IUserAppMenuService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户小程序菜单 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
public class UserAppMenuServiceImpl extends BaseServiceImpl<UserAppMenuMapper, UserAppMenuEntity> implements IUserAppMenuService {


	@Override
	public List<UserAppMenuEntity> getUserAppMenuByUserId(Long userId) {
		return this.baseMapper.getUserAppMenuByUserId(userId);
	}
}
