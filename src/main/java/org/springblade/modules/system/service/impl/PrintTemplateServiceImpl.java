/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.google.common.base.Preconditions;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.system.entity.PrintTemplateEntity;
import org.springblade.modules.system.mapper.PrintTemplateMapper;
import org.springblade.modules.system.service.IPrintTemplateService;
import org.springblade.modules.system.vo.PrintTemplateVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 打印模板 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-14
 */
@Service
public class PrintTemplateServiceImpl extends
	BaseServiceImpl<PrintTemplateMapper, PrintTemplateEntity> implements IPrintTemplateService {

	@Override
	public IPage<PrintTemplateVO> selectBladePrintTemplatePage(
		IPage<PrintTemplateVO> page, PrintTemplateVO bladePrintTemplate) {
		return page.setRecords(baseMapper.selectBladePrintTemplatePage(page, bladePrintTemplate));
	}

	/**
	 * @param template
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean design(PrintTemplateEntity template, boolean cover) {
		PrintTemplateEntity lastVersion = getOne(Wrappers.<PrintTemplateEntity>lambdaQuery()
			.eq(PrintTemplateEntity::getCode, template.getCode()).orderByDesc(
				PrintTemplateEntity::getVersion), false);
		if (lastVersion == null) {
			throw new ServiceException("模板保存失败，请联系管理员");
		}
		if (cover) {
			return this.update(Wrappers.<PrintTemplateEntity>lambdaUpdate()
				.eq(PrintTemplateEntity::getId, lastVersion.getId())
				.set(PrintTemplateEntity::getContent, template.getContent()));
		} else {
			this.update(Wrappers.<PrintTemplateEntity>lambdaUpdate()
				.eq(PrintTemplateEntity::getCode, template.getCode())
				.set(PrintTemplateEntity::getStatus, PrintTemplateEntity.STATUS_PAUSE));
			PrintTemplateEntity entity = new PrintTemplateEntity();
			BeanUtils.copyProperties(lastVersion, entity);
			entity.setId(null);
			entity.setVersion(lastVersion.getVersion() + 1);
			entity.setContent(template.getContent());
			entity.setStatus(PrintTemplateEntity.STATUS_RUNNING);
			return this.save(entity);
		}
	}

	/**
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean master(Long id) {
		PrintTemplateEntity template = getById(id);
		Preconditions.checkArgument(template != null, "操作失败！");
		boolean res = update(Wrappers.<PrintTemplateEntity>lambdaUpdate()
			.eq(PrintTemplateEntity::getCode, template.getCode())
			.set(PrintTemplateEntity::getStatus, PrintTemplateEntity.STATUS_PAUSE));
		Preconditions.checkArgument(res, "更新版本失败！");
		return update(
			Wrappers.<PrintTemplateEntity>lambdaUpdate().eq(PrintTemplateEntity::getId, id)
				.set(PrintTemplateEntity::getStatus, PrintTemplateEntity.STATUS_RUNNING));
	}

	/**
	 * @param code
	 * @return
	 */
	@Override
	public PrintTemplateEntity loadTemplate(String code) {
		return getOne(
			Wrappers.<PrintTemplateEntity>lambdaQuery().eq(PrintTemplateEntity::getCode, code)
				.eq(PrintTemplateEntity::getStatus, PrintTemplateEntity.STATUS_RUNNING)
				.orderByDesc(PrintTemplateEntity::getVersion), false);
	}


}
