/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.common.cache.SysCache;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.Post;
import org.springblade.modules.system.mapper.DeptMapper;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IPostService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.util.TreeUtil;
import org.springblade.modules.system.vo.DeptAndUserVO;
import org.springblade.modules.system.vo.DeptVO;
import org.springblade.modules.system.wrapper.DeptWrapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements IDeptService {


  private static final String TENANT_ID = "tenantId";
  private static final String PARENT_ID = "parentId";

  /**
   * 组织机构中公司的id
   */
  private final Long COMPANY_ID = 1123598813738675201L;


  /**
   * 2代表了是二级部门，例如：技术三部
   */
  private static final Integer DEPT_CATEGORY = 2;

  private final IUserService iUserService;

  private final IPostService iPostService;

  public DeptServiceImpl(IUserService iUserService, IPostService iPostService) {
    this.iUserService = iUserService;
    this.iPostService = iPostService;
  }


  @Override
  public List<DeptVO> lazyList(String tenantId, Long parentId, Map<String, Object> param) {
    // 设置租户ID
    if (AuthUtil.isAdministrator()) {
      tenantId = StringPool.EMPTY;
    }
    String paramTenantId = Func.toStr(param.get(TENANT_ID));
    if (Func.isNotEmpty(paramTenantId) && AuthUtil.isAdministrator()) {
      tenantId = paramTenantId;
    }
    // 判断点击搜索但是没有查询条件的情况
    if (Func.isEmpty(param.get(PARENT_ID)) && param.size() == 1) {
      parentId = 0L;
    }
    // 判断数据权限控制,非超管角色只可看到本级及以下数据
    if (Func.toLong(parentId) == 0L && !AuthUtil.isAdministrator()) {
      Long deptId = Func.firstLong(AuthUtil.getDeptId());
      Dept dept = SysCache.getDept(deptId);
      if (dept.getParentId() != 0) {
        parentId = dept.getParentId();
      }
    }
    // 判断点击搜索带有查询条件的情况
    if (Func.isEmpty(param.get(PARENT_ID)) && param.size() > 1 && Func.toLong(parentId) == 0L) {
      parentId = null;
    }
    return baseMapper.lazyList(tenantId, parentId, param);
  }

  @Override
  public List<DeptVO> tree(String tenantId, String deptCategory, String code, String parentCode,
      List<Long> ids) {
    return ForestNodeMerger.merge(baseMapper.tree(tenantId, deptCategory, code, parentCode, ids));
  }

  @Override
  public List<DeptVO> lazyTree(String tenantId, Long parentId) {
    if (AuthUtil.isAdministrator()) {
      tenantId = StringPool.EMPTY;
    }
    return ForestNodeMerger.merge(baseMapper.lazyTree(tenantId, parentId));
  }

  @Override
  public String getDeptIds(String tenantId, String deptNames) {
    List<Dept> deptList = baseMapper.selectList(
        Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, tenantId)
            .in(Dept::getDeptName, Func.toStrList(deptNames)));
    if (deptList != null && !deptList.isEmpty()) {
      return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct()
          .collect(Collectors.joining(","));
    }
    return null;
  }

  @Override
  public String getDeptIdsByFuzzy(String tenantId, String deptNames) {
    LambdaQueryWrapper<Dept> queryWrapper = Wrappers.<Dept>query().lambda()
        .eq(Dept::getTenantId, tenantId);
    queryWrapper.and(wrapper -> {
      List<String> names = Func.toStrList(deptNames);
      names.forEach(name -> wrapper.like(Dept::getDeptName, name).or());
    });
    List<Dept> deptList = baseMapper.selectList(queryWrapper);
    if (deptList != null && !deptList.isEmpty()) {
      return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct()
          .collect(Collectors.joining(","));
    }
    return null;
  }

  @Override
  public List<String> getDeptNames(String deptIds) {
    return baseMapper.getDeptNames(Func.toLongArray(deptIds));
  }
  @Override
  public List<String> getDeptCodes(String deptIds) {
    return baseMapper.getDeptCodes(Func.toLongArray(deptIds));
  }

  @Override
  public List<Dept> getDeptChild(Long deptId) {
    return baseMapper.selectList(
        Wrappers.<Dept>query().lambda().like(Dept::getAncestors, deptId));
  }

  @Override
  public boolean removeDept(String ids) {
    Long cnt = baseMapper.selectCount(
        Wrappers.<Dept>query().lambda().in(Dept::getParentId, Func.toLongList(ids)));
    if (cnt > 0L) {
      throw new ServiceException("请先删除子节点!");
    }
    return removeByIds(Func.toLongList(ids));
  }

  @Override
  public boolean submit(Dept dept) {
    if (Func.isEmpty(dept.getParentId())) {
      dept.setTenantId(AuthUtil.getTenantId());
      dept.setParentId(BladeConstant.TOP_PARENT_ID);
      dept.setAncestors(String.valueOf(BladeConstant.TOP_PARENT_ID));
    }
    if (dept.getParentId() > 0) {
      Dept parent = getById(dept.getParentId());
      if (Func.toLong(dept.getParentId()) == Func.toLong(dept.getId())) {
        throw new ServiceException("父节点不可选择自身!");
      }
      dept.setTenantId(parent.getTenantId());
      String ancestors = parent.getAncestors() + StringPool.COMMA + dept.getParentId();
      dept.setAncestors(ancestors);
    }
    dept.setIsDeleted(BladeConstant.DB_NOT_DELETED);
    return saveOrUpdate(dept);
  }

  @Override
  public List<DeptVO> search(String deptName, Long parentId) {
    String tenantId = AuthUtil.getTenantId();
    LambdaQueryWrapper<Dept> queryWrapper = Wrappers.<Dept>query().lambda();
    if (Func.isNotEmpty(tenantId)) {
      queryWrapper.eq(Dept::getTenantId, tenantId);
    }
    if (Func.isNotEmpty(deptName)) {
      queryWrapper.like(Dept::getDeptName, deptName);
    }
    if (Func.isNotEmpty(parentId) && parentId > 0L) {
      queryWrapper.eq(Dept::getParentId, parentId);
    }
    List<Dept> deptList = baseMapper.selectList(queryWrapper);
    return DeptWrapper.build().listNodeVO(deptList);
  }

  /**
   * 根据部门编码获取部门id
   *
   * @param tenantId
   * @param codes
   * @return
   */
  @Override
  public String getDeptIdsByCodes(String tenantId, String codes) {
    List<Dept> deptList = baseMapper.selectList(
        Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, tenantId)
            .in(Dept::getCode, Func.toStrList(codes)));
    if (deptList != null && !deptList.isEmpty()) {
      return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct()
          .collect(Collectors.joining(","));
    }
    return null;
  }

  @Override
  public Dept getSimpleDeptInfoById(Long deptId) {
    Dept simpleDeptInfo = this.baseMapper.getSimpleDeptInfoById(deptId);
    if (simpleDeptInfo == null) {
      return null;
    }
    if (Objects.equals(COMPANY_ID, simpleDeptInfo.getParentId()) || Objects.equals(0L,
        simpleDeptInfo.getParentId())) {
      return simpleDeptInfo;
    }
    return this.getSimpleDeptInfoById(simpleDeptInfo.getParentId());
  }

  @Override
  public Set<Long> getDeptChildSet(List<Long> deptIdList) {

    if (Objects.isNull(deptIdList)) {
      return Collections.emptySet();
    }
    return deptIdList.stream()
        .flatMap(item -> {
          List<Long> deptChildIds = SysCache.getDeptChildIds(item);
          if (Objects.nonNull(deptChildIds)) {
            return deptChildIds.stream();
          }
          return null;
        }).collect(Collectors.toSet());

  }

  @Override
  public List<Dept> getAllDeptList() {
    return this.list(new LambdaQueryWrapper<Dept>()
        .select(Dept::getId, Dept::getDeptName));
  }

  /**
   * @param tenantId
   * @param deptCategory
   * @param code
   * @param parentCode
   * @return
   */
  @Override
  public List<DeptVO> treeList(String tenantId, String deptCategory, String code,
      String parentCode) {
    return baseMapper.tree(tenantId, deptCategory, code, parentCode, null);
  }

  @Override
  public List<DeptAndUserVO> getDeptAndUser() {

    //1、获取所有部门，并将一维集合转为树形结构
    List<DeptAndUserVO> allDept = this.baseMapper.getAllDept();

    List<DeptAndUserVO> allUser = iUserService.getAllUser();

    List<Post> allPost = iPostService.selectAllPost();

    //给员工绑定上部门
    TreeUtil.bindingPost(allUser, allPost);

    return TreeUtil.buildTree(allDept, allUser);

  }

}
