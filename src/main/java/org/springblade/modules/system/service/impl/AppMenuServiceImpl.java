/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import org.springblade.common.cache.DictCache;
import org.springblade.common.enums.DictEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.node.TreeNode;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.system.entity.AppMenu;
import org.springblade.modules.system.entity.Dict;
import org.springblade.modules.system.entity.RoleAppMenu;
import org.springblade.modules.system.entity.UserAppMenuEntity;
import org.springblade.modules.system.mapper.AppMenuMapper;
import org.springblade.modules.system.service.IAppMenuService;
import org.springblade.modules.system.service.IRoleAppMenuService;
import org.springblade.modules.system.service.IUserAppMenuService;
import org.springblade.modules.system.vo.AppMenuVO;
import org.springblade.modules.system.wrapper.AppMenuWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 小程序菜单 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@RequiredArgsConstructor
@Service
public class AppMenuServiceImpl extends BaseServiceImpl<AppMenuMapper, AppMenu> implements
	IAppMenuService {

	private final IRoleAppMenuService roleAppMenuService;

	private final IUserAppMenuService userAppMenuService;

	@Override
	public IPage<AppMenuVO> selectAppMenuPage(IPage<AppMenuVO> page, AppMenuVO appMenu) {
		return page.setRecords(baseMapper.selectAppMenuPage(page, appMenu));
	}

	@Override
	public List<AppMenuVO> tree(String tenantId) {
		return ForestNodeMerger.merge(baseMapper.tree(tenantId));
	}

	/**
	 * @param user
	 * @return
	 */
	@Override
	public List<TreeNode> grantTree(BladeUser user) {
		List<AppMenu> menuList = user.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID) ?
			list() : baseMapper.grantListByRole(
			Func.toLongList(user.getRoleId()));
		List<TreeNode> menuTree = menuList.stream().map(menu -> {
			TreeNode treeNode = new TreeNode();
			BeanUtils.copyProperties(menu, treeNode);
			treeNode.setParentId(menu.getCategory().longValue());
			treeNode.setKey(menu.getId());
			treeNode.setTitle(menu.getName());
			treeNode.setValue(menu.getId());
			return treeNode;
		}).collect(Collectors.toList());
		List<Dict> dictList = DictCache.getList(DictEnum.APP_MENU_CATEGORY.getName());
		List<TreeNode> dictTreeNode = dictList.stream().map(dict -> {
			TreeNode treeNode = new TreeNode();
			treeNode.setId(Long.valueOf(dict.getDictKey()));
			treeNode.setParentId(0L);
			treeNode.setKey(Long.valueOf(dict.getDictKey()));
			treeNode.setTitle(dict.getDictValue());
			treeNode.setValue(Long.valueOf(dict.getDictKey()));
			return treeNode;
		}).collect(Collectors.toList());
		menuTree.addAll(dictTreeNode);
		//TODO 租户产品包未处理，如需处理需参考菜单的处理方式
		return ForestNodeMerger.merge(menuTree);
	}

	/**
	 * @param roleIds
	 * @return
	 */
	@Override
	public List<String> roleTreeKeys(String roleIds) {
		List<RoleAppMenu> roleMenus = roleAppMenuService.list(
			Wrappers.<RoleAppMenu>query().lambda()
				.in(RoleAppMenu::getRoleId, Func.toLongList(roleIds)));
		return roleMenus.stream().map(roleMenu -> Func.toStr(roleMenu.getAppMenuId()))
			.collect(Collectors.toList());
	}

	/**
	 * @param roleId
	 * @return
	 */
	@Override
	public List<AppMenuVO> menus(String roleId) {
		if (StringUtil.isBlank(roleId)) {
			return null;
		}
		List<AppMenu> allMenus = list();
		List<AppMenu> roleMenus;
		// 超级管理员并且不是顶部菜单请求则返回全部菜单
		if (AuthUtil.isAdministrator()) {
			roleMenus = allMenus;
		}
		// 非超级管理员请求返回对应角色权限菜单
		else {
			roleMenus = baseMapper.roleAppMenuByRoleId(Func.toLongList(roleId));
		}
		return buildRoutes(allMenus, roleMenus);
	}



	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveUserAppMenu(List<UserAppMenuEntity> data,Long userId) {
		userAppMenuService.remove(new LambdaQueryWrapper<UserAppMenuEntity>()
			.eq(UserAppMenuEntity::getCreateUser, userId));
		userAppMenuService.saveBatch(data);
	}

	private List<AppMenuVO> buildRoutes(List<AppMenu> allMenus, List<AppMenu> roleMenus) {
		List<AppMenu> routes = new LinkedList<>(roleMenus);
		roleMenus.forEach(roleMenu -> recursion(allMenus, routes, roleMenu));
		routes.sort(Comparator.comparing(AppMenu::getSort));
		AppMenuWrapper menuWrapper = new AppMenuWrapper();
		return menuWrapper.listVO(routes);
	}

	private void recursion(List<AppMenu> allMenus, List<AppMenu> routes, AppMenu roleMenu) {
		Optional<AppMenu> menu = allMenus.stream()
			.filter(x -> Func.equals(x.getId(), roleMenu.getParentId())).findFirst();
		if (menu.isPresent() && !routes.contains(menu.get())) {
			routes.add(menu.get());
			recursion(allMenus, routes, menu.get());
		}
	}

}
