package org.springblade.modules.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.modules.system.entity.LogOpt;
import org.springblade.modules.system.mapper.LogOptMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @since 2023/1/29 9:52
 */
@Service
public class LogOptService extends ServiceImpl<LogOptMapper, LogOpt> {

	public IPage<LogOpt> selectLogPage(IPage<LogOpt> page, Wrapper<LogOpt> wrapper) {
		return getBaseMapper().selectLogPage(page, wrapper);
	}


}
