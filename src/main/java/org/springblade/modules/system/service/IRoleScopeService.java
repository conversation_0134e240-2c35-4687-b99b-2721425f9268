/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.system.dto.ScopeDTO;
import org.springblade.modules.system.entity.RoleScope;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IRoleScopeService extends IService<RoleScope> {



	/**
	 * 查询角色下绑定
	 * @param roleIds 角色ID
	 * @return 角色对应的所有的数据权限ID
	 */
	List<Long> getScopeIdsByRoleIds(@Param("roleIds") List<Long> roleIds);


	/**
	 * 根据scopid查询对应绑定的角色和部门
	 * @param scopeIds scopeid
	 * @return 绑定的角色和部门
	 */
	List<ScopeDTO> getScopeDatasByScopeIds(Long menuId, List<Long> scopeIds);
}
