/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserHealthRecordsEntity;
import org.springblade.modules.system.excel.UserHealthRecordExcel;
import org.springblade.modules.system.mapper.UserHealthRecordsMapper;
import org.springblade.modules.system.service.IUserHealthRecordsService;
import org.springblade.modules.system.vo.UserHealthRecordsVO;
import org.springblade.modules.system.vo.UserHealthRecordsVO.Abnormal;
import org.springblade.modules.system.vo.UserHealthWeightStatsVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class UserHealthRecordsServiceImpl extends
	BaseServiceImpl<UserHealthRecordsMapper, UserHealthRecordsEntity> implements
	IUserHealthRecordsService {


	@Override
	public IPage<UserHealthRecordsVO> selectUserHealthRecordsPage(IPage<UserHealthRecordsVO> page,
		UserHealthRecordsVO userHealthRecords) {
		long total = baseMapper.selectUserHealthRecordsCount(userHealthRecords);
		page.setTotal(total);
		long start = page.getSize() * (page.getCurrent() - 1) + 1;
		long end = page.getSize() * page.getCurrent();
		List<UserHealthRecordsVO> records = baseMapper.selectUserHealthRecordsPage(start, end,
			userHealthRecords);
		records.forEach(item -> {
			List<String> deptNames = SysCache.getDeptNames(item.getDeptId());
			if (!deptNames.isEmpty()) {
				item.setDeptName(Joiner.on("/").join(deptNames));
			}
			if (item.getExaminationDate() != null) {
				item.setYear(DateUtil.fromDate(item.getExaminationDate()).getYear());
			}
			calculationAbnormal(item);
		});
		return page.setRecords(records);
	}

	/**
	 * @param userId
	 * @return
	 */
	@Override
	public UserHealthRecordsEntity getLastByUserId(Long userId) {
		return baseMapper.getLastByUserId(userId);
	}

	/**
	 * @param userHealthRecords
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean saveWeight(UserHealthRecordsEntity userHealthRecords) {
		removeCurrentWeekRecord(userHealthRecords.getUserId(),
			DateUtil.fromDate(userHealthRecords.getExaminationDate()).toLocalDate());
		UserHealthRecordsEntity detail = getLastByUserId(
			userHealthRecords.getUserId());
		if (detail != null) {
			userHealthRecords.setBloodPressureMin(detail.getBloodPressureMin());
			userHealthRecords.setBloodPressureMax(detail.getBloodPressureMax());
			userHealthRecords.setBloodPressureStatus(detail.getBloodPressureStatus());
			userHealthRecords.setBloodSugar(detail.getBloodSugar());
			userHealthRecords.setBloodSugarStatus(detail.getBloodSugarStatus());
			userHealthRecords.setBloodLipidsStatus(detail.getBloodLipidsStatus());
			userHealthRecords.setGlucose(detail.getGlucose());
			userHealthRecords.setTriglycerides(detail.getTriglycerides());
			userHealthRecords.setCholesterol(detail.getCholesterol());
			userHealthRecords.setHdl(detail.getHdl());
		}
		return save(userHealthRecords);
	}

	/**
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public UserHealthWeightStatsVO weightStats(LocalDate startDate, LocalDate endDate) {
		UserHealthWeightStatsVO res = baseMapper.weightStats(startDate, endDate);
		res.setUnEnrolled(res.getTotal() - res.getEnrolled());
		res.setNormal(res.getEnrolled() - res.getObesity() - res.getOverweight());
		return res;
	}

	/**
	 * @param data
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void importData(List<UserHealthRecordExcel> data, String type) {
		String tenantId = AuthUtil.getTenantId();
		StringBuilder err = new StringBuilder();
		List<UserHealthRecordsEntity> list = new ArrayList<>();
		data.stream().filter(excel -> StringUtils.isNotBlank(excel.getUserName()))
			.forEach(excel -> {
				User user = UserCache.getUser(tenantId, excel.getUserName());
				if (user == null) {
					err.append("用户不存在：").append(excel.getUserName()).append(";");
				}
				UserHealthRecordsEntity record = new UserHealthRecordsEntity();
				record.setType(type);
				record.setUserId(user != null ? user.getId() : null);
				record.setExaminationDate(excel.getExaminationDate());
				if (StringUtils.isBlank(excel.getWeight())) {
					err.append("体重未登记：").append(excel.getUserName()).append(";");
				}
				if (StringUtils.isBlank(excel.getHeight())) {
					err.append("身高未登记：").append(excel.getUserName()).append(";");
				}
				if (StringUtils.isBlank(excel.getBloodPressureMax()) || StringUtils.isBlank(
					excel.getBloodPressureMin())) {
					err.append("血压未登记：").append(excel.getUserName()).append(";");
				}
				try {
					record.setWeight(Float.valueOf(excel.getWeight()));
					record.setHeight(Float.valueOf(excel.getHeight()));
					record.setBloodPressureMax(Integer.valueOf(excel.getBloodPressureMax()));
					record.setBloodPressureMin(Integer.valueOf(excel.getBloodPressureMin()));
					if (StringUtils.isNotBlank(excel.getBloodSugar())) {
						record.setBloodSugar(Float.valueOf(excel.getBloodSugar()));
					}
					if (StringUtils.isNotBlank(excel.getGlucose())) {
						record.setGlucose(Float.valueOf(excel.getGlucose()));
					}
					if (StringUtils.isNotBlank(excel.getTriglycerides())) {
						record.setTriglycerides(Float.valueOf(excel.getTriglycerides()));
					}
					if (StringUtils.isNotBlank(excel.getCholesterol())) {
						record.setCholesterol(Float.valueOf(excel.getCholesterol()));
					}
					if (StringUtils.isNotBlank(excel.getHdl())) {
						record.setHdl(Float.valueOf(excel.getHdl()));
					}
				} catch (Exception e) {
					err.append("体重/身高/血压字段中存在非数字的数据：")
						.append(excel.getUserName())
						.append(";");
				}
				list.add(record);
			});
		if (err.length() > 0) {
			throw new RuntimeException(err.toString());
		}
		list.forEach(record -> {
			record.setBmi(BigDecimal.valueOf(record.getWeight()).divide(
					BigDecimal.valueOf(record.getHeight())
						.multiply(BigDecimal.valueOf(record.getHeight()))
						.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP), 2,
					RoundingMode.HALF_UP)
				.floatValue());
			record.setWeightStatus(calculationWeightStatus(record.getBmi()));
			record.setBloodPressureStatus(
				calculationBloodPressureStatus(record.getBloodPressureMax(),
					record.getBloodPressureMin()));
			if (record.getBloodSugar() != null) {
				record.setBloodSugarStatus(calculationBloodSugarStatus(record.getBloodSugar()));
			} else {
				record.setBloodSugarStatus(UserHealthRecordsEntity.BLOOD_SUGAR_STATUS_NORMAL);
			}
			if (record.getTriglycerides() != null && record.getCholesterol() != null
				&& record.getHdl() != null) {
				record.setBloodLipidsStatus(
					calculationBloodLipidsStatus(record.getTriglycerides(),
						record.getCholesterol(),
						record.getHdl()));
			}
		});
		saveOrUpdateBatch(list);
	}

	/**
	 * @param page
	 * @param userHealthRecords
	 * @return
	 */
	@Override
	public IPage<UserHealthRecordsVO> selectUserHealthRecordsPage1(IPage<UserHealthRecordsVO> page,
		UserHealthRecordsVO userHealthRecords) {
		List<UserHealthRecordsVO> records = baseMapper.selectUserHealthRecordsPage1(page,
			userHealthRecords);
		records.forEach(item -> {
			if (item.getDeptId() != null) {
				List<String> deptNames = SysCache.getDeptNames(item.getDeptId());
				if (!deptNames.isEmpty()) {
					item.setDeptName(Joiner.on("/").join(deptNames));
				}
			}
			calculationAbnormal(item);
		});
		return page.setRecords(records);
	}

	private String calculationBloodLipidsStatus(Float triglycerides, Float cholesterol, Float hdl) {
		if (triglycerides > 1.7 || cholesterol > 5.18 || hdl < 1.04) {
			return UserHealthRecordsEntity.BLOOD_LIPIDS_STATUS_HIGH;
		} else {
			return UserHealthRecordsEntity.BLOOD_LIPIDS_STATUS_NORMAL;
		}
	}

	private String calculationBloodSugarStatus(Float bloodSugar) {
		if (bloodSugar >= 7) {
			return UserHealthRecordsEntity.BLOOD_SUGAR_STATUS_HIGH;
		} else if (bloodSugar <= 3.9) {
			return UserHealthRecordsEntity.BLOOD_SUGAR_STATUS_LOW;
		} else {
			return UserHealthRecordsEntity.BLOOD_SUGAR_STATUS_NORMAL;
		}
	}

	private String calculationBloodPressureStatus(Integer bloodPressureMax,
		Integer bloodPressureMin) {
		if (bloodPressureMax < 90 || bloodPressureMin < 60) {
			return UserHealthRecordsEntity.BLOOD_PRESSURE_STATUS_LOW;
		} else if (bloodPressureMax >= 140 || bloodPressureMin >= 90) {
			return UserHealthRecordsEntity.BLOOD_PRESSURE_STATUS_HIGH;
		} else {
			return UserHealthRecordsEntity.BLOOD_PRESSURE_STATUS_NORMAL;
		}
	}

	private String calculationWeightStatus(Float bmi) {
		if (bmi < 18.5) {
			return UserHealthRecordsEntity.WEIGHT_STATUS_THINNESS;
		} else if (bmi < 24) {
			return UserHealthRecordsEntity.WEIGHT_STATUS_NORMAL;
		} else if (bmi >= 28) {
			return UserHealthRecordsEntity.WEIGHT_STATUS_OBESITY;
		} else {
			return UserHealthRecordsEntity.WEIGHT_STATUS_OVERWEIGHT;
		}
	}

	private void removeCurrentWeekRecord(Long userId, LocalDate examinationDate) {
		remove(Wrappers.<UserHealthRecordsEntity>lambdaQuery()
			.eq(UserHealthRecordsEntity::getUserId, userId)
			.eq(UserHealthRecordsEntity::getType, UserHealthRecordsEntity.TYPE_WEIGHT)
			.between(UserHealthRecordsEntity::getExaminationDate,
				examinationDate.with(DayOfWeek.MONDAY),
				LocalDate.now()));
	}

	/**
	 * 计算异常
	 *
	 * @param item
	 */
	private void calculationAbnormal(UserHealthRecordsVO item) {
		item.setTags(new ArrayList<>());
		List<Consumer<UserHealthRecordsVO>> typeOperations = ImmutableList.<Consumer<UserHealthRecordsVO>>builder()
			.add(this::bmiAbnormal)
			.add(this::bloodPressureAbnormal)
			.add(this::bloodSugarAbnormal)
			.add(this::bloodLipidsAbnormal)
			.build();
		typeOperations.forEach((operation) -> operation.accept(item));
	}

	/**
	 * 血脂异常
	 *
	 * @param item
	 */
	private void bloodLipidsAbnormal(UserHealthRecordsVO item) {
		if (UserHealthRecordsEntity.BLOOD_LIPIDS_STATUS_HIGH.equals(item.getBloodLipidsStatus())) {
			item.getTags().add(Abnormal.HYPERLIPIDEMIA);
		}

	}

	/**
	 * 血糖异常
	 *
	 * @param item
	 */
	private void bloodSugarAbnormal(UserHealthRecordsVO item) {
		if (UserHealthRecordsEntity.BLOOD_SUGAR_STATUS_HIGH.equals(item.getBloodSugarStatus())) {
			item.getTags().add(Abnormal.HYPERGLYCEMIA);
		}

	}

	/**
	 * 血压异常
	 *
	 * @param item
	 */
	private void bloodPressureAbnormal(UserHealthRecordsVO item) {
		if (UserHealthRecordsEntity.BLOOD_PRESSURE_STATUS_HIGH.equals(
			item.getBloodPressureStatus())) {
			item.getTags().add(Abnormal.HYPERTENSION);
		} else if (UserHealthRecordsEntity.BLOOD_PRESSURE_STATUS_LOW.equals(
			item.getBloodPressureStatus())) {
			item.getTags().add(Abnormal.LOW_BLOOD_PRESSURE);
		}
	}

	/**
	 * 根据bmi计算是否肥胖
	 *
	 * @param item
	 */
	private void bmiAbnormal(UserHealthRecordsVO item) {
		//偏瘦
		if (UserHealthRecordsEntity.WEIGHT_STATUS_THINNESS.equals(item.getWeightStatus())) {
			item.getTags().add(Abnormal.THINNESS);
			//超重
		} else if (UserHealthRecordsEntity.WEIGHT_STATUS_OVERWEIGHT.equals(
			item.getWeightStatus())) {
			item.getTags().add(Abnormal.OVERWEIGHT);
			//肥胖
		} else if (UserHealthRecordsEntity.WEIGHT_STATUS_OBESITY.equals(item.getWeightStatus())) {
			item.getTags().add(Abnormal.OBESITY);
		}

	}

}
