package org.springblade.modules.system.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.keyvalue.AbstractKeyValue;
import org.apache.commons.collections4.keyvalue.DefaultKeyValue;
import org.quartz.CronScheduleBuilder;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.quartz.TriggerUtils;
import org.quartz.impl.matchers.GroupMatcher;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.modules.system.service.IJobService;
import org.springblade.modules.system.vo.JobVO;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class JobServiceImpl implements IJobService {

	private final Scheduler scheduler;
	private final SchedulerFactoryBean schedulerFactoryBean;

	@SneakyThrows
	@Override
	public boolean addJob(JobVO entity) {
		String clazzName = entity.getJobClazz();
		String jobName = entity.getJobName();
		String jobGroup = entity.getJobGroup();
		String cron = entity.getCron();
		String description = entity.getDescription();

		JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
		if (scheduler.checkExists(jobKey)) {
			throw new ServiceException("该定时任务已存在：" + jobKey.getName());
		}
		Class<? extends Job> jobClass = null;
		try {
			jobClass = (Class<? extends Job>) Class.forName(clazzName);
		} catch (ClassNotFoundException e) {
			throw new ServiceException("找不到任务类：" + clazzName);
		}
		JobDataMap jobDataMap = new JobDataMap();
		if (entity.getJobData() != null) {
			jobDataMap.putAll(entity.getJobData().stream()
				.collect(Collectors.toMap(AbstractKeyValue::getKey, AbstractKeyValue::getValue)));
		}
		Scheduler scheduler = schedulerFactoryBean.getScheduler();
		JobDetail jobDetail = JobBuilder.newJob(jobClass)
			.withIdentity(jobName, jobGroup)
			.usingJobData(jobDataMap)
			.withDescription(description)
			.build();

		CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cron);
		String triggerId = jobKey.getGroup() + jobKey.getName();
		Trigger trigger = TriggerBuilder.newTrigger()
			.withSchedule(scheduleBuilder)
			.withIdentity(triggerId)
			.withDescription(description)
			.build();
		scheduler.scheduleJob(jobDetail, trigger);

		if (!scheduler.isShutdown()) {
			scheduler.start();
		}
		return true;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean updateJob(JobVO param) throws SchedulerException {
		deleteJob(param);
		return addJob(param);
	}

	/**
	 * @param param
	 * @throws SchedulerException
	 */
	@Override
	public void pauseJob(JobVO param) throws SchedulerException {
		String jobName = param.getJobName();
		String jobGroup = param.getJobGroup();

		JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
		checkJobExist(jobKey);

		scheduler.pauseJob(jobKey);
	}

	/**
	 * @param param
	 * @throws SchedulerException
	 */
	@Override
	public void resumeJob(JobVO param) throws SchedulerException {
		String jobName = param.getJobName();
		String jobGroup = param.getJobGroup();

		JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
		checkJobExist(jobKey);

		scheduler.resumeJob(jobKey);
	}

	/**
	 * @param param
	 * @throws SchedulerException
	 */
	@Override
	public void deleteJob(JobVO param) throws SchedulerException {
		String jobName = param.getJobName();
		String jobGroup = param.getJobGroup();

		JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
		String triggerId = jobKey.getGroup() + jobKey.getName();

		checkJobExist(jobKey);
		// 先暂停再删除
		scheduler.pauseTrigger(TriggerKey.triggerKey(triggerId));
		scheduler.unscheduleJob(TriggerKey.triggerKey(triggerId));
		scheduler.deleteJob(jobKey);
	}

	/**
	 * @return
	 * @throws SchedulerException
	 */
	@Override
	public List<JobVO> jobList() throws SchedulerException {
		GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
		List<JobVO> jobDtoList = new ArrayList<>();
		for (JobKey jobKey : scheduler.getJobKeys(matcher)) {
			JobVO jobDto = getJobDtoByJobKey(jobKey);
			jobDtoList.add(jobDto);
		}
		return jobDtoList;
	}


	/**
	 * @param param
	 * @return
	 * @throws SchedulerException
	 */
	@Override
	public JobVO jobDetail(JobVO param) throws SchedulerException {
		String jobName = param.getJobName();
		String jobGroup = param.getJobGroup();
		JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
		return getJobDtoByJobKey(jobKey);
	}

	@SneakyThrows
	private void checkJobExist(JobKey jobKey) {
		if (!scheduler.checkExists(jobKey)) {
			throw new ServiceException("该定时任务不存在：" + jobKey.getName());
		}
	}

	public JobVO getJobDtoByJobKey(JobKey jobKey) throws SchedulerException {
		JobDetail jobDetail = scheduler.getJobDetail(jobKey);
		List<Trigger> triggerList = (List<Trigger>) scheduler.getTriggersOfJob(jobKey);
		JobVO jobDto = new JobVO();
		jobDto.setJobClazz(jobDetail.getJobClass().getName());
		jobDto.setJobName(jobKey.getName());
		jobDto.setJobGroup(jobKey.getGroup());
		Map<String, Object> map = jobDetail.getJobDataMap().getWrappedMap();
		jobDto.setJobData(map.entrySet().stream()
			.map(entry -> new DefaultKeyValue<>(entry.getKey(),
				(String) entry.getValue())).collect(Collectors.toList()));
		//取第一个
		Trigger trigger = triggerList.get(0);
		if (trigger instanceof CronTriggerImpl) {
			CronTriggerImpl cronTriggerImpl = (CronTriggerImpl) trigger;
			String cronExpression = cronTriggerImpl.getCronExpression();
			jobDto.setCron(cronExpression);

			// 最近10次的触发时间
			List<Date> dates = TriggerUtils.computeFireTimes(cronTriggerImpl, null, 10);
			jobDto.setRecentFireTimeList(dates);
		}
		jobDto.setDescription(trigger.getDescription());
		Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
		jobDto.setTriggerState(triggerState.toString());
		return jobDto;
	}


}
