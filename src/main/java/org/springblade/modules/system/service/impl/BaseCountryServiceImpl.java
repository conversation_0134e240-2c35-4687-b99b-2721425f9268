/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import org.springblade.modules.system.entity.BaseCountryEntity;
import org.springblade.modules.system.vo.BaseCountryVO;
import org.springblade.modules.system.mapper.BaseCountryMapper;
import org.springblade.modules.system.service.IBaseCountryService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 国家 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-01
 */
@Service
public class BaseCountryServiceImpl extends BaseServiceImpl<BaseCountryMapper, BaseCountryEntity> implements IBaseCountryService {

	@Override
	public IPage<BaseCountryVO> selectBaseCountryPage(IPage<BaseCountryVO> page, BaseCountryVO baseCountry) {
		return page.setRecords(baseMapper.selectBaseCountryPage(page, baseCountry));
	}


}
