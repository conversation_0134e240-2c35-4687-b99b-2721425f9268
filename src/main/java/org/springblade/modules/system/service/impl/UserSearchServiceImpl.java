/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;

import java.util.*;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.SysCache;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserInfo;
import org.springblade.modules.system.mapper.DeptMapper;
import org.springblade.modules.system.mapper.RoleMapper;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserSearchService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.plugin.workflow.core.utils.WfTaskUtil;
import org.springframework.stereotype.Service;

/**
 * 用户查询服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserSearchServiceImpl extends BaseServiceImpl<UserMapper, User> implements
	IUserSearchService {

	private final IDeptService deptService;

	private final RoleMapper roleMapper;

	private final DeptMapper deptMapper;

	private final IUserService userService;

	/**
	 * 组织机构中公司的id
	 */
	private final Long COMPANY_ID = 1123598813738675201L;


	@Override
	public List<User> listByUser(List<Long> userId) {
		return this.list(Wrappers.<User>lambdaQuery().in(User::getId, userId));
	}

	@Override
	public List<User> listByDept(List<Long> deptId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		deptId.forEach(id -> queryWrapper.like(User::getDeptId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByPost(List<Long> postId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		postId.forEach(id -> queryWrapper.like(User::getPostId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByRole(List<Long> roleId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		roleId.forEach(id -> queryWrapper.like(User::getRoleId, id).or());
		return this.list(queryWrapper);
	}

	/**
	 * 获取上级部门领导
	 *
	 * @param userId
	 * @return
	 */
	@Override
	public List<User> deptLeaderListByUser(Long userId) {
		User user = this.getById(userId);
		if (user == null) {
			return null;
		}
		System.err.println("获取上级部门领导:用户=>" + user.getName());
		if (user.getDeptId() != null) {
			List<User> res = new ArrayList<>();
			Func.toLongList(user.getDeptId()).forEach(deptId -> {
				Dept dept = SysCache.getDept(deptId);
				Preconditions.checkState(dept.getLeaderId() != null,
					"用户所在部门未设置部门主管=>" + dept.getDeptName());
				System.err.println("获取上级部门领导:所在部门=>" + dept.getDeptName());
				User leader = this.getById(dept.getLeaderId());
				Preconditions.checkState(leader != null,
					"获取上级部门领导失败，请检查员工的部门配置");
				System.err.println("获取上级部门领导:部门领导=>" + leader.getName());
				res.add(leader);
			});
			return res;
		}

		return null;
	}


	@Override
	public List<User> findUserByRole(Long taskUserId, String deptId, String roleNames) {

		//两个角色，第一个代表要递归向上查询的角色，第二个代表查询到顶级部门之后，就直接全局按照第二个角色查找
		//如果没有第二个角色的，就一直递归到最上层
		String[] splitRole = roleNames.split(",");

		String firstRole = splitRole[0];

		String secondRole = null;
		if (splitRole.length > 1) {
			secondRole = splitRole[1];
		}

		//查询指定角色的角色id
		Long roleId = roleMapper.selectRoleIdByRoleName(firstRole);
		Objects.requireNonNull(roleId, "找不到对应角色");

		//根据部门和角色找出来对应的用户
		List<User> userList = this.baseMapper.getUserListByRoleAndDept(deptId, String.valueOf(roleId));

		long isExist = userList.stream()
			.filter(item -> item.getId().equals(taskUserId))
			.count();
		//在指定的部门没有找到用户，那就继续向上级找
		//在当前部门能找到指定角色的用户，要判断taskUserId是否也在这批用户里面，如果taskUserId也在这批用户，要查询上一级部门再找
		if (userList.isEmpty() || isExist != 0) {

			if (StringUtils.isBlank(deptId)) {
				return null;
			}
			Long parentId = deptMapper.getParentIdByDeptId(Long.valueOf(deptId));

			if (Objects.isNull(parentId) || Objects.equals(0L, parentId)) {
				return null;
			}

			if (Objects.nonNull(secondRole) && Objects.equals(parentId, COMPANY_ID)) {

				//根据部门和角色找出来对应的用户
				return this.baseMapper.getUserListByRoleName(secondRole);

			}

			return this.findUserByRole(taskUserId, String.valueOf(parentId), roleNames);
		}

		return userList;
	}



	@Override
	public List<User> recursionGetLeaderListByDeptId(Long taskUserId, String deptId) {

		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		if (Objects.isNull(dept)) {
			return null;
		}
		//如果是同一个人的话，那就继续找上级
		if (Objects.equals(taskUserId, dept.getLeaderId())) {
			return this.recursionGetLeaderListByDeptId(taskUserId, String.valueOf(dept.getParentId()));
		}
		return Collections.singletonList(this.getById(dept.getLeaderId()));
	}

	@Override
	public List<User> recursionGetLeaderHighest(Long taskUserId, String nouseDeptId, String paramValue) {
		String[] split = paramValue.split(",");
		String userId = split[0];
		String deptId = split[1];

		if(Objects.isNull(deptId)){
			return null;
		}
		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		if(Objects.isNull(dept)){
			return null;
		}
		if(dept.getDeptCategory() > 2){
			return this.recursionGetLeaderHighest(taskUserId, nouseDeptId, paramValue);
		}else{
			if(!Objects.equals(userId, dept.getLeaderId())){
				return Collections.singletonList(this.getById(dept.getLeaderId()));
			}else{
				Dept searchDept = new Dept();
				searchDept.setDeptCategory(1);
				Dept finalDept = deptService.getOne(Condition.getQueryWrapper(searchDept));
				return Collections.singletonList(this.getById(finalDept.getLeaderId()));
			}

		}
	}


}
