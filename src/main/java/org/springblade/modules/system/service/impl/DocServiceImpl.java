/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.natergy.ni.base.entity.DepotInfo;
import com.natergy.ni.common.constant.NiConstant;
import java.util.Date;
import java.util.List;
import org.springblade.modules.system.entity.DocEntity;
import org.springblade.modules.system.vo.DocVO;
import org.springblade.modules.system.mapper.DocMapper;
import org.springblade.modules.system.service.IDocService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 系统管理-操作手册 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Service
public class DocServiceImpl extends BaseServiceImpl<DocMapper, DocEntity> implements IDocService {

	@Override
	public IPage<DocVO> selectDocPage(IPage<DocVO> page, DocVO doc) {
		return page.setRecords(baseMapper.selectDocPage(page, doc));
	}

	/**
	 * @param ids
	 * @return
	 */
	@Override
	public boolean submit(List<Long> ids) {
		UpdateWrapper<DocEntity> wrapper = new UpdateWrapper<>();
		wrapper.lambda().in(DocEntity::getStatus, NiConstant.STATUS_DRAFT,
				NiConstant.STATUS_BACK)
			.in(DocEntity::getId, ids)
			.set(DocEntity::getStatus, NiConstant.STATUS_SUBMIT)
			.set(DocEntity::getApplyTime, new Date());
		return update(wrapper);
	}


}
