package org.springblade.modules.system.service;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.common.cache.UserCache;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserSecPwd;
import org.springblade.modules.system.mapper.UserSecPwdMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/1/29 9:52
 */
@Service
public class UserSecPwdService extends ServiceImpl<UserSecPwdMapper, UserSecPwd> {

    public UserSecPwd getByUserId(Long userId) {
        return getOne(Wrappers.lambdaQuery(UserSecPwd.class).eq(UserSecPwd::getUserId, userId));
    }


    public String getSecretKey(Long userId) {
        UserSecPwd secPwd = getByUserId(userId);
        if (secPwd == null) {

        }
        return null;
    }

    public boolean updatePassword(Long userId, String newPassword,
                                  String newPassword1) {
        if (!newPassword.equals(newPassword1)) {
            throw new ServiceException("请输入正确的确认密码!");
        }
        long count = count(
                Wrappers.<UserSecPwd>lambdaQuery().eq(UserSecPwd::getUserId, userId));
        if (count > 0) {
            return this.update(
                    Wrappers.<UserSecPwd>update().lambda().set(UserSecPwd::getSecPassword, DigestUtil.hex(newPassword))
                            .eq(UserSecPwd::getUserId, userId));
        } else {
            UserSecPwd secPwd = new UserSecPwd();
            secPwd.setUserId(userId);
            secPwd.setSecPassword(DigestUtil.hex(newPassword));
            return this.save(secPwd);
        }
    }

    public Boolean checkPwd(String secret, Long userId) {
        UserSecPwd secPwd = getOne(Wrappers.lambdaQuery(UserSecPwd.class).eq(UserSecPwd::getUserId, userId));
        String pwd = null;
        if (secPwd == null) {
            User user = UserCache.getUser(userId);
            if (user != null) {
                String idCard = user.getIdCard();
                if (StringUtils.isBlank(idCard)) {
                    return false;
                }
                pwd = DigestUtil.hex(DigestUtil.md5Hex(idCard.toLowerCase()));
            } else {
                return false;
            }
        } else {
            pwd = secPwd.getSecPassword();
        }
        return pwd.equals(DigestUtil.hex(secret));

    }
}
