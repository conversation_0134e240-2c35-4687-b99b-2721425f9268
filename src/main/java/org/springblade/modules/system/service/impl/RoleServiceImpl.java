/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import static org.springblade.common.constant.CommonConstant.API_SCOPE_CATEGORY;
import static org.springblade.common.constant.CommonConstant.DATA_SCOPE_CATEGORY;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.natergy.ni.chat.entity.RoleChat;
import com.natergy.ni.chat.service.IRoleChatService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.Role;
import org.springblade.modules.system.entity.RoleAppMenu;
import org.springblade.modules.system.entity.RoleMenu;
import org.springblade.modules.system.entity.RoleScope;
import org.springblade.modules.system.mapper.RoleMapper;
import org.springblade.modules.system.service.IRoleAppMenuService;
import org.springblade.modules.system.service.IRoleMenuService;
import org.springblade.modules.system.service.IRoleScopeService;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.vo.RoleVO;
import org.springblade.modules.system.wrapper.RoleWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@AllArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements IRoleService {

  private final IRoleMenuService roleMenuService;
  private final IRoleAppMenuService roleAppMenuService;

  private final IRoleScopeService roleScopeService;
  private final IRoleChatService roleChatService;

  @Override
  public IPage<RoleVO> selectRolePage(IPage<RoleVO> page, RoleVO role) {
    return page.setRecords(baseMapper.selectRolePage(page, role));
  }

  @Override
  public List<RoleVO> tree(String tenantId) {
    String userRole = AuthUtil.getUserRole();
    String excludeRole = null;
    if (!CollectionUtil.contains(Func.toStrArray(userRole), RoleConstant.ADMIN)
        && !CollectionUtil.contains(Func.toStrArray(userRole), RoleConstant.ADMINISTRATOR)) {
      excludeRole = RoleConstant.ADMIN;
    }
    return ForestNodeMerger.merge(baseMapper.tree(tenantId, excludeRole));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean grant(@NotEmpty List<Long> roleIds, List<Long> menuIds, List<Long> appMenuIds,
      List<Long> dataScopeIds, List<Long> apiScopeIds, List<Long> chatIds) {
    return grantRoleMenu(roleIds, menuIds) && grantRoleAppMenu(roleIds, appMenuIds)
        && grantDataScope(roleIds, dataScopeIds)
        && grantApiScope(roleIds, apiScopeIds)
        && grantRoleChat(roleIds, chatIds);
  }

  private boolean grantRoleChat(List<Long> roleIds, List<Long> chatIds) {
    // 防止越权配置超管角色
    checkRoleUnauthorizedAdmin(roleIds);
    // 删除角色配置的菜单集合
    roleChatService.remove(
        Wrappers.<RoleChat>update().lambda().in(RoleChat::getRoleId, roleIds));
    // 组装配置
    List<RoleChat> roleChats = new ArrayList<>();
    roleIds.forEach(roleId -> chatIds.forEach(chatId -> {
      RoleChat roleChat = new RoleChat();
      roleChat.setRoleId(roleId);
      roleChat.setChatId(chatId);
      roleChats.add(roleChat);
    }));
    // 新增配置
    roleChatService.saveBatch(roleChats);
    // 递归设置下属角色菜单集合
    recursionRoleChat(roleIds, chatIds);
    return true;
  }

  private void recursionRoleChat(List<Long> roleIds, List<Long> chatIds) {
    roleIds.forEach(roleId -> baseMapper.selectList(
        Wrappers.<Role>query().lambda().eq(Role::getParentId, roleId)).forEach(role -> {
      List<RoleChat> roleChatList = roleChatService.list(
          Wrappers.<RoleChat>query().lambda().eq(RoleChat::getRoleId, role.getId()));
      // 子节点过滤出父节点删除的菜单集合
      List<Long> collectRoleChatIds = roleChatList.stream().map(RoleChat::getChatId)
          .filter(chatId -> !chatIds.contains(chatId)).collect(Collectors.toList());
      if (!collectRoleChatIds.isEmpty()) {
        // 删除子节点权限外的菜单集合
        roleChatService.remove(
            Wrappers.<RoleChat>update().lambda().eq(RoleChat::getRoleId, role.getId())
                .in(RoleChat::getChatId, collectRoleChatIds));
        // 递归设置下属角色菜单集合
        recursionRoleAppMenu(Collections.singletonList(role.getId()), chatIds);
      }
    }));
  }

  private boolean grantRoleAppMenu(List<Long> roleIds, List<Long> appMenuIds) {
    // 防止越权配置超管角色
    checkRoleUnauthorizedAdmin(roleIds);
    // 删除角色配置的菜单集合
    roleAppMenuService.remove(
        Wrappers.<RoleAppMenu>update().lambda().in(RoleAppMenu::getRoleId, roleIds));
    // 组装配置
    List<RoleAppMenu> roleMenus = new ArrayList<>();
    roleIds.forEach(roleId -> appMenuIds.forEach(menuId -> {
      RoleAppMenu roleMenu = new RoleAppMenu();
      roleMenu.setRoleId(roleId);
      roleMenu.setAppMenuId(menuId);
      roleMenus.add(roleMenu);
    }));
    // 新增配置
    roleAppMenuService.saveBatch(roleMenus);
    // 递归设置下属角色菜单集合
    recursionRoleAppMenu(roleIds, appMenuIds);
    return true;
  }

  private void recursionRoleAppMenu(List<Long> roleIds, List<Long> appMenuIds) {
    roleIds.forEach(roleId -> baseMapper.selectList(
        Wrappers.<Role>query().lambda().eq(Role::getParentId, roleId)).forEach(role -> {
      List<RoleAppMenu> roleMenuList = roleAppMenuService.list(
          Wrappers.<RoleAppMenu>query().lambda().eq(RoleAppMenu::getRoleId, role.getId()));
      // 子节点过滤出父节点删除的菜单集合
      List<Long> collectRoleMenuIds = roleMenuList.stream().map(RoleAppMenu::getAppMenuId)
          .filter(menuId -> !appMenuIds.contains(menuId)).collect(Collectors.toList());
      if (collectRoleMenuIds.size() > 0) {
        // 删除子节点权限外的菜单集合
        roleAppMenuService.remove(
            Wrappers.<RoleAppMenu>update().lambda().eq(RoleAppMenu::getRoleId, role.getId())
                .in(RoleAppMenu::getAppMenuId, collectRoleMenuIds));
        // 递归设置下属角色菜单集合
        recursionRoleAppMenu(Collections.singletonList(role.getId()), appMenuIds);
      }
    }));
  }

  private boolean grantRoleMenu(List<Long> roleIds, List<Long> menuIds) {
    // 防止越权配置超管角色
    checkRoleUnauthorizedAdmin(roleIds);
    // 删除角色配置的菜单集合
    roleMenuService.remove(Wrappers.<RoleMenu>update().lambda().in(RoleMenu::getRoleId, roleIds));
    // 组装配置
    List<RoleMenu> roleMenus = new ArrayList<>();
    roleIds.forEach(roleId -> menuIds.forEach(menuId -> {
      RoleMenu roleMenu = new RoleMenu();
      roleMenu.setRoleId(roleId);
      roleMenu.setMenuId(menuId);
      roleMenus.add(roleMenu);
    }));
    // 新增配置
    roleMenuService.saveBatch(roleMenus);
    // 递归设置下属角色菜单集合
    recursionRoleMenu(roleIds, menuIds);
    return true;
  }

  private void recursionRoleMenu(List<Long> roleIds, List<Long> menuIds) {
    roleIds.forEach(roleId -> baseMapper.selectList(
        Wrappers.<Role>query().lambda().eq(Role::getParentId, roleId)).forEach(role -> {
      List<RoleMenu> roleMenuList = roleMenuService.list(
          Wrappers.<RoleMenu>query().lambda().eq(RoleMenu::getRoleId, role.getId()));
      // 子节点过滤出父节点删除的菜单集合
      List<Long> collectRoleMenuIds = roleMenuList.stream().map(RoleMenu::getMenuId)
          .filter(menuId -> !menuIds.contains(menuId)).collect(Collectors.toList());
      if (collectRoleMenuIds.size() > 0) {
        // 删除子节点权限外的菜单集合
        roleMenuService.remove(
            Wrappers.<RoleMenu>update().lambda().eq(RoleMenu::getRoleId, role.getId())
                .in(RoleMenu::getMenuId, collectRoleMenuIds));
        // 递归设置下属角色菜单集合
        recursionRoleMenu(Collections.singletonList(role.getId()), menuIds);
      }
    }));
  }

  private boolean grantDataScope(List<Long> roleIds, List<Long> dataScopeIds) {
    // 删除角色配置的数据权限集合
    roleScopeService.remove(
        Wrappers.<RoleScope>update().lambda().eq(RoleScope::getScopeCategory, DATA_SCOPE_CATEGORY)
            .in(RoleScope::getRoleId, roleIds));
    // 组装配置
    List<RoleScope> roleDataScopes = new ArrayList<>();
    roleIds.forEach(roleId -> dataScopeIds.forEach(scopeId -> {
      RoleScope roleScope = new RoleScope();
      roleScope.setScopeCategory(DATA_SCOPE_CATEGORY);
      roleScope.setRoleId(roleId);
      roleScope.setScopeId(scopeId);
      roleDataScopes.add(roleScope);
    }));
    // 新增配置
    roleScopeService.saveBatch(roleDataScopes);
    return true;
  }

  private boolean grantApiScope(List<Long> roleIds, List<Long> apiScopeIds) {
    // 删除角色配置的接口权限集合
    roleScopeService.remove(
        Wrappers.<RoleScope>update().lambda().eq(RoleScope::getScopeCategory, API_SCOPE_CATEGORY)
            .in(RoleScope::getRoleId, roleIds));
    // 组装配置
    List<RoleScope> roleApiScopes = new ArrayList<>();
    roleIds.forEach(roleId -> apiScopeIds.forEach(scopeId -> {
      RoleScope roleScope = new RoleScope();
      roleScope.setScopeCategory(API_SCOPE_CATEGORY);
      roleScope.setScopeId(scopeId);
      roleScope.setRoleId(roleId);
      roleApiScopes.add(roleScope);
    }));
    // 新增配置
    roleScopeService.saveBatch(roleApiScopes);
    return true;
  }

  @Override
  public String getRoleIds(String tenantId, String roleNames) {
    List<Role> roleList = baseMapper.selectList(
        Wrappers.<Role>query().lambda().eq(Role::getTenantId, tenantId)
            .in(Role::getRoleName, Func.toStrList(roleNames)));
    if (roleList != null && roleList.size() > 0) {
      return roleList.stream().map(role -> Func.toStr(role.getId())).distinct()
          .collect(Collectors.joining(","));
    }
    return null;
  }

  @Override
  public List<String> getRoleNames(String roleIds) {
    return baseMapper.getRoleNames(Func.toLongArray(roleIds));
  }

  @Override
  public List<String> getRoleAliases(String roleIds) {
    return baseMapper.getRoleAliases(Func.toLongArray(roleIds));
  }

  @Override
  public boolean submit(Role role) {
    if (!AuthUtil.isAdministrator()) {
      if (Func.toStr(role.getRoleAlias()).equals(RoleConstant.ADMINISTRATOR)) {
        throw new ServiceException("无权限创建超管角色！");
      }
    }
    if (Func.isEmpty(role.getParentId())) {
      role.setTenantId(AuthUtil.getTenantId());
      role.setParentId(BladeConstant.TOP_PARENT_ID);
    }
    if (role.getParentId() > 0) {
      Role parent = getById(role.getParentId());
      if (Func.toLong(role.getParentId()) == Func.toLong(role.getId())) {
        throw new ServiceException("父节点不可选择自身!");
      }
      role.setTenantId(parent.getTenantId());
    }
    role.setIsDeleted(BladeConstant.DB_NOT_DELETED);
    return saveOrUpdate(role);
  }

  @Override
  public List<RoleVO> search(String roleName, Long parentId) {
    String tenantId = AuthUtil.getTenantId();
    LambdaQueryWrapper<Role> queryWrapper = Wrappers.<Role>query().lambda();
    if (Func.isNotEmpty(roleName)) {
      queryWrapper.like(Role::getRoleName, roleName);
    }
    if (Func.isNotEmpty(parentId) && parentId > 0L) {
      queryWrapper.eq(Role::getParentId, parentId);
    }
    if (Func.isNotEmpty(tenantId)) {
      queryWrapper.eq(Role::getTenantId, tenantId);
    }
    List<Role> roleList = baseMapper.selectList(queryWrapper);
    return RoleWrapper.build().listNodeVO(roleList);
  }

  @Override
  public boolean removeRole(String ids) {
    Long cnt = baseMapper.selectCount(
        Wrappers.<Role>query().lambda().in(Role::getParentId, Func.toLongList(ids)));
    if (cnt > 0L) {
      throw new ServiceException("请先删除子节点!");
    }
    return removeByIds(Func.toLongList(ids));
  }

  /**
   * 检查角色是否越权配置管理员角色
   *
   * @param roleIds 角色id集合
   */
  @Override
  public void checkRoleUnauthorizedAdmin(List<Long> roleIds) {
    // 防止越权配置超管角色
    long administratorCount = this.count(
        Wrappers.<Role>query().lambda().eq(Role::getRoleAlias, RoleConstant.ADMINISTRATOR)
            .in(Role::getId, roleIds));
    if (!AuthUtil.isAdministrator() && administratorCount > 0L) {
      throw new ServiceException("无权配置超管角色!");
    }
    // 防止越权配置管理员角色
    long adminCount = this.count(
        Wrappers.<Role>query().lambda().eq(Role::getRoleAlias, RoleConstant.ADMIN)
            .in(Role::getId, roleIds));
    if (!AuthUtil.isAdmin() && adminCount > 0L) {
      throw new ServiceException("无权配置管理员角色!");
    }
  }

  /**
   * @param aliases
   * @return
   */
  @Override
  public List<Role> getRoleIdsByAliases(List<String> aliases) {
    return baseMapper.getRoleIdsByAliases(aliases);
  }

}
