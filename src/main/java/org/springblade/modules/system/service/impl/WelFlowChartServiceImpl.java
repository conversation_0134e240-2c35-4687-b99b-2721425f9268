/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import java.util.List;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.modules.system.entity.WelFlowChartEntity;
import org.springblade.modules.system.mapper.WelFlowChartMapper;
import org.springblade.modules.system.service.IWelFlowChartService;
import org.springblade.modules.system.vo.WelFlowChartVO;
import org.springblade.modules.system.wrapper.WelFlowChartWrapper;
import org.springframework.stereotype.Service;

/**
 * 系统管理-首页流程图配置 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-28
 */
@Service
public class WelFlowChartServiceImpl extends
	BaseServiceImpl<WelFlowChartMapper, WelFlowChartEntity> implements IWelFlowChartService {

	@Override
	public IPage<WelFlowChartVO> selectNiWelFlowChartPage(IPage<WelFlowChartVO> page,
		WelFlowChartVO niWelFlowChart) {
		return page.setRecords(baseMapper.selectNiWelFlowChartPage(page, niWelFlowChart));
	}

	@Override
	public List<WelFlowChartVO> tree(String tenantId) {
		List<WelFlowChartEntity> list = list(Wrappers.<WelFlowChartEntity>lambdaQuery()
			.eq(WelFlowChartEntity::getTenantId, tenantId).orderByAsc(WelFlowChartEntity::getSn));
		return WelFlowChartWrapper.build().treeNodeVO(list);
	}

}
