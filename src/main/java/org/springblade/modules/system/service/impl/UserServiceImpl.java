/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.google.zxing.EncodeHintType;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.natergy.ni.crm.dto.UserRegionDTO;
import com.natergy.ni.crm.vo.RelatedUserVO;
import com.natergy.ni.pa.entity.PaAttUserInfo;
import com.natergy.ni.pa.service.IPaAttUserInfoService;
import com.natergy.ni.pa.util.IdCardUtil;
import com.natergy.ni.pa.util.UuidGenerate;
import io.jsonwebtoken.lang.Assert;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DictCache;
import org.springblade.common.cache.ParamCache;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.enums.DictEnum;
import org.springblade.common.utils.QRCodeUtil;
import org.springblade.core.datascope.enums.DataScopeEnum;
import org.springblade.core.datascope.handler.ScopeModelHandler;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.BladeTenantProperties;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.modules.auth.enums.UserEnum;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.dto.ScopeDTO;
import org.springblade.modules.system.entity.*;
import org.springblade.modules.system.excel.UserExcel;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.service.*;
import org.springblade.modules.system.vo.ContractItem;
import org.springblade.modules.system.vo.DeptAndUserVO;
import org.springblade.modules.system.vo.UserVO;
import org.springblade.modules.system.wrapper.UserWrapper;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.springblade.common.constant.CommonConstant.DEFAULT_PARAM_PASSWORD;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserService {

	private static final String GUEST_NAME = "guest";

	private final IUserDeptService userDeptService;
	private final IUserOauthService userOauthService;
	private final IRoleService roleService;
	private final BladeTenantProperties tenantProperties;
	private final IDataScopeService dataScopeService;
	private final ScopeModelHandler scopeModelHandler;

	private final IDictBizService dictService;

	private final IRoleScopeService iRoleScopeService;

	private final IAttachService attachService;

	private final String MARRIED_CN = "已婚";
	private final String UN_MARRIED_CN = "未婚";
	private final List<String> MARRIED = Arrays.asList("是", MARRIED_CN);
	private final List<String> UN_MARRIED = Arrays.asList("否", UN_MARRIED_CN);
	private final Integer UN_MARRIED_STATUS = 0;
	private final Integer MARRIED_STATUS = 1;
	private final String UN_MARRIED_STATUS_STR = "0";
	private final String MARRIED_STATUS_STR = "1";
	private final String POLITICAL = "ni_pa_political_status";
	private final String EDU_LEVEL = "ni_pa_eduLevel";
	private final static String SPLIT_SYMBOL = ",";

	/**
	 * 初始化一个用于策略模式的方法调用栈
	 */
	private final Map<Integer, Function<ScopeDTO, List<Long>>> DATA_SCOPE_TYPE_METHODS = Maps.newHashMapWithExpectedSize(
		8);

	private DataSourceTransactionManager transactionManager;
	private final IPaAttUserInfoService iPaAttUserInfoService;


	@PostConstruct
	public void init() {

		DATA_SCOPE_TYPE_METHODS.put(DataScopeEnum.ALL.getType(), this::getAllUserIds);

		DATA_SCOPE_TYPE_METHODS.put(DataScopeEnum.OWN.getType(),
			data -> Collections.singletonList(AuthUtil.getUserId()));

		DATA_SCOPE_TYPE_METHODS.put(DataScopeEnum.OWN_DEPT.getType(), this::getOwnDept);

		DATA_SCOPE_TYPE_METHODS.put(DataScopeEnum.OWN_DEPT_CHILD.getType(), this::getOwnDeptChild);

		DATA_SCOPE_TYPE_METHODS.put(DataScopeEnum.CUSTOM.getType(), this::getCustom);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(User user) {
		if (StringUtil.isBlank(user.getTenantId())) {
			user.setTenantId(BladeConstant.ADMIN_TENANT_ID);
		}
		String tenantId = user.getTenantId();
		Tenant tenant = SysCache.getTenant(tenantId);
		if (Func.isNotEmpty(tenant)) {
			Integer accountNumber = tenant.getAccountNumber();
			if (tenantProperties.getLicense()) {
				String licenseKey = tenant.getLicenseKey();
				String decrypt = DesUtil.decryptFormHex(licenseKey, TenantConstant.DES_KEY);
				accountNumber = JsonUtil.parse(decrypt, Tenant.class).getAccountNumber();
			}
			Long tenantCount = baseMapper.selectCount(
				Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId));
			if (accountNumber != null && accountNumber > 0 && accountNumber <= tenantCount) {
				throw new ServiceException("当前租户已到最大账号额度!");
			}
		}
		if (Func.isNotEmpty(user.getPassword())) {
			user.setPassword(DigestUtil.encrypt(user.getPassword()));
		}
		Long userCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId)
				.eq(User::getAccount, user.getAccount()));
		if (userCount > 0L && Func.isEmpty(user.getId())) {
			throw new ServiceException(
				StringUtil.format("当前用户 [{}] 已存在!", user.getAccount()));
		}
		String secondaryPhones = user.getSecondaryPhones();
		if (Objects.nonNull(secondaryPhones) && "clear".equals(secondaryPhones)) {
			user.setSecondaryPhones(StringPool.EMPTY);
		}
		String licensePlate = user.getLicensePlate();
		if (Objects.nonNull(licensePlate) && "clear".equals(licensePlate)) {
			user.setLicensePlate(StringPool.EMPTY);
		}
		return save(user) && submitUserDept(user);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateUser(User user) {
		String tenantId = user.getTenantId();
		Long userCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId)
				.eq(User::getAccount, user.getAccount()).notIn(User::getId, user.getId()));
		if (userCount > 0L) {
			throw new ServiceException(
				StringUtil.format("当前用户 [{}] 已存在!", user.getAccount()));
		}
		String secondaryPhones = user.getSecondaryPhones();
		if (Objects.nonNull(secondaryPhones) && "clear".equals(secondaryPhones)) {
			user.setSecondaryPhones(StringPool.EMPTY);
		}
		String licensePlate = user.getLicensePlate();
		if (Objects.nonNull(licensePlate) && "clear".equals(licensePlate)) {
			user.setLicensePlate(StringPool.EMPTY);
		}
		return updateUserInfo(user) && submitUserDept(user);
	}

	@Override
	public boolean updateUserInfo(User user) {
		user.setPassword(null);
		// 初始使用时设置, 初期导入后恢复回 user.setPassword(null);
		// String password = user.getPassword();
		// if (Func.isNotEmpty(password)) {
		// 	user.setPassword(DigestUtil.encrypt(user.getPassword()));
		// }
		return updateById(user);
	}

	private boolean submitUserDept(User user) {
		List<Long> deptIdList = Func.toLongList(user.getDeptId());
		List<UserDept> userDeptList = new ArrayList<>();
		deptIdList.forEach(deptId -> {
			UserDept userDept = new UserDept();
			userDept.setUserId(user.getId());
			userDept.setDeptId(deptId);
			userDeptList.add(userDept);
		});
		userDeptService.remove(
			Wrappers.<UserDept>update().lambda().eq(UserDept::getUserId, user.getId()));
		return userDeptService.saveBatch(userDeptList);
	}

	@Override
	public IPage<User> selectUserPage(IPage<User> page, User user, Long deptId, String tenantId) {
		List<Long> deptIdList = SysCache.getDeptChildIds(deptId);
		return page.setRecords(baseMapper.selectUserPage(page, user, deptIdList, tenantId));
	}

	@Override
	public IPage<User> selectUserPageByVO(IPage<User> page, UserVO user, Long deptId,
		String tenantId) {

		List<Long> deptIdList = SysCache.getDeptChildIds(deptId);

		user.setDeptId(null);
		return page.setRecords(baseMapper.selectUserPageByVO(page, user, deptIdList, tenantId));
	}

	@Override
	public IPage<UserVO> selectUserSearch(UserVO user, Query query) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		queryWrapper.eq(user.getStatus() != null, User::getStatus, user.getStatus());
		String tenantId = AuthUtil.getTenantId();
		if (StringUtil.isNotBlank(tenantId)) {
			queryWrapper.eq(User::getTenantId, tenantId);
		}
		if (StringUtil.isNotBlank(user.getName())) {
			queryWrapper.like(User::getName, user.getName());
		}
		if (StringUtil.isNotBlank(user.getDeptName())) {
			String deptIds = SysCache.getDeptIdsByFuzzy(AuthUtil.getTenantId(), user.getDeptName());
			if (StringUtil.isNotBlank(deptIds)) {
				queryWrapper.and(wrapper -> {
					List<Long> deptIds1 = Func.toLongList(deptIds).stream()
						.flatMap(deptId -> SysCache.getDeptChildIds(deptId).stream()).collect(
							Collectors.toList());
					deptIds1.forEach(id -> wrapper.like(User::getDeptId, id).or());
				});
			} else {
				IPage<UserVO> res = Condition.getPage(query);
				res.setRecords(new ArrayList<>());
				res.setTotal(0);
				return res;
			}
		}
		if (StringUtil.isNotBlank(user.getPostName())) {
			String postIds = SysCache.getPostIdsByFuzzy(AuthUtil.getTenantId(), user.getPostName());
			if (StringUtil.isNotBlank(postIds)) {
				queryWrapper.and(wrapper -> {
					List<String> ids = Func.toStrList(postIds);
					ids.forEach(id -> wrapper.like(User::getPostId, id).or());
				});
			} else {
				IPage<UserVO> res = Condition.getPage(query);
				res.setRecords(new ArrayList<>());
				res.setTotal(0);
				return res;
			}
		}
		IPage<User> pages = this.page(Condition.getPage(query), queryWrapper);
		return UserWrapper.build().pageVO(pages);
	}

	@Override
	public User userByAccount(String tenantId, String account) {
		return baseMapper.selectOne(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId)
			.eq(User::getAccount, account).eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED));
	}

	@Override
	public UserInfo userInfo(Long userId) {
		User user = baseMapper.selectById(userId);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account, String password) {
		User user = baseMapper.getUser(tenantId, account, password);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account, String password, UserEnum userEnum) {
		User user = baseMapper.getUser(tenantId, account, password);
		return buildUserInfo(user, userEnum);
	}

	private UserInfo buildUserInfo(User user) {
		return buildUserInfo(user, UserEnum.WEB);
	}

	private UserInfo buildUserInfo(User user, UserEnum userEnum) {
		if (ObjectUtil.isEmpty(user)) {
			return null;
		}
		UserInfo userInfo = new UserInfo();
		userInfo.setUser(user);
		if (Func.isNotEmpty(user)) {
			List<String> roleAlias = roleService.getRoleAliases(user.getRoleId());
			userInfo.setRoles(roleAlias);
		}
		// 根据每个用户平台，建立对应的detail表，通过查询将结果集写入到detail字段
		Kv detail = Kv.create().set("type", userEnum.getName());
		if (userEnum == UserEnum.WEB) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(
				Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		} else if (userEnum == UserEnum.APP) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(
				Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(
				Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		}
		userInfo.setDetail(detail);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo userInfo(UserOauth userOauth) {
		UserOauth uo = userOauthService.getOne(
			Wrappers.<UserOauth>query().lambda().eq(UserOauth::getUuid, userOauth.getUuid())
				.eq(UserOauth::getSource, userOauth.getSource()));
		UserInfo userInfo;
		if (Func.isNotEmpty(uo) && Func.isNotEmpty(uo.getUserId())) {
			userInfo = this.userInfo(uo.getUserId());
			userInfo.setOauthId(Func.toStr(uo.getId()));
		} else {
			userInfo = new UserInfo();
			if (Func.isEmpty(uo)) {
				userOauthService.save(userOauth);
				userInfo.setOauthId(Func.toStr(userOauth.getId()));
			} else {
				userInfo.setOauthId(Func.toStr(uo.getId()));
			}
			User user = new User();
			user.setAccount(userOauth.getUsername());
			user.setTenantId(userOauth.getTenantId());
			userInfo.setUser(user);
			userInfo.setRoles(Collections.singletonList(GUEST_NAME));
		}
		return userInfo;
	}

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param openId
	 * @param userEnum
	 * @return
	 */
	@Override
	public UserInfo userInfoByMiniProgramOpenId(String tenantId, String openId, UserEnum userEnum) {
		User user = getByMiniProgramOpenId(tenantId, openId);
		return buildUserInfo(user, userEnum);
	}

	@Override
	public boolean grant(String userIds, String roleIds) {
		return this.update(
			Wrappers.<User>update().lambda().in(User::getId, Func.toLongList(userIds))
				.set(User::getRoleId, roleIds));
	}

	@Override
	public boolean resetPassword(String userIds) {
		return this.update(
			Wrappers.<User>update().lambda().in(User::getId, Func.toLongList(userIds))
				.set(User::getPassword, DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD))
				.set(User::getUpdateTime, DateUtil.now()));
	}

	@Override
	public boolean updatePassword(Long userId, String oldPassword, String newPassword,
		String newPassword1) {
		User user = getById(userId);
		if (!newPassword.equals(newPassword1)) {
			throw new ServiceException("请输入正确的确认密码!");
		}
		if (!user.getPassword().equals(DigestUtil.hex(oldPassword))) {
			throw new ServiceException("原密码不正确!");
		}
		return this.update(
			Wrappers.<User>update().lambda().set(User::getPassword, DigestUtil.hex(newPassword))
				.eq(User::getId, userId));
	}

	@Override
	public boolean removeUser(String userIds) {
		if (Func.contains(Func.toLongArray(userIds), AuthUtil.getUserId())) {
			throw new ServiceException("不能删除本账号!");
		}
		return deleteLogic(Func.toLongList(userIds));
	}

	@Override
	public void importUser(List<UserExcel> data, Boolean isCovered) {
		Map<String, String> politicalMap = dictService.getList(POLITICAL).stream()
			.collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey));
		Map<String, String> levelMap = dictService.getList(EDU_LEVEL).stream()
			.collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey));
		Map<String, Long> badgeMap = iPaAttUserInfoService.repeatExist().stream()
			.collect(Collectors.toMap(PaAttUserInfo::getBadgenumber, PaAttUserInfo::getUserid));
		// 开启事务
		DefaultTransactionDefinition def = new DefaultTransactionDefinition();
		TransactionStatus status = transactionManager.getTransaction(def);
		try {
			// 执行业务逻辑
			data.forEach(userExcel -> {
				User user = Objects.requireNonNull(BeanUtil.copy(userExcel, User.class));
				// 设置用户平台
				user.setUserType(
					Func.toInt(DictCache.getKey(DictEnum.USER_TYPE, userExcel.getUserTypeName()),
						1));
				// 设置部门ID
				user.setDeptId(Func.toStrWithEmpty(
					SysCache.getDeptIds(userExcel.getTenantId(), userExcel.getDeptName()),
					StringPool.EMPTY));
				// 设置岗位ID
				user.setPostId(Func.toStrWithEmpty(
					SysCache.getPostIds(userExcel.getTenantId(), userExcel.getPostName()),
					StringPool.EMPTY));
				// 设置角色ID
				user.setRoleId(Func.toStrWithEmpty(
					SysCache.getRoleIds(userExcel.getTenantId(), userExcel.getRoleName()),
					StringPool.EMPTY));
				// 设置生日/性别
				String idCard = Func.toStrWithEmpty(userExcel.getIdCard(), StringPool.EMPTY);
				if (idCard.length() == IdCardUtil.ID_CARD_LENGTH) {
					String stringBirth = IdCardUtil.getStringBirth(idCard);
					try {
						user.setBirthday(DateUtil.DATE_FORMAT.parse(stringBirth));
						Integer gender = IdCardUtil.getGender(idCard);
						user.setSex(gender);
					} catch (ParseException e) {
						throw new RuntimeException("身份证号码 格式错误");
					}
				}
				// 设置是否已婚
				String isMarriedName = userExcel.getIsMarriedName();
				if (MARRIED.contains(isMarriedName)) {
					user.setIsMarried(MARRIED_STATUS);
				} else if (UN_MARRIED.contains(isMarriedName)) {
					user.setIsMarried(UN_MARRIED_STATUS);
				}
				// 设置政治面貌
				String politicalName = userExcel.getPoliticalStatusName();
				if (politicalMap.containsKey(politicalName)) {
					user.setPoliticalStatus(Integer.parseInt(politicalMap.get(politicalName)));
				}
				// 设置文化程度
				String eduLevelName = userExcel.getEduLevelName();
				if (levelMap.containsKey(eduLevelName)) {
					user.setEduLevel(Integer.parseInt(levelMap.get(eduLevelName)));
				}
				// 设置考勤机 userid
				String badgeNumber = userExcel.getZkecoBadgeNumber();
				if (badgeMap.containsKey(badgeNumber)) {
					user.setZkecoUserId(badgeMap.get(badgeNumber));
				} else {
					if (ObjectUtil.isNotEmpty(badgeNumber)) {
						String numberStr = Integer.toString(Integer.parseInt(badgeNumber));
						if (badgeMap.containsKey(numberStr)) {
							user.setZkecoUserId(badgeMap.get(numberStr));
							// user.setZkecoBadgeNumber(numberStr);
						}
					}
				}
				// 设置租户ID
				if (!AuthUtil.isAdministrator() || StringUtil.isBlank(user.getTenantId())) {
					user.setTenantId(AuthUtil.getTenantId());
				}
				// 覆盖数据
				if (isCovered) {
					// 查询用户是否存在
					User oldUser = UserCache.getUser(userExcel.getTenantId(),
						userExcel.getAccount());
					if (oldUser != null && oldUser.getId() != null) {
						user.setId(oldUser.getId());
						this.updateUser(user);
						return;
					}
				}
				if (StringUtil.isBlank(user.getPassword())) {
					// 获取默认密码配置
					String initPassword = ParamCache.getValue(DEFAULT_PARAM_PASSWORD);
					user.setPassword(initPassword);
				}
				this.submit(user);
			});
			// 提交事务
			transactionManager.commit(status);
		} catch (Exception e) {
			new RuntimeException("导入失败, " + e.getMessage());
			// 回滚事务
			transactionManager.rollback(status);
		}
	}

	@Override
	public List<UserExcel> exportUser(Wrapper<User> queryWrapper) {
		Map<String, String> politicalMap = dictService.getList(POLITICAL).stream()
			.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
		Map<String, String> levelMap = dictService.getList(EDU_LEVEL).stream()
			.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
		List<UserExcel> userList = baseMapper.exportUser(queryWrapper);
		userList.forEach(user -> {
			user.setUserTypeName(DictCache.getValue(DictEnum.USER_TYPE, user.getUserType()));
			user.setRoleName(StringUtil.join(SysCache.getRoleNames(user.getRoleId())));
			user.setDeptName(StringUtil.join(SysCache.getDeptNames(user.getDeptId())));
			user.setPostName(StringUtil.join(SysCache.getPostNames(user.getPostId())));
			String isMarried = Func.toStrWithEmpty(user.getIsMarried(), StringPool.EMPTY);
			if (UN_MARRIED_STATUS_STR.equals(isMarried)) {
				user.setIsMarriedName(UN_MARRIED_CN);
			} else if (MARRIED_STATUS_STR.equals(isMarried)) {
				user.setIsMarriedName(MARRIED_CN);
			}
			String politicalStatus = Func.toStrWithEmpty(user.getPoliticalStatus(),
				StringPool.EMPTY);
			if (politicalMap.containsKey(politicalStatus)) {
				user.setPoliticalStatusName(politicalMap.get(politicalStatus));
			}
			String level = Func.toStrWithEmpty(user.getEduLevel(), StringPool.EMPTY);
			if (levelMap.containsKey(level)) {
				user.setEduLevelName(levelMap.get(level));
			}
			if (StringUtils.isNotBlank(user.getStatus()) && "1".equals(user.getStatus())) {
				user.setStatus("在职");
			} else if (StringUtils.isNotBlank(user.getStatus()) && "2".equals(user.getStatus())) {
				user.setStatus("离职");
			} else if (StringUtils.isNotBlank(user.getStatus()) && "3".equals(user.getStatus())) {
				user.setStatus("停薪留职");
			}
		});
		return userList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean registerGuest(User user, Long oauthId) {
		Tenant tenant = SysCache.getTenant(user.getTenantId());
		if (tenant == null || tenant.getId() == null) {
			throw new ServiceException("租户信息错误!");
		}
		UserOauth userOauth = userOauthService.getById(oauthId);
		if (userOauth == null || userOauth.getId() == null) {
			throw new ServiceException("第三方登陆信息错误!");
		}
		user.setRealName(user.getName());
		user.setAvatar(userOauth.getAvatar());
		user.setRoleId(StringPool.MINUS_ONE);
		user.setDeptId(StringPool.MINUS_ONE);
		user.setPostId(StringPool.MINUS_ONE);
		boolean userTemp = this.submit(user);
		userOauth.setUserId(user.getId());
		userOauth.setTenantId(user.getTenantId());
		boolean oauthTemp = userOauthService.updateById(userOauth);
		return (userTemp && oauthTemp);
	}

	@Override
	public boolean updatePlatform(Long userId, Integer userType, String userExt) {
		if (userType.equals(UserEnum.WEB.getCategory())) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(
				Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userWeb.setId(query.getId());
			}
			userWeb.setUserId(userId);
			userWeb.setUserExt(userExt);
			return userWeb.insertOrUpdate();
		} else if (userType.equals(UserEnum.APP.getCategory())) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(
				Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userApp.setId(query.getId());
			}
			userApp.setUserId(userId);
			userApp.setUserExt(userExt);
			return userApp.insertOrUpdate();
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(
				Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userOther.setId(query.getId());
			}
			userOther.setUserId(userId);
			userOther.setUserExt(userExt);
			return userOther.insertOrUpdate();
		}
	}

	@Override
	public UserVO platformDetail(User user) {
		User detail = baseMapper.selectOne(Condition.getQueryWrapper(user));
		UserVO userVO = UserWrapper.build().entityVO(detail);
		if (userVO.getUserType().equals(UserEnum.WEB.getCategory())) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(
				Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		} else if (userVO.getUserType().equals(UserEnum.APP.getCategory())) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(
				Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(
				Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		}
		return userVO;
	}

	@Override
	public void relateUserInfo(User user) {
		List<PaAttUserInfo> userInfos = iPaAttUserInfoService.repeatExist();
		Long zkecoUserId = user.getZkecoUserId();
		String badgeNumber = user.getZkecoBadgeNumber();
		if (zkecoUserId == null && User.EX_NUMBER.equals(badgeNumber)) {
			this.update(Wrappers.<User>lambdaUpdate().eq(User::getId, user.getId())
				.set(User::getZkecoUserId, null).set(User::getZkecoBadgeNumber, null));
			user.setZkecoBadgeNumber(null);
			return;
		}
		if (StringUtil.isNumeric(badgeNumber)) {
			int number = Integer.parseInt(badgeNumber);
			String numberStr = number + StringPool.EMPTY;
			String format = String.format("%05d", number);
			Long count = baseMapper.selectCount(Wrappers.<User>query().lambda()
				.in(User::getZkecoBadgeNumber, Arrays.asList(numberStr, format))
				.notIn(user.getId() != null, User::getId, user.getId()));
			Assert.isTrue(count == 0L,
				String.format("编号 %s 或 %s 已关联其他用户", numberStr, format));
			Map<String, Long> collect = userInfos.stream().collect(Collectors.toMap(
				info -> String.format("%05d", Integer.parseInt(info.getBadgenumber())),
				PaAttUserInfo::getUserid));
			Assert.isTrue(collect.containsKey(format),
				String.format("不存在编号 %s 或 %s", numberStr, format));
			user.setZkecoBadgeNumber(format);
			user.setZkecoUserId(collect.get(format));
		}
	}

	@Override
	public List<User> selectUserListByVO(UserVO user, String deptIds, String tenantId) {
		List<Long> deptIdList = Func.toLongList(deptIds).stream()
			.flatMap(deptId -> SysCache.getDeptChildIds(deptId).stream())
			.collect(Collectors.toList());
		return baseMapper.selectUserPageByVO(null, user, deptIdList, tenantId);
	}

	/**
	 * 根据考勤系统的cid查询用户
	 *
	 * @param cid
	 * @return
	 */
	@Override
	public User getByCid(String cid) {
		return getOne(Wrappers.lambdaQuery(User.class).apply(
			"right('0000000'+zkeco_badge_number,7) = " + String.format("%07d",
				Integer.valueOf(cid))).eq(User::getStatus, 1));
	}

	/**
	 * 根据微信小程序的openid获取用户
	 *
	 * @param tenantId
	 * @param openid
	 * @return
	 */
	@Override
	public User getByMiniProgramOpenId(String tenantId, String openid) {
		return getOne(Wrappers.lambdaQuery(User.class).eq(User::getTenantId, tenantId)
			.eq(User::getMiniprogramOpenid, openid).eq(User::getStatus, 1));
	}

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param phone
	 * @param userEnum
	 * @return
	 */
	@Override
	public UserInfo userInfoByPhone(String tenantId, String phone, UserEnum userEnum) {
		User user = baseMapper.getUserByPhone(tenantId, phone);
		return buildUserInfo(user, userEnum);
	}

	/**
	 * 校验重号
	 *
	 * @param user
	 * @return
	 */
	@Override
	public String searchSamePhone(User user) {
		List<String> phones = new ArrayList<>();
		String phone = user.getPhone();
		if (Objects.nonNull(phone) && StringUtil.hasText(phone)) {
			phones.add(phone);
		}
//    String secondaryPhones = user.getSecondaryPhones();
//    if (Objects.nonNull(secondaryPhones) && StringUtil.hasText(secondaryPhones)) {
//      JSONArray jsonArray = JSONArray.parseArray(secondaryPhones);
//      List<String> phoneList = jsonArray.stream().filter(Objects::nonNull).map(Object::toString)
//          .filter(StringUtil::hasText).collect(Collectors.toList());
//      phones.addAll(phoneList);
//    }
		if (!phones.isEmpty()) {
			List<User> users = baseMapper.searchSamePhone(phones, user.getId());
			if (!users.isEmpty()) {
				String names = users.stream().map(User::getRealName)
					.collect(Collectors.joining(","));
				return StringUtil.format("{} 与 [{}] 主手机号重复!", phone, names);
			}
		}
		return "1";
	}

	/**
	 * 用户基本信息
	 *
	 * @return
	 */
	@Override
	public IPage<User> userBaseInfoPage(Query query, UserVO user) {
		String deptId = user.getDeptId();
		List<Long> deptIds = new ArrayList<>();
		if (Objects.nonNull(deptId)) {
			deptIds = SysCache.getDeptChildIds(Long.parseLong(deptId));
		}
		String realName = user.getRealName();
		List<List<Long>> scopeList = this.getScopeList();
		if (Objects.isNull(scopeList)) {
			return baseMapper.scopeUserInfoList(Condition.getPage(query), user, null, null, deptIds,
				null,
				realName);
		} else if (scopeList.isEmpty()) {
			return new Page<>();
		} else {
			return baseMapper.scopeUserInfoList(Condition.getPage(query), user, scopeList.get(0),
				scopeList.get(1), deptIds, null, realName);
		}
	}

	private String ADDRESS_LIST_SCOPE = "ni_base_user_info";
	private String SPELL_KEY = "firstSpell";
	private String USER_LIST_KEY = "users";

	public List<List<Long>> getScopeList() {
		// 数据权限
		boolean isAdmin = AuthUtil.isAdmin() || AuthUtil.isAdministrator();
		if (isAdmin) {
			return null;
		}
		List<List<Long>> scopeList = new ArrayList<>();
		BladeUser user = AuthUtil.getUser();
		String roleIds = user.getRoleId();
		Set<DataScope> dataScopes = Func.toLongList(roleIds).stream().flatMap(
				roleId -> dataScopeService.codeListByRoleId(roleId, ADDRESS_LIST_SCOPE).stream())
			.collect(Collectors.toSet());
		if (dataScopes.isEmpty()) {
			return scopeList;
		}
		Set<Integer> scopeTypes = dataScopes.stream().map(DataScope::getScopeType)
			.collect(Collectors.toSet());
		// 全显权限
		if (scopeTypes.contains(DataScopeEnum.ALL.getType())) {
			return null;
		}
		Set<Long> scopeDeptList = new HashSet<>();
		Set<Long> scopeUserList = new HashSet<>();
		if (scopeTypes.contains(DataScopeEnum.OWN.getType())) {
			scopeUserList.add(user.getUserId());
		}
		if (scopeTypes.contains(DataScopeEnum.OWN_DEPT.getType())) {
			scopeDeptList.addAll(Func.toLongList(user.getDeptId()));
		}
		if (scopeTypes.contains(DataScopeEnum.OWN_DEPT_CHILD.getType())) {
			List<Long> deptIds = Func.toLongList(user.getDeptId());
			scopeDeptList.addAll(deptIds);
			deptIds.forEach(dId -> {
				List<Long> dList = scopeModelHandler.getDeptAncestors(dId);
				scopeDeptList.addAll(dList);
			});
		}
		if (scopeTypes.contains(DataScopeEnum.CUSTOM.getType())) {
			List<DataScope> scopes = dataScopes.stream()
				.filter(scope -> scope.getScopeType() == DataScopeEnum.CUSTOM.getType())
				.collect(Collectors.toList());
			List<DataScope> scopeContainsUsers = scopes.stream()
				.filter(scope -> StringUtil.isNotBlank(scope.getPersonId()))
				.collect(Collectors.toList());
			List<Long> uList = scopeContainsUsers.stream()
				.flatMap(scope -> Func.toLongList(scope.getPersonId()).stream())
				.collect(Collectors.toList());
			List<Long> excludeDeptList = scopeContainsUsers.stream()
				.flatMap(dataScope -> Func.toLongList(dataScope.getDeptId()).stream())
				.collect(Collectors.toList());
			List<Long> dList = scopes.stream()
				.filter(scope -> StringUtil.isEmpty(scope.getPersonId()))
				.flatMap(dataScope -> Func.toLongList(dataScope.getDeptId()).stream())
				.collect(Collectors.toList());
			scopeUserList.addAll(uList);
			scopeDeptList.addAll(dList.stream().filter(dept -> !excludeDeptList.contains(dept))
				.collect(Collectors.toList()));
		}
		scopeList.add(new ArrayList<>(scopeDeptList));
		scopeList.add(new ArrayList<>(scopeUserList));
		return scopeList;
	}

	public List<UserSimplified> scopeAddressList() {
		List<List<Long>> scopeList = this.getScopeList();
		if (Objects.isNull(scopeList)) {
			// 全显
			return baseMapper.scopeAddressList(null, null);
		} else if (scopeList.isEmpty()) {
			// 空
			return new ArrayList<>();
		} else {
			// 按权限
			return baseMapper.scopeAddressList(scopeList.get(0), scopeList.get(1));
		}
	}

	public List<Map<String, Object>> getAddressList() {
		List<UserSimplified> users = this.scopeAddressList();
		users.forEach(user -> {
			String nameTotalSpell = user.getNameTotalSpell();
			String[] wordSpells = nameTotalSpell.split(" ");
			user.setNameSpell(
				Arrays.stream(wordSpells).map(wordSpell -> wordSpell.substring(0, 1).toUpperCase())
					.collect(Collectors.joining(StringPool.EMPTY)));
			user.setNameTotalSpell(String.join(StringPool.EMPTY, wordSpells));
			String licensePlate = user.getLicensePlate();
			if (Objects.nonNull(licensePlate)) {
				user.setLicensePlate(licensePlate.toUpperCase());
			}
			List<String> deptName = SysCache.getDeptNames(user.getDeptId());
			List<String> postName = SysCache.getPostNames(user.getPostId());
			user.setDeptName(Func.join(deptName));
			user.setPostName(Func.join(postName));
		});
		Map<String, List<UserSimplified>> userGroupMap = users.stream()
			.collect(Collectors.groupingBy(usr -> usr.getNameSpell().substring(0, 1)));
		List<Map<String, Object>> resultList = new ArrayList<>();
		userGroupMap.entrySet().forEach(entry -> {
			Map<String, Object> spellMap = new HashMap<>();
			spellMap.put(SPELL_KEY, entry.getKey());
			spellMap.put(USER_LIST_KEY, entry.getValue());
			resultList.add(spellMap);
		});
		resultList.sort(
			(l1, l2) -> l1.get(SPELL_KEY).toString().compareTo(l2.get(SPELL_KEY).toString()));
		return resultList;
	}

	/**
	 * @param queryWrapper
	 * @param response
	 */
	@Override
	public void generateQRImage(QueryWrapper<User> queryWrapper, List<Long> deptIds,
		HttpServletResponse response) throws UnsupportedEncodingException {
		List<UserExcel> userList = getExportUser(queryWrapper, deptIds);
		String qrCodeParam = ParamCache.getValue(QR_CODE_PARAM_KEY);
		Map<String, Object> map = JSON.parseObject(qrCodeParam,
			new TypeReference<Map<String, Object>>() {
			});
//		int width = (int) map.get("width");
//		int height = (int) map.get("height");
//		Map<EncodeHintType, Object> hints = new HashMap<>();
//		hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
//		//L 水平 7%的字码可被修正
//		//M 水平 15%的字码可被修正
//		//Q 水平 25%的字码可被修正
//		//H 水平 30%的字码可被修正
//		hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
//		hints.put(EncodeHintType.MARGIN, 2);
		String fileName = "qr";
		fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
		response.setContentType("application/octet-stream");
		//告诉客户端该文件不是直接解析而是以附件的形式打开（下载）
		response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".zip");
		try (ZipOutputStream zos = new ZipOutputStream(
			new BufferedOutputStream(response.getOutputStream()))) {
			userList.forEach(user -> {
				List<String> deptNames = SysCache.getDeptNames(user.getDeptId());
				String deptName = deptNames == null || deptNames.isEmpty() ? "" : deptNames.get(0);
				String qr = Base64Util.encode(user.getAccount(), StandardCharsets.UTF_8);
				try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
					BufferedImage bufferedImage = QRCodeUtil.getQRCodeImage(qr, false,
						deptName + "-" + user.getAccount());
					ImageIO.write(bufferedImage, "png", os);
					ZipEntry zipEntry = new ZipEntry(deptName + "-" + user.getAccount() + ".png");
					zos.putNextEntry(zipEntry);
					byte[] bytes = os.toByteArray();
					zos.write(bytes, 0, bytes.length);
					//关闭输入输出流
					zos.closeEntry();
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			});
		} catch (Exception e) {
			log.error("导出二维码失败", e);
		}

	}

	private List<UserExcel> getExportUser(QueryWrapper<User> queryWrapper, List<Long> deptIds) {
		if (deptIds != null && !deptIds.isEmpty()) {
			List<Long> userIds = userDeptService.getUserIdsByDeptIds(new HashSet<>(deptIds));
			if (userIds.isEmpty()) {
				return new ArrayList<>();
			} else {
				queryWrapper.lambda().in(User::getId, userIds);
			}
		}
		return baseMapper.exportUser(queryWrapper);
	}

	public static final String QR_CODE_PARAM_KEY = "user.qr.param";

//    public void generateQRCodeImage(String text, int width, int height, OutputStream os, Map<EncodeHintType, Object> hints)
//            throws WriterException, IOException {
//        QRCodeWriter qrCodeWriter = new QRCodeWriter();
//        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height, hints);
//        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", os);
//    }

	@Override
	public Boolean addContract(UserVO vo, IAttachService attachService) {
		Long userId = vo.getId();
		User user = this.getById(userId);
		String contract = user.getContract();
		List<ContractItem> contracts = vo.getContracts();
		if (!contracts.isEmpty()) {
			List<ContractItem> contractItems = new ArrayList<>();
			contracts.stream().findFirst().ifPresent(item -> {
				if (!StringUtils.isEmpty(contract)) {
					contractItems.addAll(JSONArray.parseArray(contract, ContractItem.class));
					contractItems.forEach(i -> i.setEffect(false));
				}
				item.setCreateSN(UuidGenerate.getUUID());
				String name = item.getName();
				if (!StringUtils.isEmpty(name)) {
					Attach one = attachService.getOne(
						Wrappers.<Attach>lambdaQuery().eq(Attach::getName, name));
					if (one != null) {
						item.setLinkId(one.getId());
					}
				}
				contractItems.add(item);
			});
			return this.update(Wrappers.<User>lambdaUpdate().eq(User::getId, userId)
				.set(User::getContract, JSONArray.toJSONString(contractItems)));
		}
		return false;
	}

	@Override
	public Boolean updateContract(UserVO vo, IAttachService attachService) {
		Long userId = vo.getId();
		User user = this.getById(userId);
		String contract = user.getContract();
		List<ContractItem> contracts = vo.getContracts();
		if (!contracts.isEmpty()) {
			List<ContractItem> contractItems = JSONArray.parseArray(contract, ContractItem.class);
			contracts.stream().findFirst().ifPresent(item -> {
				if (!StringUtils.isEmpty(contract)) {
					contractItems.stream().filter(i -> item.getCreateSN().equals(i.getCreateSN()))
						.findFirst().ifPresent(bean -> {
							String name = item.getName();
							if (!StringUtils.isEmpty(name)) {
								Attach one = attachService.getOne(
									Wrappers.<Attach>lambdaQuery().eq(Attach::getName, name));
								if (one != null) {
									bean.setLinkId(one.getId());
								}
							}
							bean.setContractStart(item.getContractStart());
							bean.setContractEnd(item.getContractEnd());
						});
				}
			});
			return this.update(Wrappers.<User>lambdaUpdate().eq(User::getId, userId)
				.set(User::getContract, JSONArray.toJSONString(contractItems)));
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean removeContract(Long userId, String ids, String sns,
		IAttachService attachService) {
		User user = this.getById(userId);
		String contract = user.getContract();
		List<ContractItem> contractItems = JSONArray.parseArray(contract, ContractItem.class);
		List<Long> removeIds = Func.toLongList(ids);
		List<String> removeSns = Func.toStrList(sns);
		List<ContractItem> retain = contractItems.stream()
			.filter(item -> !removeSns.contains(item.getCreateSN())).collect(Collectors.toList());
		return this.update(Wrappers.<User>lambdaUpdate().eq(User::getId, userId)
			.set(User::getContract, retain.isEmpty() ? "[]" : JSONArray.toJSONString(retain))) && (
			removeIds.isEmpty() || attachService.deleteLogic(removeIds));
	}

	@Override
	public Boolean changeContract(Long userId, String sn) {
		User user = this.getById(userId);
		String contract = user.getContract();
		List<ContractItem> contractItems = JSONArray.parseArray(contract, ContractItem.class);
		contractItems.forEach(item -> {
			item.setEffect(sn.equals(item.getCreateSN()));
		});
		return this.update(Wrappers.<User>lambdaUpdate().eq(User::getId, userId)
			.set(User::getContract, JSONArray.toJSONString(contractItems)));
	}

	@Override
	public void setVoContractJson(UserVO vo, IAttachService attachService) {
		if (vo.getContractStart() == null && vo.getContractEnd() == null
			&& vo.getContractName() == null) {
			return;
		}
		String sn = vo.getContractSN();
		User user = this.getById(vo.getId());
		String contract = user.getContract();
		List<ContractItem> contractItems = new ArrayList<>();
		if (!StringUtils.isEmpty(contract)) {
			contractItems.addAll(JSONArray.parseArray(contract, ContractItem.class));
		}
		String contractName = vo.getContractName();
		if (StringUtils.isEmpty(sn)) {
			contractItems.add(this.iniContract(vo, attachService));
		} else {
			contractItems.forEach(item -> item.setEffect(false));
			List<ContractItem> filterList = contractItems.stream()
				.filter(item -> sn.equals(item.getCreateSN())).collect(Collectors.toList());
			if (filterList.isEmpty()) {
				contractItems.add(this.iniContract(vo, attachService));
			} else {
				ContractItem contractItem = filterList.get(0);
				if (contractItem.getLinkId() != null) {
					contractItems.add(this.iniContract(vo, attachService));
				} else {
					contractItem.setContractStart(vo.getContractStart());
					contractItem.setContractEnd(vo.getContractEnd());
					contractItem.setEffect(true);
					if (!StringUtils.isEmpty(contractName)) {
						Attach one = attachService.getOne(
							Wrappers.<Attach>lambdaQuery().eq(Attach::getName, contractName));
						contractItem.setLinkId(one.getId());
					}
				}
			}
		}
		vo.setContract(JSONArray.toJSONString(contractItems));
	}

	@Override
	public void unSubscribe(String openId) {
		this.update(
			new LambdaUpdateWrapper<User>().eq(User::getOpenId, openId).set(User::getOpenId, null));
	}

	@Override
	public Integer getUserSubscribeStatusById(Long userId) {
		return this.baseMapper.getUserSubscribeStatusById(userId);
	}

	/**
	 * @param name
	 * @param query
	 * @return
	 */
	@Override
	public IPage<UserVO> selectUserSearch(String name, Integer status, Query query) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();

		if (Objects.isNull(status)) {
			queryWrapper.in(User::getStatus, User.STATUS_ON, User.STATUS_QUIT);
		} else {
			queryWrapper.eq(User::getStatus, status);
		}

		String tenantId = AuthUtil.getTenantId();
		if (StringUtil.isNotBlank(tenantId)) {
			queryWrapper.eq(User::getTenantId, tenantId);
		}
		if (StringUtil.isNotBlank(name)) {
			String deptIds = SysCache.getDeptIdsByFuzzy(AuthUtil.getTenantId(), name);
			String postIds = SysCache.getPostIdsByFuzzy(AuthUtil.getTenantId(), name);
			queryWrapper.and(wrapper -> {
				if (RegexUtil.match("\\d+", name)) {
					wrapper.and(
						w -> w.eq(User::getId, Long.valueOf(name)).or().like(User::getName, name));
				} else {
					wrapper.like(User::getName, name);
				}
				if (StringUtil.isNotBlank(deptIds)) {
					List<String> ids = Func.toStrList(deptIds);
					wrapper.or(w -> ids.forEach(id -> w.eq(User::getDeptId, id).or()));
				}
				if (StringUtil.isNotBlank(postIds)) {
					List<String> ids = Func.toStrList(postIds);
					wrapper.or(w ->
						ids.forEach(id -> w.like(User::getPostId, id).or()));
				}
			});
		}
		IPage<User> pages = this.page(Condition.getPage(query), queryWrapper);
		return UserWrapper.build().pageVO(pages);
	}


	@Override
	public List<User> getAllUserList() {
		return this.list(new LambdaQueryWrapper<User>()
			.select(User::getId, User::getName));
	}


	@Override
	public List<String> getCheckinUserList() {
		return this.baseMapper.getCheckinUserList();
	}

	@Override
	public List<Long> getUserIdByRoleId(Long roleId) {
		return this.baseMapper.getUserIdByRoleId(roleId);
	}

	/**
	 * @param deptIds
	 * @return
	 */
	@Async
	@Override
	public Future<Long> countByDeptIds(List<Long> deptIds) {
		return new AsyncResult<>(baseMapper.countByDeptIds(deptIds));
	}

	/**
	 * @param deptIds
	 * @return
	 */
	@Override
	public Map<Long, Long> countGroupByDeptIds(Set<Long> deptIds) {
		return deptIds.stream().collect(Collectors.toMap(Function.identity(), deptId -> {
			List<Long> deIds = SysCache.getDeptChildIds(deptId);
			Future<Long> num = countByDeptIds(deIds);
			try {
				return num.get();
			} catch (Exception e) {
				log.error("查询部门用户数失败", e);
			}
			return 0L;
		}));
	}

	@Override
	public List<DeptAndUserVO> getAllUser() {
		return this.baseMapper.getAllUser();
	}

	@Override
	public IPage<RelatedUserVO> selectRecursionSalespersonList(IPage<RelatedUserVO> page,
		String searchKey, List<Long> userIds) {
		//国内和国外部门写死
		List<Long> domesticDeptChildIds = SysCache.getDeptChildIds(1558250198611173378L);
		List<Long> abroadDeptChildIds = SysCache.getDeptChildIds(1558250278609133570L);
		domesticDeptChildIds.addAll(abroadDeptChildIds);
		page.setRecords(this.baseMapper.selectRecursionSalespersonList(page, searchKey, userIds,
			domesticDeptChildIds));
		return page;
	}

	@Override
	public void bindAvatar(List<RelatedUserVO> userList) {
		//并行流获取头像
		userList.parallelStream().forEach(item -> {
			String fileLinkId = item.getAvatar();
			if (Objects.nonNull(fileLinkId)) {
				String avatarUrl = attachService.getLinkById(Long.parseLong(fileLinkId));
				item.setAvatar(avatarUrl);
			}
		});
	}

	@Override
	public void bindRegion(List<RelatedUserVO> userList, List<UserRegionDTO> regionList) {
		//填充业务经理的大区
		userList.forEach(user -> {
			List<String> regions = regionList.stream()
				.filter(userRegiion -> Objects.equals(userRegiion.getUserId(), user.getUserId()))
				.map(UserRegionDTO::getRegionName)
				.collect(Collectors.toList());
			user.setRegions(regions);
		});
	}


	@Override
	public ContractItem iniContract(UserVO vo, IAttachService attachService) {
		ContractItem contractItem = new ContractItem();
		contractItem.setCreateSN(UuidGenerate.getUUID());
		contractItem.setContractStart(vo.getContractStart());
		contractItem.setContractEnd(vo.getContractEnd());
		String contractName = vo.getContractName();
		if (!StringUtils.isEmpty(contractName)) {
			Attach one = attachService.getOne(
				Wrappers.<Attach>lambdaQuery().eq(Attach::getName, contractName));
			contractItem.setLinkId(one.getId());
		}
		contractItem.setEffect(true);
		return contractItem;
	}

	@Override
	public List<Long> getUserByRole(Long menuId, String roleIds) {

		//逗号拆分Long类型的集合
		List<Long> roleIdList = Arrays.stream(roleIds.split(SPLIT_SYMBOL))
			.map(Long::valueOf)
			.collect(Collectors.toList());

		//根据角色，查询出来角色对应的数据权限id
		List<Long> scopeIds = iRoleScopeService.getScopeIdsByRoleIds(roleIdList);

		//根据数据权限ID，查询每一个绑定的部门和用户的ID
		List<ScopeDTO> scopeDataList = iRoleScopeService.getScopeDatasByScopeIds(menuId, scopeIds);

		return scopeDataList.stream().flatMap(item -> {

			List<Long> userIds = DATA_SCOPE_TYPE_METHODS.get(item.getScopeType()).apply(item);

			return userIds.stream();

		}).distinct().collect(Collectors.toList());

	}

	/**
	 * 获取自定义
	 *
	 * @param item scopeData
	 * @return 用户id集合
	 */
	private List<Long> getCustom(ScopeDTO item) {

		List<Long> userIds = new ArrayList<>();

		//部门不是空的
		if (StringUtils.isNotBlank(item.getDeptId())) {

			String[] deptIdArray = item.getDeptId().split(SPLIT_SYMBOL);

			//部门可能是顶级部门，要查出来所有的子部门，并形成一个set集合返回
			Set<Long> deptIds = Arrays.stream(deptIdArray).flatMap(deptId -> {

				List<Long> deptChildIds = SysCache.getDeptChildIds(Long.valueOf(deptId));

				return deptChildIds.stream();

			}).collect(Collectors.toSet());

			//根据部门获取到所有用户id
			userIds = userDeptService.getUserIdsByDeptIds(deptIds);

		}

		//如果配置的用户id不是空的话，那么将其并入到根据部门查询出来的用户里
		if (StringUtils.isNotBlank(item.getPersonId())) {

			String[] personIds = item.getPersonId().split(SPLIT_SYMBOL);

			for (String personId : personIds) {
				userIds.add(Long.valueOf(personId));
			}
		}

		return userIds;

	}


	/**
	 * 获取部门以及其子部门的
	 *
	 * @param item scopeData
	 * @return 用户id集合
	 */
	private List<Long> getOwnDeptChild(ScopeDTO item) {

		String[] deptIdArray = AuthUtil.getDeptId().split(SPLIT_SYMBOL);

		//部门可能是顶级部门，要查出来所有的子部门，并形成一个set集合返回
		Set<Long> deptIds = Arrays.stream(deptIdArray).flatMap(deptId -> {

			List<Long> deptChildIds = SysCache.getDeptChildIds(Long.valueOf(deptId));

			return deptChildIds.stream();

		}).collect(Collectors.toSet());

		//根据部门获取到所有用户id
		return userDeptService.getUserIdsByDeptIds(deptIds);
	}


	/**
	 * 获取本级部门
	 *
	 * @param item scopeData
	 * @return 用户id集合
	 */
	private List<Long> getOwnDept(ScopeDTO item) {

		String[] deptIdArray = AuthUtil.getDeptId().split(SPLIT_SYMBOL);

		Set<Long> deptIds = Arrays.stream(deptIdArray)
			.map(Long::valueOf)
			.collect(Collectors.toSet());

		//根据部门获取到所有用户id
		return userDeptService.getUserIdsByDeptIds(deptIds);
	}


	/**
	 * 获取全部用户
	 *
	 * @param scopeDTO scopeData
	 * @return 用户集合
	 */
	private List<Long> getAllUserIds(ScopeDTO scopeDTO) {
		return this.getAllUserList().stream()
			.map(BaseEntity::getId)
			.collect(Collectors.toList());
	}

	/**
	 * 标记省内外人员
	 */
	@Override
	public Boolean markInProvince(List<Long> ids, Boolean inProvince) {
		UpdateWrapper<User> wrapper = new UpdateWrapper<>();
		wrapper.lambda()
			.in(User::getId, ids)
			.set(User::getInProvince, inProvince);
		return update(wrapper);
	}

	/**
	 * 获取权限用户
	 */
	@Override
	public List<Long> getScopeUserId(String DATA_SCOPE_CODE) {
		QueryWrapper<DataScope> dataScopeQueryWrapper = new QueryWrapper<>();
		dataScopeQueryWrapper.lambda().eq(DataScope::getResourceCode, DATA_SCOPE_CODE);
		DataScope dataScope = dataScopeService.getOne(dataScopeQueryWrapper);
		String personId = dataScope.getPersonId();
		List<Long> userIds = Arrays.stream(personId.split(","))
			.map(Long::valueOf)
			.collect(Collectors.toList());
		return userIds;
	}

	/**
	 * @param deptIds
	 * @return
	 */
	@Override
	public List<User> listByDeptIds(List<Long> deptIds) {
		Set<Long> deptIdList = deptIds.stream()
			.flatMap(deptId -> SysCache.getDeptChildIds(deptId).stream())
			.collect(Collectors.toSet());
		List<Long> userIds = userDeptService.getUserIdsByDeptIds(deptIdList);
		return this.list(
			Wrappers.<User>lambdaQuery().in(User::getId, userIds)
				.eq(User::getStatus, User.STATUS_ON));
	}

	/**
	 * 角色配置，在原有基础上累加
	 *
	 * @param userIds
	 * @param roleIds
	 * @return
	 */
	@Override
	public boolean grantPlus(List<Long> userIds, List<Long> roleIds) {
		List<User> users = this.listByIds(userIds);
		List<User> updates = new ArrayList<>();
		users.forEach(user -> {
			String roleId = user.getRoleId();
			List<Long> roleIdList = new ArrayList<>();
			if (StringUtils.isNotBlank(roleId)) {
				roleIdList.addAll(Func.toLongList(roleId));
			}
			roleIdList.addAll(roleIds);
			User upd = new User();
			upd.setId(user.getId());
			upd.setOpenId(user.getOpenId());
			upd.setRoleId(roleIdList.stream().distinct().map(Object::toString)
				.collect(Collectors.joining(",")));
			updates.add(upd);
		});
		return this.updateBatchById(updates);
	}
}
