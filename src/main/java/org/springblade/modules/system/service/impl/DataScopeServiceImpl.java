/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import static org.springblade.common.constant.CommonConstant.DATA_SCOPE_CATEGORY;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.system.entity.DataScope;
import org.springblade.modules.system.entity.RoleScope;
import org.springblade.modules.system.mapper.DataScopeMapper;
import org.springblade.modules.system.service.IDataScopeService;
import org.springblade.modules.system.service.IRoleScopeService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class DataScopeServiceImpl extends BaseServiceImpl<DataScopeMapper, DataScope> implements
    IDataScopeService {

  private final IRoleScopeService roleScopeService;

  @Override
  public List<DataScope> codeListByRoleId(Long roleId, String dataScopeCode) {
    return baseMapper.codeListByRoleId(roleId,dataScopeCode);
  }
}
