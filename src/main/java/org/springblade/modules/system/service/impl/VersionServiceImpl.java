/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.system.entity.VersionEntity;
import org.springblade.modules.system.mapper.VersionMapper;
import org.springblade.modules.system.service.IVersionService;
import org.springframework.stereotype.Service;

/**
 * 系统管理-版本管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Service
public class VersionServiceImpl extends BaseServiceImpl<VersionMapper, VersionEntity> implements
    IVersionService {


  /**
   * @param id
   * @return
   */
  @Override
  public boolean releaseById(Long id) {
    return SqlHelper.retBool(baseMapper.releaseById(id));
  }

  /**
   * @param id
   * @return
   */
  @Override
  public boolean releaseBackById(Long id) {
    return SqlHelper.retBool(baseMapper.releaseBackById(id));
  }

  /**
   * @return
   */
  @Override
  public VersionEntity getCurrentVersion(String type) {
    return baseMapper.getCurrentVersion(type);
  }
}
