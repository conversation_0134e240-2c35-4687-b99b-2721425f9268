package org.springblade.modules.notification.event;

import lombok.Getter;
import org.springblade.modules.notification.dto.NotificationDto;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @since 2023/2/9 16:51
 */
@Getter
public class NotificationEvent extends ApplicationEvent {


  private static final long serialVersionUID = 7357465822517640446L;
  private final NotificationDto notificationDto;
  private final Type[] type;


  public NotificationEvent(NotificationDto notificationDto) {
    super(notificationDto);
    this.notificationDto = notificationDto;
    this.type = new Type[]{Type.SOCKET, Type.MINIPROGRAM};
  }

  public NotificationEvent(NotificationDto notificationDto, Type... type) {
    super(notificationDto);
    this.notificationDto = notificationDto;
    this.type = type;
  }

  public enum Type {
    //发送socket
    SOCKET,
    //发送小程序
    MINIPROGRAM
  }
}
