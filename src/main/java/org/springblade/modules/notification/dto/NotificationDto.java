package org.springblade.modules.notification.dto;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.plugin.workflow.process.model.WfProcess;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @since 2023/2/9 16:47
 */
@Data
@NoArgsConstructor
public class NotificationDto implements Serializable {

	private static final long serialVersionUID = -4498748802582769484L;
	/**
	 * 全体用户
	 */
	public static final String RECEIVE_TYPE_ALL = "1";
	/**
	 * 指定部门
	 */
	public static final String RECEIVE_TYPE_DEPT = "2";
	/**
	 * 指定用户
	 */
	public static final String RECEIVE_TYPE_USER = "3";

	private String code;
	/**
	 * 标题
	 */
	private String title;
	/**
	 * 类型
	 */
	private String type;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 接收类型 1. 全体用户，2.制定部门，3. 指定用户
	 */
	private String receiveType;
	/**
	 * 接收id
	 */
	private List<Long> receiverId;
	/**
	 * 业务id
	 */
	private String businessId;


	/**
	 * 传递流程信息
	 */
	private WfProcess wfProcess;

}
