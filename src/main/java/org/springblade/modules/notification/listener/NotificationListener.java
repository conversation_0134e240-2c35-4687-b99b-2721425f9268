package org.springblade.modules.notification.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.natergy.ni.fin.entity.FinReportEntity;
import io.reactivex.functions.BiConsumer;
import java.time.LocalDate;
import java.util.function.Function;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.auth.dto.OffiaccountDTO;
import org.springblade.modules.auth.service.MiniprogramService;
import org.springblade.modules.auth.service.WeChatTemplateMessage;
import org.springblade.modules.notification.dto.NotificationDto;
import org.springblade.modules.notification.event.NotificationEvent;
import org.springblade.modules.notification.event.NotificationEvent.Type;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.ws.WebSocketServer;
import org.springblade.plugin.workflow.process.model.WfProcess;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/9/18 16:53
 */
@Slf4j
@ConditionalOnExpression("${notification.push.active:true}")
@Component
public class NotificationListener {

  @Value("${wechat.miniprogram.appId}")
  private String appId;

  /**
   * 流程通知，小程序跳转地址
   */
  private final static String JUMP_PATH_FLOW_TODO = "pages/workflow/workbench/index";

  /**
   * 公告通知，小程序跳转地址
   */
  private final static String JUMP_PATH_NOTICE = "pages/message/list";


  /**
   * 收到生日祝福跳转页面
   */
  private final static String JUMP_PATH_BIRTHDAY_NOTICE = "pages/birthday/birthday-receive";


  private final MiniprogramService miniprogramService;


  public NotificationListener(MiniprogramService miniprogramService) {

    this.miniprogramService = miniprogramService;

  }

  private final static Map<String, BiConsumer<NotificationDto, Long>> SWITCH_MAP = Maps.newHashMapWithExpectedSize(
      8);
  private final Map<Type, BiConsumer<NotificationDto, Long>> TYPE_OPERATIONS_MAP = Maps.newHashMapWithExpectedSize(
      8);

  @PostConstruct
  public void init() {
    SWITCH_MAP.put("flowTodo", this::sendFlowWork);
    SWITCH_MAP.put("notice", this::sendNotice);

    TYPE_OPERATIONS_MAP.put(Type.SOCKET, this::sendSocket);
    TYPE_OPERATIONS_MAP.put(Type.MINIPROGRAM, this::sendMiniProgram);
  }

  /**
   * 发送送各种通知
   *
   * @param event 监听的事件
   */
  @Async
  @Order
  @EventListener(NotificationEvent.class)
  public void pushMessage(NotificationEvent event) {
    NotificationDto notificationDto = event.getNotificationDto();
    Type[] types = event.getType();
    notificationDto.getReceiverId()
        .forEach(item -> {
          Stream.of(types).forEach(type-> {
            try {
              TYPE_OPERATIONS_MAP.get(type).accept(notificationDto, item);
            } catch (Exception e) {
              log.error(e.getMessage());
            }
          });
        });

  }


  /**
   * 流程节点发送通知消息
   *
   * @param notificationDto 通知信息
   * @param userId          发送的用户Id
   */
  @Async
  public void sendFlowWork(NotificationDto notificationDto, Long userId) {

    WfProcess detail = notificationDto.getWfProcess();

    List<Map<String, Object>> params = new ArrayList<>(10);

    Map<String, Object> serialNumberParam = Maps.newHashMapWithExpectedSize(1);
    serialNumberParam.put("character_string31", detail.getSerialNo());
    params.add(serialNumberParam);

    Map<String, Object> nameParam = Maps.newHashMapWithExpectedSize(1);
    nameParam.put("phrase4", detail.getProcessDefinitionName());
    params.add(nameParam);

    Map<String, Object> taskNameParam = Maps.newHashMapWithExpectedSize(1);
    taskNameParam.put("thing7", detail.getTaskName());
    params.add(taskNameParam);

    Map<String, Object> applyParam = Maps.newHashMapWithExpectedSize(1);
    applyParam.put("thing21", detail.getStartUsername());
    params.add(applyParam);

    Map<String, Object> dateTimeParam = Maps.newHashMapWithExpectedSize(1);
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    dateTimeParam.put("time3", dateFormat.format(detail.getCreateTime()));
    params.add(dateTimeParam);

    User user = UserCache.getUser(userId);

    if (StringUtils.isBlank(user.getOpenId())) {
      throw new RuntimeException(user.getName() + "没有绑定微信公众平台");
    }

    //先获取access_token
    OffiaccountDTO offiaccountAccessToken = miniprogramService.getOffiaccountAccessToken();

    WeChatTemplateMessage weChatTemplateMessage = new WeChatTemplateMessage.Builder()
        .setTemplate_id("xRn7hnFXevOwNUsW9R2MXrx_41gB2GW1Up2Z4nusdwk")
        .setDataValue(params)
        .setTouser(user.getOpenId())
        .setMiniprogram(new WeChatTemplateMessage.MiniProgram(appId, JUMP_PATH_FLOW_TODO))
        .builder();

    weChatTemplateMessage.sendMsg((jsonStr) -> miniprogramService.sendTemp(jsonStr,
        offiaccountAccessToken.getAccessToken()));
  }


  /**
   * 发送公告通知
   *
   * @param notificationDto 通知信息
   * @param userId          对应的用户
   */
  @Async
  public void sendNotice(NotificationDto notificationDto, Long userId) {

    Map<String, Object> templateParams = Maps.newHashMapWithExpectedSize(2);

    String templateId;

    String jumpUrl;

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    switch (notificationDto.getType()) {

      case "contractNotice":

        templateParams.put("thing1", notificationDto.getTitle());

        templateParams.put("time5", dateFormat.format(new Date()));

        templateId = "QAayTIupzowH-aDg5RK0KZCT-Vh4lkobf5NStNKext8";

        jumpUrl = JUMP_PATH_NOTICE;
        break;

      case "birthdayNotice":

        templateParams.put("thing2", notificationDto.getTitle());

        templateParams.put("time3", dateFormat.format(new Date()));

        templateId = "1skfd4B0lgwvORonS2sHZPpybpCoSmS5BgQJSEdTVDA";

        jumpUrl = JUMP_PATH_BIRTHDAY_NOTICE;
        break;

      default:

        templateParams.put("thing2", notificationDto.getTitle());

        templateParams.put("time3", dateFormat.format(new Date()));

        templateId = "1skfd4B0lgwvORonS2sHZPpybpCoSmS5BgQJSEdTVDA";

        jumpUrl = JUMP_PATH_NOTICE;

        break;


    }

    userNotice(userId, templateParams, templateId, jumpUrl);
  }


  private void userNotice(Long userId, Map<String, Object> templateParams, String templateId,
      String jumpUrl) {

    User user = UserCache.getUser(userId);

    if (StringUtils.isBlank(user.getOpenId())) {
      throw new RuntimeException(user.getName() + "没有绑定微信公众平台");
    }
    //先获取access_token
    OffiaccountDTO offiaccountAccessToken = miniprogramService.getOffiaccountAccessToken();

    List<Map<String, Object>> params = new ArrayList<>(10);

    templateParams.forEach((key, item) -> {
      Map<String, Object> titleParam = Maps.newHashMapWithExpectedSize(1);
      titleParam.put(key, item);
      params.add(titleParam);
    });

    WeChatTemplateMessage weChatTemplateMessage = new WeChatTemplateMessage.Builder()
        .setTemplate_id(templateId)
        .setDataValue(params)
        .setTouser(user.getOpenId())
        .setMiniprogram(new WeChatTemplateMessage.MiniProgram(appId, jumpUrl))
        .builder();
    weChatTemplateMessage.sendMsg((jsonStr) -> miniprogramService.sendTemp(jsonStr,
        offiaccountAccessToken.getAccessToken()));
  }

  @Async
  public void sendSocket(NotificationDto notificationDto, Long userId) {
    WebSocketServer.sendWholeAsyncMessage(userId, JSON.toJSONString(notificationDto));
  }

  public void sendMiniProgram(NotificationDto notificationDto, Long userId) {
    try {
      SWITCH_MAP.get(notificationDto.getCode()).accept(notificationDto, userId);
    } catch (Exception e) {
      log.error(e.getMessage());
    }
  }
}
