package org.springblade.modules.develop.service;

import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.TemplateConfig;
import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.query.IDatabaseQuery;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @since 2023/3/23 13:36
 */
public class ConfigBuilderLite {

  private final List<TableInfo> tableInfoList = new ArrayList<>();

  private final IDatabaseQuery databaseQuery1;

  /**
   *
   * @param configBuilder
   */
  public ConfigBuilderLite(
      @NotNull ConfigBuilder configBuilder) {
    Class<? extends IDatabaseQuery> databaseQueryClass = configBuilder.getDataSourceConfig()
        .getDatabaseQueryClass();
    try {
      Constructor<? extends IDatabaseQuery> declaredConstructor = databaseQueryClass.getDeclaredConstructor(
          ConfigBuilder.class);
      this.databaseQuery1 = declaredConstructor.newInstance(configBuilder);
    } catch (ReflectiveOperationException exception) {
      throw new RuntimeException("创建IDatabaseQuery实例出现错误:", exception);
    }
  }

  public TableInfo convertTableFields(TableInfo tableInfo) {
    if (databaseQuery1 instanceof SqlQueryLite) {
      ((SqlQueryLite) databaseQuery1).convertTableFields(tableInfo);
    }
    return tableInfo;
  }
}
