package org.springblade.modules.develop.dto;

import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @since 2023/3/23 13:20
 */
@Data
@NoArgsConstructor
public class TableInfoDTO implements Serializable {

  private static final long serialVersionUID = 6746746726083495364L;


  /**
   * 包导入信息
   */
  private final Set<String> importPackages = new TreeSet<>();

  /**
   * 是否转换
   */
  private boolean convert;

  /**
   * 表名称
   */
  private String name;

  /**
   * 表注释
   */
  private String comment;

  /**
   * 实体名称
   */
  private String entityName;

  /**
   * mapper名称
   */
  private String mapperName;

  /**
   * xml名称
   */
  private String xmlName;

  /**
   * service名称
   */
  private String serviceName;

  /**
   * serviceImpl名称
   */
  private String serviceImplName;

  /**
   * controller名称
   */
  private String controllerName;

  /**
   * 表字段
   */
  private final List<TableField> fields = new ArrayList<>();

  /**
   * 是否有主键
   */
  private boolean havePrimaryKey;

  /**
   * 公共字段
   */
  private final List<TableField> commonFields = new ArrayList<>();

  /**
   * 字段名称集
   */
  private String fieldNames;

  public TableInfoDTO(TableInfo tableInfo) {
    super();
    BeanUtils.copyProperties(tableInfo, this);
    this.setComment(tableInfo.getName() + StringPool.COLON + tableInfo.getComment());
  }
}
