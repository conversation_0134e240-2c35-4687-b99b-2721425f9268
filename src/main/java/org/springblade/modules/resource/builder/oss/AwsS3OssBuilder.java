/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.builder.oss;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import lombok.SneakyThrows;
import org.springblade.core.oss.OssTemplate;
import org.springblade.core.oss.props.OssProperties;
import org.springblade.core.oss.rule.OssRule;
import org.springblade.modules.resource.entity.Oss;
import org.springblade.core.oss.AwsS3Template;

/**
 * S3云存储构建类
 *
 * <AUTHOR>
 */
public class AwsS3OssBuilder {

  @SneakyThrows
  public static OssTemplate template(Oss oss, OssRule ossRule) {
    // 创建配置类
    OssProperties ossProperties = new OssProperties();
    ossProperties.setEndpoint(oss.getEndpoint());
    ossProperties.setAccessKey(oss.getAccessKey());
    ossProperties.setSecretKey(oss.getSecretKey());
    ossProperties.setBucketName(oss.getBucketName());
    AmazonS3ClientBuilder  client = AmazonS3ClientBuilder.standard();
    AWSCredentials acre = new BasicAWSCredentials(oss.getAccessKey(), oss.getSecretKey());
    ClientConfiguration config = new ClientConfiguration();
    config.setProtocol(com.amazonaws.Protocol.HTTPS);
    config.setConnectionTimeout(10001);
    config.setSignerOverride("S3SignerType");
    AWSCredentialsProvider acrep = new AWSStaticCredentialsProvider(acre);
    AwsClientBuilder.EndpointConfiguration econfig = new AwsClientBuilder.EndpointConfiguration(oss.getEndpoint(), null);
    client.setClientConfiguration(config);
    client.setCredentials(acrep);
    client.setEndpointConfiguration(econfig);

    AmazonS3 build = client.build();
    return new AwsS3Template(build, ossRule, ossProperties);
  }

}
