/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.mapper;

import org.springblade.modules.resource.entity.SmsLogEntity;
import org.springblade.modules.resource.vo.SmsLogVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 短信日志表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
public interface SmsLogMapper extends BaseMapper<SmsLogEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param smsLog
	 * @return
	 */
	List<SmsLogVO> selectSmsLogPage(IPage page, SmsLogVO smsLog);


}
