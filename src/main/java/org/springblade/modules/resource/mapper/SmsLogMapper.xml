<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.resource.mapper.SmsLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="smsLogResultMap" type="org.springblade.modules.resource.entity.SmsLogEntity">
        <result column="tenant_id" property="tenantId"/>
        <result column="category" property="category"/>
        <result column="sms_code" property="smsCode"/>
        <result column="template_id" property="templateId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="type" property="type"/>
        <result column="content" property="content"/>
        <result column="id" property="id"/>
        <result column="phone" property="phone"/>
        <result column="date" property="date"/>
        <result column="ip" property="ip"/>
    </resultMap>


    <select id="selectSmsLogPage" resultMap="smsLogResultMap">
        select * from blade_sms_log where is_deleted = 0
    </select>


</mapper>
