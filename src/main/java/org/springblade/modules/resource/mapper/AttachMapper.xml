<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.resource.mapper.AttachMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="attachResultMap" type="org.springblade.modules.resource.vo.AttachVO">
    <result column="id" property="id"/>
    <result column="create_user" property="createUser"/>
    <result column="create_user_name" property="createUserName"/>
    <result column="create_dept" property="createDept"/>
    <result column="create_time" property="createTime"/>
    <result column="update_user" property="updateUser"/>
    <result column="update_time" property="updateTime"/>
    <result column="status" property="status"/>
    <result column="is_deleted" property="isDeleted"/>
    <result column="link" property="link"/>
    <result column="domain_url" property="domainUrl"/>
    <result column="name" property="name"/>
    <result column="original_name" property="originalName"/>
    <result column="extension" property="extension"/>
    <result column="attach_size" property="attachSize"/>
    <result column="business_key" property="businessKey"/>
    <result column="business_name" property="businessName"/>
  </resultMap>


  <select id="selectAttachPage" resultMap="attachResultMap">
    select *,
    (select name
    from blade_user
    where blade_user.is_deleted = 0 and id = blade_attach.create_user) as create_user_name
    from blade_attach
    where is_deleted = 0
    <if test="param2.id!=null">
      and id = #{param2.id,jdbcType=BIGINT}
    </if>
    <if test="param2.domainUrl!=null and param2.domainUrl!=''">
      and domain_url like concat('%',#{param2.domainUrl,jdbcType=VARCHAR},'%')
    </if>
    <if test="param2.name!=null and param2.name!=''">
      and name like concat('%',#{param2.name,jdbcType=VARCHAR},'%')
    </if>
    <if test="param2.originalName!=null and param2.originalName!=''">
      and original_name like concat('%',#{param2.originalName,jdbcType=VARCHAR},'%')
    </if>
    <if test="param2.businessKey!=null and param2.businessKey!=''">
      and business_key in
        <foreach collection="param2.businessKey.split(',')" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
    </if>
    <if test="param2.businessName!=null and param2.businessName!=''">
      and business_name= #{param2.businessName,jdbcType=VARCHAR}
    </if>
    <if test="param2.ids!=null and param2.ids!=''">
      and id in
      <foreach collection="param2.ids.split(',')" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by id desc
  </select>
</mapper>
