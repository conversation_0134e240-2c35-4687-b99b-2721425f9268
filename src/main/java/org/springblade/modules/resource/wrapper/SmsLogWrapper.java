/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.resource.entity.SmsLogEntity;
import org.springblade.modules.resource.vo.SmsLogVO;
import java.util.Objects;

/**
 * 短信日志表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
public class SmsLogWrapper extends BaseEntityWrapper<SmsLogEntity, SmsLogVO>  {

	public static SmsLogWrapper build() {
		return new SmsLogWrapper();
 	}

	@Override
	public SmsLogVO entityVO(SmsLogEntity smsLog) {
		SmsLogVO smsLogVO = Objects.requireNonNull(BeanUtil.copy(smsLog, SmsLogVO.class));

		//User createUser = UserCache.getUser(smsLog.getCreateUser());
		//User updateUser = UserCache.getUser(smsLog.getUpdateUser());
		//smsLogVO.setCreateUserName(createUser.getName());
		//smsLogVO.setUpdateUserName(updateUser.getName());

		return smsLogVO;
	}


}
