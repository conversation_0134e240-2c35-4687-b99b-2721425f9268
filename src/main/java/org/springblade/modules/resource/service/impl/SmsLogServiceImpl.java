/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.service.impl;

import org.springblade.modules.resource.entity.SmsLogEntity;
import org.springblade.modules.resource.vo.SmsLogVO;
import org.springblade.modules.resource.mapper.SmsLogMapper;
import org.springblade.modules.resource.service.ISmsLogService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 短信日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Service
public class SmsLogServiceImpl extends BaseServiceImpl<SmsLogMapper, SmsLogEntity> implements ISmsLogService {

	@Override
	public IPage<SmsLogVO> selectSmsLogPage(IPage<SmsLogVO> page, SmsLogVO smsLog) {
		return page.setRecords(baseMapper.selectSmsLogPage(page, smsLog));
	}


}
