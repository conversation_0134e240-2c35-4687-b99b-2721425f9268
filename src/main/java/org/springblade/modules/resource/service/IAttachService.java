/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springblade.modules.resource.dto.AttachDTO;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.vo.AttachVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 附件表 服务类
 *
 * <AUTHOR>
 */
public interface IAttachService extends BaseService<Attach> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param attach
	 * @return
	 */
	IPage<AttachVO> selectAttachPage(IPage<AttachVO> page, AttachDTO attach);

	/**
	 * 删除业务关联中不在关联的附件
	 *
	 * @param businessName
	 * @param id
	 * @param collect
	 * @return
	 */
	boolean removeBusinessUnLink(String businessName, String businessKey, List<Long> ids);

	/**
	 * 将附件表数据关联业务表
	 *
	 * @param ids
	 * @param businessName
	 * @param businessKey
	 * @return
	 */
	boolean linkBusiness(List<Long> ids, String businessName, String businessKey);

	List<Attach> listByBusiness(String businessName, String businessKey);

	boolean updateBusinessByIds(List<Long> ids, String businessKey, String businessName);

	String getLinkById(Long id);

	List<Attach> linkListByIds(Collection<Long> ids);

	List<AttachVO> selectAttachList(AttachDTO attach);

	List<Attach> getLinksByBusinessKeysAndBusinessName(String businessKeys, String businessName);
}
