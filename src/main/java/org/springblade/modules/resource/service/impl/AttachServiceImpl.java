/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.beust.ah.A;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.ParamCache;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.oss.OssTemplate;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.builder.oss.OssBuilder;
import org.springblade.modules.resource.dto.AttachDTO;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.entity.Oss;
import org.springblade.modules.resource.mapper.AttachMapper;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.resource.vo.AttachVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 附件表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AttachServiceImpl extends BaseServiceImpl<AttachMapper, Attach> implements
	IAttachService {

	private final OssBuilder ossBuilder;
	public static final String OSS_FILE_EXPIRES_KEY = "oss.file.expires";

	/**
	 * @param attach
	 * @return
	 */
	@Override
	public List<AttachVO> selectAttachList(AttachDTO attach) {
		return baseMapper.selectAttachPage(null, attach);
	}

	/**
	 * @param businessKeys
	 * @param businessName
	 * @return
	 */
	@Override
	public List<Attach> getLinksByBusinessKeysAndBusinessName(String businessKeys,
		String businessName) {
		List<Attach> list = listByBusiness(businessName, businessKeys);
		return getLinks(list);
	}

	@Override
	public IPage<AttachVO> selectAttachPage(IPage<AttachVO> page, AttachDTO attach) {
		return page.setRecords(baseMapper.selectAttachPage(page, attach));
	}

	/**
	 * 只是将数据逻辑删除，不删除附件文件
	 *
	 * @param businessName
	 * @param businessKey
	 * @param ids
	 * @return
	 */
	@Override
	public boolean removeBusinessUnLink(String businessName, String businessKey, List<Long> ids) {
		QueryWrapper<Attach> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(Attach::getBusinessName, businessName)
			.eq(Attach::getBusinessKey, businessKey)
			.notIn(Attach::getId, ids);
		return remove(wrapper);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean linkBusiness(List<Long> ids, String businessName, String businessKey) {
		boolean res = removeBusinessUnLink(businessName, businessKey, ids);
		UpdateWrapper<Attach> wrapper = new UpdateWrapper<>();
		wrapper.lambda().in(Attach::getId, ids)
			.set(Attach::getBusinessName, businessName)
			.set(Attach::getBusinessKey, businessKey);
		return update(wrapper);
	}

	@Override
	public List<Attach> listByBusiness(String businessName, String businessKey) {
		QueryWrapper<Attach> wrapper = new QueryWrapper<>();
		wrapper.lambda().in(Attach::getBusinessKey, Func.toStrList(businessKey))
			.eq(Attach::getBusinessName, businessName);
		return list(wrapper);
	}

	@Override
	public boolean updateBusinessByIds(@NonNull List<Long> ids, @NonNull String businessKey,
		@NonNull String businessName) {
		LambdaUpdateWrapper<Attach> wrapper = new LambdaUpdateWrapper<>();
		wrapper.in(Attach::getId, ids)
			.set(Attach::getBusinessKey, businessKey)
			.set(Attach::getBusinessName, businessName);
		return update(wrapper);
	}

	/**
	 * @param id
	 * @return
	 */
	@Override
	public String getLinkById(Long id) {
		Attach attach = getById(id);
		if (attach == null) {
			log.error("没有找到附件");
			return "";
		}
		List<Attach> attaches = getLinks(Collections.singletonList(attach));
		return attaches.get(0).getLink();
	}


	/**
	 * @param ids
	 * @return
	 */
	@Override
	public List<Attach> linkListByIds(Collection<Long> ids) {
		List<Attach> attaches = listByIds(ids);
		if (attaches == null || attaches.isEmpty()) {
			return new ArrayList<>();
		}
		return getLinks(attaches);
	}

	private List<Attach> getLinks(List<Attach> attaches) {
		String tenantId;
		if (StringUtils.isBlank(AuthUtil.getTenantId())) {
			tenantId = BladeConstant.ADMIN_TENANT_ID;
		} else {
			tenantId = AuthUtil.getTenantId();
		}
		Set<String> ossCodes = attaches.stream().map(Attach::getOssCode)
			.filter(StringUtils::isNotBlank).collect(Collectors.toSet());
		Map<String, Oss> ossMap = ossCodes.stream().collect(
			Collectors.toMap(Function.identity(), ossCode -> ossBuilder.getOss(tenantId, ossCode)));
		Long expires = Long.valueOf(ParamCache.getValue(OSS_FILE_EXPIRES_KEY));
		attaches.stream().filter(attach -> StringUtils.isNotBlank(attach.getOssCode()))
			.forEach(attach -> {
				Oss oss = ossMap.get(attach.getOssCode());
				OssTemplate template = ossBuilder.template(attach.getOssCode());
				if (template != null && oss != null) {
					if (oss.getAccessPolicy().equals(Oss.ACCESS_POLICY_PRIVATE)) {
						attach.setLink(template.fileLink(attach.getName(), expires));
					} else {
						attach.setLink(template.fileLink(attach.getName()));
					}
				}
			});
		return attaches;
	}

}
