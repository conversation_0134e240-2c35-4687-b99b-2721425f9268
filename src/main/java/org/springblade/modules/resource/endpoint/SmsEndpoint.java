/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.endpoint;

import static org.springblade.modules.resource.utils.SmsUtil.PARAM_KEY;
import static org.springblade.modules.resource.utils.SmsUtil.SEND_FAIL;
import static org.springblade.modules.resource.utils.SmsUtil.SEND_SUCCESS;
import static org.springblade.modules.resource.utils.SmsUtil.VALIDATE_FAIL;
import static org.springblade.modules.resource.utils.SmsUtil.VALIDATE_SUCCESS;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.ParamCache;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.launch.server.ServerInfo;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.sms.model.SmsCode;
import org.springblade.core.sms.model.SmsData;
import org.springblade.core.sms.model.SmsResponse;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.modules.resource.builder.sms.SmsBuilder;
import org.springblade.modules.resource.entity.Sms;
import org.springblade.modules.resource.entity.SmsLogEntity;
import org.springblade.modules.resource.enums.SmsCodeEnum;
import org.springblade.modules.resource.service.ISmsLogService;
import org.springblade.modules.resource.utils.SmsUtil;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短信服务端点
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_RESOURCE_NAME + "/sms/endpoint")
@Api(value = "短信服务端点", tags = "短信服务端点")
public class SmsEndpoint {

  /**
   * 当天最大验证码短信发送量
   */
  public static final String TODAY_MAX_SEND_VALID_SMS_NUMBER = "auth.phone.send-validate.max";
  /**
   * 短信验证码过期时间
   */
  public static final String SEND_VALID_EXPIRES_IN = "auth.phone.send-validate.expires_in";
  /**
   * 短信服务构建类
   */
  private final SmsBuilder smsBuilder;
  private final ISmsLogService smsLogService;
  private final ServerInfo serverInfo;
  private final BladeRedis bladeRedis;
  //================================= 短信服务校验 =================================

  /**
   * 短信验证码发送
   *
   * @param phone 手机号
   */
  @SneakyThrows
  @PostMapping("/send-validate")
  public R sendValidate(@RequestParam String phone) {
    long sendNum = smsLogService.count(
        Wrappers.lambdaQuery(SmsLogEntity.class).gt(SmsLogEntity::getDate, LocalDateTime.of(
                LocalDate.now(), LocalTime.MIN)).lt(SmsLogEntity::getDate, LocalDateTime.of(
                LocalDate.now(), LocalTime.MAX))
            .eq(SmsLogEntity::getType, SmsCodeEnum.VALIDATE.getName())
            .eq(SmsLogEntity::getPhone, phone));
    String max = ParamCache.getValue(TODAY_MAX_SEND_VALID_SMS_NUMBER);
    Assert.isTrue(sendNum <= Integer.parseInt(max), "本日发送短信验证码数已达上限！");
    Map<String, String> params = SmsUtil.getValidateParams();
    String tenantId = StringUtils.isNotBlank(AuthUtil.getTenantId()) ? AuthUtil.getTenantId()
        : BladeConstant.ADMIN_TENANT_ID;
    Sms sms = smsBuilder.getSms(tenantId, StringPool.EMPTY);
    SmsCode smsCode = smsBuilder.template(tenantId, sms)
        .sendValidate(new SmsData(params).setKey(PARAM_KEY), phone);
    smsCode.setPhone(phone);
    saveSmsLog(sms, smsCode, SmsCodeEnum.VALIDATE);
    String expiresIn = ParamCache.getValue(SEND_VALID_EXPIRES_IN);

    // 存入redis并设置过期时间为30分钟
    bladeRedis.setEx(CacheNames.PHONE_VALIDATE_KEY + phone, smsCode.getValue(),
        Duration.ofMinutes(Integer.parseInt(expiresIn)));
    return smsCode.isSuccess() ? R.data(smsCode, SEND_SUCCESS) : R.fail(SEND_FAIL);
  }

  private void saveSmsLog(Sms sms, SmsCode smsCode, SmsCodeEnum smsCodeEnum) {
    SmsLogEntity log = new SmsLogEntity();
    log.setType(smsCodeEnum.getName());
    log.setIp(serverInfo.getIp());
    log.setCategory(sms.getCategory());
    log.setSmsCode(sms.getSmsCode());
    log.setTemplateId(sms.getTemplateId());
    log.setContent(smsCode.getValue());
    log.setPhone(smsCode.getPhone());
    log.setDate(new Date());
    log.setSuccess(smsCode.isSuccess());
    smsLogService.save(log);
  }

  /**
   * 校验短信
   *
   * @param smsCode 短信校验信息
   */
  @SneakyThrows
  @PostMapping("/validate-message")
  public R validateMessage(SmsCode smsCode) {
    boolean validate = smsBuilder.template().validateMessage(smsCode);
    return validate ? R.success(VALIDATE_SUCCESS) : R.fail(VALIDATE_FAIL);
  }

  //========== 通用短信自定义发送(支持自定义params参数传递, 推荐用于测试, 不推荐用于生产环境) ==========

  /**
   * 发送信息
   *
   * @param code   资源编号
   * @param params 自定义短信参数
   * @param phones 手机号集合
   */
  @SneakyThrows
  @PostMapping("/send-message")
  public R sendMessage(@RequestParam String code, @RequestParam String params,
      @RequestParam String phones) {
    SmsData smsData = new SmsData(JsonUtil.readMap(params, String.class, String.class));
    return send(code, smsData, phones);
  }

  //========== 指定短信服务发送(可根据各种场景自定拓展定制, 损失灵活性增加安全性, 推荐用于生产环境) ==========

  /**
   * 短信通知
   *
   * @param phones 手机号集合
   */
  @SneakyThrows
  @PostMapping("/send-notice")
  public R sendNotice(@RequestParam String phones) {
    Map<String, String> params = new HashMap<>(3);
    params.put("title", "通知标题");
    params.put("content", "通知内容");
    params.put("date", "通知时间");
    SmsData smsData = new SmsData(params);
    return send(smsData, phones);
  }

  /**
   * 订单通知
   *
   * @param phones 手机号集合
   */
  @SneakyThrows
  @PostMapping("/send-order")
  public R sendOrder(@RequestParam String phones) {
    Map<String, String> params = new HashMap<>(3);
    params.put("orderNo", "订单编号");
    params.put("packageNo", "快递单号");
    params.put("user", "收件人");
    SmsData smsData = new SmsData(params);
    return send(smsData, phones);
  }

  /**
   * 会议通知
   *
   * @param phones 手机号集合
   */
  @SneakyThrows
  @PostMapping("/send-meeting")
  public R sendMeeting(@RequestParam String phones) {
    Map<String, String> params = new HashMap<>(2);
    params.put("roomId", "会议室");
    params.put("topic", "会议主题");
    params.put("date", "会议时间");
    SmsData smsData = new SmsData(params);
    return send(smsData, phones);
  }

  //================================= 通用短信发送接口 =================================

  /**
   * 通用短信发送接口
   *
   * @param smsData 短信内容
   * @param phones  手机号列表
   * @return 是否发送成功
   */
  private R send(SmsData smsData, String phones) {
    SmsResponse response = smsBuilder.template().sendMessage(smsData, Func.toStrList(phones));
    return response.isSuccess() ? R.success(SEND_SUCCESS) : R.fail(SEND_FAIL);
  }

  /**
   * 通用短信发送接口
   *
   * @param code    资源编号
   * @param smsData 短信内容
   * @param phones  手机号列表
   * @return 是否发送成功
   */
  private R send(String code, SmsData smsData, String phones) {
    SmsResponse response = smsBuilder.template(code).sendMessage(smsData, Func.toStrList(phones));
    return response.isSuccess() ? R.success(SEND_SUCCESS) : R.fail(SEND_FAIL);
  }

}
