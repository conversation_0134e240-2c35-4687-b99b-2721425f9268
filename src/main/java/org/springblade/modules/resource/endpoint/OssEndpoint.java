/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.endpoint;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.oss.model.OssFile;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springblade.modules.resource.builder.oss.OssBuilder;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.entity.Oss;
import org.springblade.modules.resource.service.IAttachService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

import static org.springblade.core.tool.utils.FileUtil.getFileExtension;

/**
 * 对象存储端点
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@Api(value = "对象存储端点", tags = "对象存储端点")
@RequestMapping(AppConstant.APPLICATION_RESOURCE_NAME + "/oss/endpoint")
public class OssEndpoint {

  /**
   * 对象存储构建类
   */
  private final OssBuilder ossBuilder;

  /**
   * 附件表服务
   */
  private final IAttachService attachService;

  /**
   * 创建存储桶
   *
   * @param bucketName 存储桶名称
   * @return Bucket
   */
  @SneakyThrows
  @PostMapping("/make-bucket")
  @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
  public R makeBucket(@RequestParam String bucketName) {
    ossBuilder.template().makeBucket(bucketName);
    return R.success("创建成功");
  }

  /**
   * 创建存储桶
   *
   * @param bucketName 存储桶名称
   * @return R
   */
  @SneakyThrows
  @PostMapping("/remove-bucket")
  @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
  public R removeBucket(@RequestParam String bucketName) {
    ossBuilder.template().removeBucket(bucketName);
    return R.success("删除成功");
  }

  /**
   * 拷贝文件
   *
   * @param fileName       存储桶对象名称
   * @param destBucketName 目标存储桶名称
   * @param destFileName   目标存储桶对象名称
   * @return R
   */
  @SneakyThrows
  @PostMapping("/copy-file")
  public R copyFile(@RequestParam String fileName, @RequestParam String destBucketName,
      String destFileName) {
    ossBuilder.template().copyFile(fileName, destBucketName, destFileName);
    return R.success("操作成功");
  }

  /**
   * 获取文件信息
   *
   * @param fileName 存储桶对象名称
   * @return InputStream
   */
  @SneakyThrows
  @GetMapping("/stat-file")
  public R<OssFile> statFile(@RequestParam String fileName) {
    return R.data(ossBuilder.template().statFile(fileName));
  }

  /**
   * 获取文件相对路径
   *
   * @param fileName 存储桶对象名称
   * @return String
   */
  @SneakyThrows
  @GetMapping("/file-path")
  public R<String> filePath(@RequestParam String fileName) {
    return R.data(ossBuilder.template().filePath(fileName));
  }


  /**
   * 获取文件外链
   *
   * @param fileName 存储桶对象名称
   * @return String
   */
  @SneakyThrows
  @GetMapping("/file-link")
  public R<String> fileLink(@RequestParam String fileName) {
    return R.data(ossBuilder.template().fileLink(fileName));
  }


	/**
   * 上传文件
   *
   * @param file 文件
   * @return ObjectStat
   */
  @SneakyThrows
  @PostMapping("/put-file")
  public R<BladeFile> putFile(@RequestParam MultipartFile file) {
    BladeFile bladeFile = ossBuilder.template()
        . putFile(file.getOriginalFilename(), file.getInputStream());
    return R.data(bladeFile);
  }

  /**
   * 上传文件
   *
   * @param fileName 存储桶对象名称
   * @param file     文件
   * @return ObjectStat
   */
  @SneakyThrows
  @PostMapping("/put-file-by-name")
  public R<BladeFile> putFile(@RequestParam String fileName, @RequestParam MultipartFile file) {
    BladeFile bladeFile = ossBuilder.template().putFile(fileName, file.getInputStream());
    return R.data(bladeFile);
  }

  /**
   * 上传文件并保存至附件表
   *
   * @param file 文件
   * @return ObjectStat
   */
  @SneakyThrows
  @PostMapping("/put-file-attach")
  public R<BladeFile> putFileAttach(@RequestParam MultipartFile file) {
    String fileName = file.getOriginalFilename();
    BladeFile bladeFile = ossBuilder.template().putFile(fileName, file.getInputStream());
    Long attachId = buildAttach(fileName, file.getSize(), bladeFile);
    bladeFile.setAttachId(attachId);
    return R.data(bladeFile);
  }

	/**
	 * 上传文件并保存至附件表，后台程序（本地）调用
	 * @param file 文件
	 * @return
	 */
	@SneakyThrows
	public R<BladeFile> putFileAttachLocal(MultipartFile file) {
		String fileName = file.getOriginalFilename();
		BladeFile bladeFile = ossBuilder.template().putFile(fileName, file.getInputStream());
		Long attachId = buildAttach(fileName, file.getSize(), bladeFile);
		bladeFile.setAttachId(attachId);
		return R.data(bladeFile);
	}

	/**
	 * 上传png图片并保存至附件表，后台程序（本地）调用,发票快照专用
//	 * @param file 文件
	 * @return
	 */
	@SneakyThrows
	public R<BladeFile> putFileAttachLocal(String fileName, String baseImg) {
		// 解码 Base64 字符串
		baseImg = baseImg.replace("data:image/png;base64,", "");
		byte[] imageBytes = Base64.getDecoder().decode(baseImg);
		InputStream inputStream = new ByteArrayInputStream(imageBytes);
		Long fileSize = (long) imageBytes.length;
		BladeFile bladeFile = ossBuilder.template().putFile(fileName, inputStream);
		Long attachId = buildAttach1(fileName, fileSize, bladeFile, BladeConstant.ADMIN_TENANT_ID);
		bladeFile.setAttachId(attachId);
		return R.data(bladeFile);
	}

	/**
	 * 上传各种文件并保存至附件表，后台程序（本地）调用，保存文件流
	 //	 * @param file 文件
	 * @return
	 */
	@SneakyThrows
	public R<BladeFile> putFileAttach(String fileName, ByteArrayInputStream inputStream) {
		Long length = 0L;
		while (inputStream.read() != -1) {
			length++;
		}
		// 重置流的位置以便后续使用
		inputStream.reset();

		Long fileSize = length;
		BladeFile bladeFile = ossBuilder.template().putFile(fileName, inputStream);
		Long attachId = buildAttach1(fileName, fileSize, bladeFile, BladeConstant.ADMIN_TENANT_ID);
		bladeFile.setAttachId(attachId);
		return R.data(bladeFile);


//		@RequestParam String businessKey,
//		@RequestParam MultipartFile file) {
//			String fileName = file.getOriginalFilename();
//			BladeFile bladeFile = ossBuilder.template().putFile(fileName, file.getInputStream());
//			String tenantId = AuthUtil.getTenantId();
//			Oss oss = ossBuilder.getOss(tenantId, null);
//			Long attachId = buildAttach(fileName, file.getSize(), bladeFile, businessName, businessKey,oss.getOssCode());
//			bladeFile.setAttachId(attachId);
//			return R.data(bladeFile);



	}

	/**
	 * 上传文件并保存至附件表(带水印)
	 *
	 * @param file 文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-file-attach-watermark")
	public R<BladeFile> putFileAttachWatermark(@RequestParam MultipartFile file) {
		String fileName = file.getOriginalFilename();

		// 添加水印
		InputStream imageInputStream = file.getInputStream();
		BufferedImage image = ImageIO.read(imageInputStream);
		addWatermark(image);

		// 保存带水印的图片（将带水印的图像写回原始文件）
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

		ImageIO.write(image, "jpg", outputStream); // 修改图片格式为jpg格式
		BladeFile bladeFile = ossBuilder.template().putFile(fileName, new ByteArrayInputStream(outputStream.toByteArray()));

		Long attachId = buildAttach(fileName, file.getSize(), bladeFile);
		bladeFile.setAttachId(attachId);

		return R.data(bladeFile);
	}

	private void addWatermark(BufferedImage image) {
		Graphics2D g2d = image.createGraphics();
		g2d.setColor(Color.gray); // 设置水印文字颜色
		g2d.setFont(new Font("Arial", Font.BOLD, 66)); // 设置水印文字字体和大小

		// 获取当前时间并将其作为水印文字
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String watermarkText = dateFormat.format(new Date());

		// 计算水印文字的长度
		FontMetrics fm = g2d.getFontMetrics();
		int textWidth = fm.stringWidth(watermarkText);
		int textHeight = fm.getHeight();

		// 水印文字的位置
		int x = image.getWidth() - textWidth; // 距离右边缘的距离
		int y = image.getHeight() - textHeight; // 距离底边缘的距离

		// 添加水印文字
		g2d.drawString(watermarkText, x, y);

		g2d.dispose();
	}

  /**
   * 上传文件并保存至附件表
   *
   * @param fileName 存储桶对象名称
   * @param file     文件
   * @return ObjectStat
   */
  @SneakyThrows
  @PostMapping("/put-file-attach-by-name")
  public R<BladeFile> putFileAttach(@RequestParam String fileName,
      @RequestParam MultipartFile file) {
    BladeFile bladeFile = ossBuilder.template().putFile(fileName, file.getInputStream());
    Long attachId = buildAttach(fileName, file.getSize(), bladeFile);
    bladeFile.setAttachId(attachId);
    return R.data(bladeFile);
  }

  /**
   * 构建附件表
   *
   * @param fileName  文件名
   * @param fileSize  文件大小
   * @param bladeFile 对象存储文件
   * @return attachId
   */
  public Long buildAttach(String fileName, Long fileSize, BladeFile bladeFile) {
	  String tenantId = AuthUtil.getTenantId();
	  Oss oss = ossBuilder.getOss(tenantId, null);
    return buildAttach(fileName, fileSize, bladeFile, null, null,oss.getOssCode());
  }

	/**
	 * 构建附件表，发货快照专用
	 *
	 * @param fileName  文件名
	 * @param fileSize  文件大小
	 * @param bladeFile 对象存储文件
	 * @return attachId
	 */
	public Long buildAttach1(String fileName, Long fileSize, BladeFile bladeFile, String tenantId) {
//		String tenantId = AuthUtil.getTenantId();
		Oss oss = ossBuilder.getOss(tenantId, null);
		return buildAttach(fileName, fileSize, bladeFile, null, null,oss.getOssCode());
	}

  /**
   * 删除文件
   *
   * @param fileName 存储桶对象名称
   * @return R
   */
  @SneakyThrows
  @PostMapping("/remove-file")
  @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
  public R removeFile(@RequestParam String fileName) {
    ossBuilder.template().removeFile(fileName);
    return R.success("操作成功");
  }

  /**
   * 批量删除文件
   *
   * @param fileNames 存储桶对象名称集合
   * @return R
   */
  @SneakyThrows
  @PostMapping("/remove-files")
  @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
  public R removeFiles(@RequestParam String fileNames) {
    ossBuilder.template().removeFiles(Func.toStrList(fileNames));
    return R.success("操作成功");
  }


  /**
   * 上传文件并保存至附件表 带业务类型和业务主键
   *
   * @param file 文件
   * @return ObjectStat
   */
  @SneakyThrows
  @PostMapping("/put-file-attach-business")
  public R<BladeFile> putFileAttach(@RequestParam String businessName,
      @RequestParam String businessKey,
	  @RequestParam(defaultValue = "public") String code,
      @RequestParam MultipartFile file) {
    String fileName = file.getOriginalFilename();
    BladeFile bladeFile = ossBuilder.template().putFile(fileName, file.getInputStream());
	  String tenantId = AuthUtil.getTenantId();
	  Oss oss = ossBuilder.getOss(tenantId, code);
    Long attachId = buildAttach(fileName, file.getSize(), bladeFile, businessName, businessKey,oss.getOssCode());
    bladeFile.setAttachId(attachId);
    return R.data(bladeFile);
  }

	@SneakyThrows
	@PostMapping("employee/file")
	public R<BladeFile> employeeUpload(@RequestParam MultipartFile file) {
		String fileName = file.getOriginalFilename();
		BladeFile bladeFile = ossBuilder.template().putFile(fileName, file.getInputStream());

		Oss oss = ossBuilder.getOss(TokenUtil.DEFAULT_TENANT_ID, null);
		Long attachId = buildAttach(fileName, file.getSize(), bladeFile, null, null,oss.getOssCode());
		bladeFile.setAttachId(attachId);
		return R.data(bladeFile);
	}


  /**
   * 构建附件表
   *
   * @param fileName  文件名
   * @param fileSize  文件大小
   * @param bladeFile 对象存储文件
   * @return attachId
   */
  private Long buildAttach(String fileName, Long fileSize, BladeFile bladeFile, String businessName,
      String businessKey,String ossCode) {
    String fileExtension = getFileExtension(fileName);
    Attach attach = new Attach();
    attach.setDomainUrl(bladeFile.getDomain());
    attach.setLink(bladeFile.getLink());
    attach.setName(bladeFile.getName());
    attach.setOriginalName(bladeFile.getOriginalName());
    attach.setAttachSize(fileSize);
    attach.setExtension(fileExtension);
    attach.setBusinessName(businessName);
    attach.setBusinessKey(businessKey);
	  attach.setOssCode(ossCode);
    attachService.save(attach);
    return attach.getId();
  }
}
