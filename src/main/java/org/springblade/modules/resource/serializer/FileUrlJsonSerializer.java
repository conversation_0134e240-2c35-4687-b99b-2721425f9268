package org.springblade.modules.resource.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/01/02
 */
@Component
@NoArgsConstructor
public class FileUrlJsonSerializer extends JsonSerializer<Object> {

	private static IAttachService attachService;
	private final static String SLASH_SYMBOL = "/";

	@Autowired
	public FileUrlJsonSerializer(IAttachService attachService) {

		FileUrlJsonSerializer.attachService = attachService;
	}


	@Override
	public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
		Object currentValue = gen.getOutputContext().getCurrentValue();

		Long fileId = null;

		String fileFullUrl = "";

		if (currentValue instanceof BladeFile) {
			BladeFile attach = (BladeFile) currentValue;
			fileId = attach.getAttachId();
			fileFullUrl = attach.getDomain() + SLASH_SYMBOL + attach.getName();
		} else if (currentValue instanceof Attach) {
			Attach attach = (Attach) currentValue;
			fileId = attach.getId();
			fileFullUrl = attach.getDomainUrl() + SLASH_SYMBOL + attach.getName();
		} else {
			if (Objects.nonNull(value)) {
				fileId = Long.valueOf(value.toString());
			}
		}

		if (Objects.isNull(fileId)) {
			gen.writeString(fileFullUrl);
		} else {
			String linkUrl = attachService.getLinkById(fileId);

			gen.writeString(linkUrl);
		}

	}
}
