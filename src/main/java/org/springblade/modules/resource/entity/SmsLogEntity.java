/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 短信日志表 实体类
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Data
@TableName("blade_sms_log")
@ApiModel(value = "SmsLog对象", description = "短信日志表")
@EqualsAndHashCode(callSuper = true)
public class SmsLogEntity extends TenantEntity {

  private static final long serialVersionUID = 1375691806962682021L;
  /**
   * 分类
   */
  @ApiModelProperty(value = "分类")
  private Integer category;
  /**
   * 资源编号
   */
  @ApiModelProperty(value = "资源编号")
  private String smsCode;
  /**
   * 模板ID
   */
  @ApiModelProperty(value = "模板ID")
  private String templateId;
  /**
   *
   */
  @ApiModelProperty(value = "")
  private String type;
  /**
   *
   */
  @ApiModelProperty(value = "")
  private String content;
  /**
   *
   */
  @ApiModelProperty(value = "")
  private String phone;
  /**
   *
   */
  @DateTimeFormat(
      pattern = "yyyy-MM-dd HH:mm:ss"
  )
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm:ss"
  )
  @ApiModelProperty(value = "")
  private Date date;
  /**
   *
   */
  @ApiModelProperty(value = "")
  private String ip;

  private Boolean success;

}
