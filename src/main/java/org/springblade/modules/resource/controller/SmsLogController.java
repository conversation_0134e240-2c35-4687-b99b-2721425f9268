/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import java.time.LocalDate;
import java.util.Date;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.resource.entity.SmsLogEntity;
import org.springblade.modules.resource.vo.SmsLogVO;
import org.springblade.modules.resource.wrapper.SmsLogWrapper;
import org.springblade.modules.resource.service.ISmsLogService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 短信日志表 控制器
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_RESOURCE_NAME + "/sms/log")
@Api(value = "短信日志表", tags = "短信日志表接口")
public class SmsLogController extends BladeController {

  private final ISmsLogService smsLogService;

  /**
   * 短信日志表 详情
   */
  @GetMapping("/detail")
  @ApiOperationSupport(order = 1)
  @ApiOperation(value = "详情", notes = "传入smsLog")
  public R<SmsLogVO> detail(SmsLogEntity smsLog) {
    SmsLogEntity detail = smsLogService.getOne(Condition.getQueryWrapper(smsLog));
    return R.data(SmsLogWrapper.build().entityVO(detail));
  }

  /**
   * 短信日志表 分页
   */
  @GetMapping("/list")
  @ApiOperationSupport(order = 2)
  @ApiOperation(value = "分页", notes = "传入smsLog")
  public R<IPage<SmsLogVO>> list(SmsLogEntity smsLog,
      @ApiParam(value = "日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
      @ApiParam(value = "日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
      Query query) {
    QueryWrapper<SmsLogEntity> wrapper = Condition.getQueryWrapper(smsLog);
    wrapper.lambda().ge(startDate != null, SmsLogEntity::getDate, startDate)
        .le(endDate != null, SmsLogEntity::getDate, endDate);
    IPage<SmsLogEntity> pages = smsLogService.page(Condition.getPage(query), wrapper);
    return R.data(SmsLogWrapper.build().pageVO(pages));
  }

  /**
   * 短信日志表 自定义分页
   */
  @GetMapping("/page")
  @ApiOperationSupport(order = 3)
  @ApiOperation(value = "分页", notes = "传入smsLog")
  public R<IPage<SmsLogVO>> page(SmsLogVO smsLog, Query query) {
    IPage<SmsLogVO> pages = smsLogService.selectSmsLogPage(Condition.getPage(query), smsLog);
    return R.data(pages);
  }

  /**
   * 短信日志表 新增
   */
  @PostMapping("/save")
  @ApiOperationSupport(order = 4)
  @ApiOperation(value = "新增", notes = "传入smsLog")
  public R save(@Valid @RequestBody SmsLogEntity smsLog) {
    return R.status(smsLogService.save(smsLog));
  }

  /**
   * 短信日志表 修改
   */
  @PostMapping("/update")
  @ApiOperationSupport(order = 5)
  @ApiOperation(value = "修改", notes = "传入smsLog")
  public R update(@Valid @RequestBody SmsLogEntity smsLog) {
    return R.status(smsLogService.updateById(smsLog));
  }

  /**
   * 短信日志表 新增或修改
   */
  @PostMapping("/submit")
  @ApiOperationSupport(order = 6)
  @ApiOperation(value = "新增或修改", notes = "传入smsLog")
  public R submit(@Valid @RequestBody SmsLogEntity smsLog) {
    return R.status(smsLogService.saveOrUpdate(smsLog));
  }

  /**
   * 短信日志表 删除
   */
  @PostMapping("/remove")
  @ApiOperationSupport(order = 7)
  @ApiOperation(value = "逻辑删除", notes = "传入ids")
  public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
    return R.status(smsLogService.deleteLogic(Func.toLongList(ids)));
  }


}
