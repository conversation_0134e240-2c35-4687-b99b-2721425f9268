/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.resource.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.ByteArrayInputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.codec.Charsets;
import org.apache.commons.io.IOUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.oss.OssTemplate;
import org.springblade.core.oss.model.OssFile;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Base64Util;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.builder.oss.OssBuilder;
import org.springblade.modules.resource.dto.AttachDTO;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.entity.Oss;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.resource.vo.AttachVO;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;

/**
 * 附件表 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_RESOURCE_NAME + "/attach")
@Api(value = "附件", tags = "附件")
public class AttachController extends BladeController {

	private final IAttachService attachService;
	/**
	 * 对象存储构建类
	 */
	private final OssBuilder ossBuilder;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入attach")
	public R<Attach> detail(Attach attach) {
		Attach detail = attachService.getOne(Condition.getQueryWrapper(attach));
		return R.data(detail);
	}

	/**
	 * 分页 附件表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入attach")
	public R<List<AttachVO>> list(AttachDTO attach) {
		String tenantId = AuthUtil.getTenantId();
		List<AttachVO> res = attachService.selectAttachList(attach);
		res.forEach(item -> {
			Oss oss = ossBuilder.getOss(tenantId, item.getOssCode());
			item.setAccessPolicy(oss.getAccessPolicy());
		});
		return R.data(res);
	}

	/**
	 * 自定义分页 附件表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入attach")
	public R<IPage<AttachVO>> page(AttachDTO attach, Query query) {
		String tenantId = AuthUtil.getTenantId();
		IPage<AttachVO> pages = attachService.selectAttachPage(Condition.getPage(query), attach);
		pages.getRecords().forEach(item -> {
			Oss oss = ossBuilder.getOss(tenantId, item.getOssCode());
			item.setAccessPolicy(oss.getAccessPolicy());
		});
		return R.data(pages);
	}

	/**
	 * 新增 附件表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入attach")
	public R save(@Valid @RequestBody Attach attach) {
		return R.status(attachService.save(attach));
	}

	/**
	 * 修改 附件表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入attach")
	public R update(@Valid @RequestBody Attach attach) {
		return R.status(attachService.updateById(attach));
	}

	/**
	 * 新增或修改 附件表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入attach")
	public R submit(@Valid @RequestBody Attach attach) {
		return R.status(attachService.saveOrUpdate(attach));
	}


	/**
	 * 删除 附件表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(attachService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 获取文件外链
	 *
	 * @param id 存储桶对象名称
	 * @return String
	 */
	@SneakyThrows
	@GetMapping("/file-link")
	public R<String> fileLink(@RequestParam Long id) {
		return R.data(attachService.getLinkById(id));
	}


	@GetMapping("/fileLinkByBusinessKeys")
	public R<List<Attach>> fileLinkByBusinessKey(@RequestParam String businessKeys,
		@RequestParam String businessName) {
		return R.data(
			attachService.getLinksByBusinessKeysAndBusinessName(businessKeys, businessName));
	}

	/**
	 * 导出模板
	 */
	@GetMapping("download")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "导出")
	public void export(@RequestParam Long id, HttpServletResponse response) throws IOException {
		Attach attach = attachService.getById(id);
		Oss oss = ossBuilder.getOss(AuthUtil.getTenantId(), attach.getOssCode());
		OssTemplate template = ossBuilder.template(attach.getOssCode());
		InputStream is = template.getFile(oss.getBucketName(), attach.getName());
		OssFile ossFile = template.statFile(oss.getBucketName(), attach.getName());
		response.setContentType(ossFile.getContentType());
		response.setCharacterEncoding(StandardCharsets.UTF_8.name());
		String fileName = URLEncoder.encode(attach.getOriginalName(),
			StandardCharsets.UTF_8.name());
		response.setHeader("Content-Disposition",
			String.format("attachment; filename=%s", fileName));
		IOUtils.copy(is, response.getOutputStream());
	}

}
