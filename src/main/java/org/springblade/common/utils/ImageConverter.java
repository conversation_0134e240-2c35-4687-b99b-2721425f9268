package org.springblade.common.utils;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.Base64;
import java.util.regex.Pattern;

public class ImageConverter implements Converter<String> {

	@Override
	public Class<?> supportJavaTypeKey() {
		return String.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.IMAGE;
	}

	@Override
	public String convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
									GlobalConfiguration globalConfiguration) {
		return null; // 导入时不需要转换
	}

	@Override
	public CellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty,
										  GlobalConfiguration globalConfiguration) {
		if (value == null || value.trim().isEmpty()) {
			return new CellData<>("");
		}

		// 检测是否为二进制流的特殊表示形式（例如，以"data:"开头的Base64编码数据）
		if (value.startsWith("data:")) {
			try {
				// 提取Base64编码的部分并解码
				String base64Data = value.substring(value.indexOf(",") + 1);
				byte[] binaryData = Base64.getDecoder().decode(base64Data);
				return new CellData<>(binaryData);
			} catch (Exception e) {
				return new CellData<>("二进制流解析错误: " + e.getMessage());
			}
		}

		// 传统文件路径处理逻辑
		if (!isValidPath(value)){
			return new CellData<>(value);
		} else {
			try {
				File file = new File(value);
				if (file.exists() && file.isFile()) {
					try (FileInputStream fis = new FileInputStream(file);
						 ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
						byte[] buffer = new byte[8192];
						int bytesRead;
						while ((bytesRead = fis.read(buffer)) != -1) {
							bos.write(buffer, 0, bytesRead);
						}
						return new CellData<>(bos.toByteArray());
					}
				} else {
					return new CellData<>("二维码图片不存在！");
				}
			} catch (Exception e) {
				return new CellData<>("图片处理错误: " + e.getMessage());
			}
		}
	}

	//windows校验
	private static final Pattern WINDOWS_PATH_PATTERN = Pattern.compile("^[a-zA-Z]:[\\\\/](?:[^\\\\/:*?\"<>|\\r\\n]+[\\\\/])*[^\\\\/:*?\"<>|\\r\\n]*$");
	//Linux校验
	private static final Pattern UNIX_PATH_PATTERN = Pattern.compile("^/(?:[^/]+/)*[^/]*$");
	// URL正则表达式
	private static final Pattern URL_PATTERN = Pattern.compile("^(https?|ftp)://[^\\s/$.?#].[^\\s]*$", Pattern.CASE_INSENSITIVE);

	/**
	 * 校验字符串是否为有效的路径格式
	 * @param path 待校验的字符串
	 * @return 如果是有效路径返回true，否则返回false
	 */
	public static boolean isValidPath(String path) {
		if (path == null || path.trim().isEmpty()) {
			return false;
		}
		path = path.trim();
		// 检查是否为URL
		if (URL_PATTERN.matcher(path).matches()) {
			return true;
		}
		// 检查是否为Windows路径
		if (WINDOWS_PATH_PATTERN.matcher(path).matches()) {
			return true;
		}
		// 检查是否为Unix/Linux路径
		return UNIX_PATH_PATTERN.matcher(path).matches();
	}
}
