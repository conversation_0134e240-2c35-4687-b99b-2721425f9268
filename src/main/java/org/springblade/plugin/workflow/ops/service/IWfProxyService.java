package org.springblade.plugin.workflow.ops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.system.entity.User;
import org.springblade.plugin.workflow.ops.entity.WfProxy;

import java.util.LinkedHashSet;

/**
 * 流程代理 服务类
 *
 * <AUTHOR>
 */
public interface IWfProxyService extends IService<WfProxy> {

	Object create(WfProxy proxy);

	/**
	 * 获取流程的代理用户
	 *
	 * @param userId       原用户
	 * @param processInsId 流程实例id
	 * @return 代理用户
	 */
	String getProxyUser(String userId, String processInsId);

	/**
	 * 获取流程的代理用户
	 *
	 * @param users        原用户列表
	 * @param processInsId 流程实例id
	 * @return 代理用户
	 */
	LinkedHashSet<User> getProxyUsers(LinkedHashSet<User> users, String processInsId);

}
