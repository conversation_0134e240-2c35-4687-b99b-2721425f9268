package org.springblade.plugin.workflow.ops.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 流程代理实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("blade_wf_proxy")
@ApiModel(value = "WfProxy对象", description = "流程代理")
public class WfProxy implements Serializable {

	private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	@ApiModelProperty(value = "委托人")
	private Long userId;

	@ApiModelProperty(value = "代理人")
	private Long proxyUserId;

	@ApiModelProperty(value = "流程定义key")
	private String processDefKey;

	@ApiModelProperty(value = "1永久 2定时")
	private String type;

	@ApiModelProperty(value = "开始时间")
	private LocalDateTime startTime;

	@ApiModelProperty(value = "结束时间")
	private LocalDateTime endTime;

	@ApiModelProperty("业务状态")
	private Integer status;
}
