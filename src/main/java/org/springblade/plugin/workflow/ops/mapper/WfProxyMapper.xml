<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.plugin.workflow.ops.mapper.WfProxyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wfProxyResultMap" type="org.springblade.plugin.workflow.ops.entity.WfProxy">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="user_id" property="userId"/>
        <result column="proxy_user_id" property="proxyUserId"/>
        <result column="process_def_key" property="processDefKey"/>
        <result column="type" property="type"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
    </resultMap>

</mapper>
