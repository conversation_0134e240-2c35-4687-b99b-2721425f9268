package org.springblade.plugin.workflow.ops.vo;

import org.springblade.plugin.workflow.ops.entity.WfProxy;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 流程代理视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WfProxyVO对象", description = "流程代理")
public class WfProxyVO extends WfProxy {
	private static final long serialVersionUID = 1L;

}
