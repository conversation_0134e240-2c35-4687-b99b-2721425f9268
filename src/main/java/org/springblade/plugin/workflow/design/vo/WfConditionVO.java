
package org.springblade.plugin.workflow.design.vo;

import org.springblade.plugin.workflow.design.entity.WfCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 流程表达式视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WfConditionVO对象", description = "流程表达式")
public class WfConditionVO extends WfCondition {
	private static final long serialVersionUID = 1L;

}
