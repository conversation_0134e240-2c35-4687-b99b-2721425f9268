<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.plugin.workflow.design.mapper.WfModelHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="formResultMap" type="org.springblade.plugin.workflow.design.entity.WfModelHistory">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="model_key" property="modelKey"/>
        <result column="description" property="description"/>
        <result column="model_comment" property="modelComment"/>
        <result column="created" property="created"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_updated" property="lastUpdated"/>
        <result column="last_updated_by" property="lastUpdatedBy"/>
        <result column="removal_date" property="removalDate"/>
        <result column="version" property="version"/>
        <result column="model_editor_json" property="modelEditorJson"/>
        <result column="model_id" property="modelId"/>
        <result column="model_type" property="modelType"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <delete id="removeByModelId">
        DELETE
        FROM ACT_DE_MODEL_HISTORY
        WHERE model_id = #{id}
    </delete>

</mapper>
