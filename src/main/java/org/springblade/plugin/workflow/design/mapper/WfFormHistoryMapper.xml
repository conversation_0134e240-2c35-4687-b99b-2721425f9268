<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.plugin.workflow.design.mapper.WfFormHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wfFormHistoryResultMap" type="org.springblade.plugin.workflow.design.entity.WfFormHistory">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="form_id" property="formId"/>
        <result column="key" property="key"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="version" property="version"/>
    </resultMap>
    <delete id="removeByFormId">
        DELETE
        FROM blade_wf_form_history
        WHERE form_id = #{id}
    </delete>


    <select id="selectWfFormHistoryPage" resultMap="wfFormHistoryResultMap">
        select *
        from blade_wf_form_history
        where is_deleted = 0
    </select>

</mapper>
