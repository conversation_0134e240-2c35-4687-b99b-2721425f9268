<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.plugin.workflow.design.mapper.WfConditionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wfConditionResultMap" type="org.springblade.plugin.workflow.design.entity.WfCondition">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="expression" property="expression"/>
        <result column="type" property="type"/>
    </resultMap>


    <select id="selectWfConditionPage" resultMap="wfConditionResultMap">
        select *
        from blade_wf_condition
        where is_deleted = 0
    </select>

</mapper>
