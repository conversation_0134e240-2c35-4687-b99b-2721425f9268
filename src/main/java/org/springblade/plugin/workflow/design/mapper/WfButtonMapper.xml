<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.plugin.workflow.design.mapper.WfButtonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wfButtonResultMap" type="org.springblade.plugin.workflow.design.entity.WfButton">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="key" property="key"/>
        <result column="name" property="name"/>
        <result column="sort" property="sort"/>
    </resultMap>


    <select id="selectWfButtonPage" resultMap="wfButtonResultMap">
        select *
        from blade_wf_button
        where is_deleted = 0
    </select>

</mapper>
