package org.springblade.plugin.workflow.demo.service.impl;

import static org.springblade.plugin.workflow.process.entity.WfNotice.Type.DELETE_PROCESS;

import org.apache.commons.lang3.StringUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.plugin.workflow.demo.entity.WfProcessLeave;
import org.springblade.plugin.workflow.demo.mapper.WfProcessLeaveMapper;
import org.springblade.plugin.workflow.demo.service.IWfProcessLeaveService;
import org.springblade.plugin.workflow.process.ann.WfProcess;
import org.springblade.plugin.workflow.process.entity.WfNotice.Type;
import org.springframework.stereotype.Service;

/**
 * 请假流程业务示例 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class WfProcessLeaveServiceImpl extends
    BaseServiceImpl<WfProcessLeaveMapper, WfProcessLeave> implements IWfProcessLeaveService {

  @Override
  public Boolean submit(WfProcessLeave wfProcessLeave) {
    return this.saveOrUpdate(wfProcessLeave);
  }
}
