package org.springblade.plugin.workflow.demo.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.plugin.workflow.demo.entity.WfProcessLeave;
import org.springblade.plugin.workflow.demo.vo.WfProcessLeaveVO;
import org.springblade.plugin.workflow.demo.wrapper.WfProcessLeaveWrapper;
import org.springblade.plugin.workflow.demo.service.IWfProcessLeaveService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 请假流程业务示例 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-workflow/process/leave")
@Api(value = "请假流程业务示例", tags = "请假流程业务示例接口")
public class WfProcessLeaveController extends BladeController {

	private final IWfProcessLeaveService wfProcessLeaveService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入wfProcessLeave")
	public R<WfProcessLeaveVO> detail(WfProcessLeave wfProcessLeave) {
		WfProcessLeave detail = wfProcessLeaveService.getOne(Condition.getQueryWrapper(wfProcessLeave));
		return R.data(WfProcessLeaveWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 请假流程业务示例
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入wfProcessLeave")
	public R<IPage<WfProcessLeaveVO>> list(WfProcessLeave wfProcessLeave, Query query) {
		IPage<WfProcessLeave> pages = wfProcessLeaveService.page(Condition.getPage(query), Condition.getQueryWrapper(wfProcessLeave)
			.orderByDesc("id"));
		return R.data(WfProcessLeaveWrapper.build().pageVO(pages));
	}

	/**
	 * 新增 请假流程业务示例
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入wfProcessLeave")
	public R save(@Valid @RequestBody WfProcessLeave wfProcessLeave) {
		return R.status(wfProcessLeaveService.save(wfProcessLeave));
	}

	/**
	 * 修改 请假流程业务示例
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入wfProcessLeave")
	public R update(@Valid @RequestBody WfProcessLeave wfProcessLeave) {
		return R.status(wfProcessLeaveService.updateById(wfProcessLeave));
	}

	/**
	 * 新增或修改 请假流程业务示例
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入wfProcessLeave")
	public R submit(@Valid @RequestBody WfProcessLeave wfProcessLeave) {
		return R.status(wfProcessLeaveService.submit(wfProcessLeave));
	}


	/**
	 * 删除 请假流程业务示例
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(wfProcessLeaveService.deleteLogic(Func.toLongList(ids)));
	}

}
