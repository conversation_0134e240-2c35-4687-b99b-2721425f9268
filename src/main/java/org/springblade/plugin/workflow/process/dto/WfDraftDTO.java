package org.springblade.plugin.workflow.process.dto;

import org.springblade.plugin.workflow.process.entity.WfDraft;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程草稿箱数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WfDraftDTO extends WfDraft {
	private static final long serialVersionUID = 1L;
	/**
	 * 草稿箱弹窗展示的草稿内容
	 */
	private String formData;
	/**
	 * 草稿版本和当前；流程版本是否相同
	 */
	private boolean sameVersion;


}
