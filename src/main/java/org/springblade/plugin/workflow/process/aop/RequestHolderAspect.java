package org.springblade.plugin.workflow.process.aop;

import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @date 2023/8/25
 * @desc request是被threadLocal存储的，但是threadLocal是不能被子线程访问的，如果想让其可以子线程可以访问
 *  * 需要RequestContextHolder的inheritable设置为true，使用InherbritableThreadLocal来存储request
 */
@Aspect
@Component
public class RequestHolderAspect {

	@Before("@annotation(org.springblade.plugin.workflow.process.ann.RequestInheritableThread)")
	public void doBefore() {
		ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		RequestContextHolder.setRequestAttributes(sra, true);
	}

}
