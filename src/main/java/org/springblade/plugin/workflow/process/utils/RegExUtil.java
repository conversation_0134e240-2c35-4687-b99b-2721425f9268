package org.springblade.plugin.workflow.process.utils;


import org.springblade.plugin.workflow.process.dto.FunctionInfoDTO;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/8/23
 * @desc 正则表达式验证
 */
public class RegExUtil {

	private static final String SPLIT_PATTERN = "f:(\\w+)(\\(([^)]*)\\))?";



	public static FunctionInfoDTO processExpression(String input) {
		Pattern regexPattern = Pattern.compile(SPLIT_PATTERN);
		Matcher matcher = regexPattern.matcher(input);
		if (matcher.matches()) {
			String functionName = matcher.group(1);
			String argument = matcher.group(3);
			return new FunctionInfoDTO()
				.setFunctionName(functionName)
				.setParamValue(argument);
		}

		return null;
	}

}
