package org.springblade.plugin.workflow.process.service.impl;

import io.jsonwebtoken.lang.Assert;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.variable.api.types.VariableType;
import org.flowable.variable.api.types.VariableTypes;
import org.flowable.variable.service.impl.persistence.entity.HistoricVariableInstanceEntity;
import org.flowable.variable.service.impl.persistence.entity.HistoricVariableInstanceEntityManager;

/**
 * 修改历史数据，删除所有历史变量，并创建新的历史变量
 */
@RequiredArgsConstructor
public class ReplaceHistoricVariablesCmd implements Command<Void> {

	private final String processInstanceId;
	private final Map<String, Object> variables;

	@Override
	public Void execute(CommandContext commandContext) {
		Assert.isTrue(StringUtils.isNotBlank(processInstanceId), "流程实例ID不能为空！");
		Assert.notNull(variables, "变量不能为空！");
		// 通过流程引擎配置来获取 HistoricVariableInstanceEntityManager
		ProcessEngineConfigurationImpl processEngineConfiguration = CommandContextUtil.getProcessEngineConfiguration(
			commandContext);
		HistoricVariableInstanceEntityManager historicVariableInstanceEntityManager = processEngineConfiguration.getVariableServiceConfiguration()
			.getHistoricVariableInstanceEntityManager();
		ProcessEngineConfigurationImpl procEngineConf = CommandContextUtil.getProcessEngineConfiguration(
			commandContext);
		HistoryService historyService = procEngineConf.getHistoryService();

		// --- 步骤 1: 删除该流程实例所有旧的历史变量 ---
		List<HistoricVariableInstanceEntity> oldVariables = historicVariableInstanceEntityManager
			.findHistoricalVariableInstancesByProcessInstanceId(processInstanceId);

		for (HistoricVariableInstanceEntity oldVariable : oldVariables) {
			historicVariableInstanceEntityManager.delete(oldVariable);
		}
		// --- 步骤 2: 根据您提供的Map，创建新的历史变量 ---
		// 从引擎配置中获取变量类型注册器
		VariableTypes variableTypes = processEngineConfiguration.getVariableTypes();
		HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
			.processInstanceId(this.processInstanceId)
			.includeProcessVariables()
			.singleResult();
		for (Map.Entry<String, Object> entry : variables.entrySet()) {
			// 创建一个新的历史变量实体
			HistoricVariableInstanceEntity newVariable = historicVariableInstanceEntityManager.create();
			newVariable.setProcessInstanceId(processInstanceId);
			newVariable.setName(entry.getKey());
			newVariable.setCreateTime(processEngineConfiguration.getClock().getCurrentTime());

			// [*** 关键修正点 ***]
			// 1. 根据值的 Class 找到对应的 VariableType 处理器
			Object value = entry.getValue();
			VariableType variableType = variableTypes.findVariableType(value);

			// 2. 将类型名称设置到实体中 (例如 'string', 'integer', 'serializable')
			newVariable.setVariableType(variableType);

			// 3. 使用该类型处理器将值设置到实体中
			//    它会自动将值存入 TEXT_, LONG_, DOUBLE_ 等合适的字段
			variableType.setValue(value, newVariable);
			// [*** 修正结束 ***]
			newVariable.setExecutionId(historicProcessInstance.getId());
			// 将完全配置好的新历史变量插入数据库
			historicVariableInstanceEntityManager.insert(newVariable);
		}

		return null;
	}


	/**
	 * 如何调用这个命令
	 *
	 * @param managementService Flowable的ManagementService
	 * @param processInstanceId 要操作的流程实例ID
	 * @param correctVariables  包含正确变量的Map
	 */
	public static void executeReplacement(ManagementService managementService,
		String processInstanceId, Map<String, Object> correctVariables) {
		managementService.executeCommand(
			new ReplaceHistoricVariablesCmd(processInstanceId, correctVariables));
	}
}
