package org.springblade.plugin.workflow.process.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

import org.springblade.core.tool.api.R;
import org.springblade.plugin.workflow.core.utils.ObjectUtil;
import org.springblade.plugin.workflow.core.utils.WfTaskUtil;
import org.springblade.plugin.workflow.process.dto.WfDraftDTO;
import org.springblade.plugin.workflow.process.entity.WfDraft;
import org.springblade.plugin.workflow.process.utils.JsonParseUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springblade.plugin.workflow.process.service.IWfDraftService;
import org.springblade.core.boot.ctrl.BladeController;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程草稿箱 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-workflow/process/draft")
@Api(value = "流程草稿箱", tags = "流程草稿箱接口")
public class WfDraftController extends BladeController {

	private final IWfDraftService wfDraftService;

	@GetMapping("detail")
	@ApiOperation("本人当前表单草稿")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processDefId", value = "流程定义id"),
		@ApiImplicitParam(name = "platform", value = "平台 pc/app"),
	})
	public R detail(WfDraft wfDraft) {
		if (ObjectUtil.isAnyEmpty(wfDraft.getProcessDefId(), wfDraft.getPlatform(),wfDraft.getProcessKey())) {
			return R.fail("参数错误");
		}
		String processDefId = wfDraft.getProcessDefId();
		wfDraft.setUserId(Long.valueOf(WfTaskUtil.getTaskUser()));
		wfDraft.setProcessDefId(null);
		QueryWrapper<WfDraft> wrapper = new QueryWrapper<>(wfDraft);
		wrapper.orderByDesc("id");
		List<WfDraft> list = wfDraftService.list(wrapper);
		List<WfDraftDTO> wfDraftDtoList = list.stream().map(item -> {
			WfDraftDTO wfDraftDTO = new WfDraftDTO();
			wfDraftDTO.setId(item.getId());
			wfDraftDTO.setDraftTitle(item.getDraftTitle());
			wfDraftDTO.setVariables(item.getVariables());
			wfDraftDTO.setCreateTime(item.getCreateTime());
			String variables = item.getVariables();
			String fromData = JsonParseUtil.analysisJson(variables,"|");
			fromData = fromData.length() > 1000? fromData.substring(0, 1000) : fromData;
			wfDraftDTO.setFormData(fromData);
			wfDraftDTO.setSameVersion(processDefId.equals(item.getProcessDefId()));
			return wfDraftDTO;
		}).collect(Collectors.toList());
		return R.data(wfDraftDtoList);
	}

	@PostMapping("submit")
	@ApiOperation("保存")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processDefId", value = "流程定义id"),
		@ApiImplicitParam(name = "platform", value = "平台 pc/app"),
	})
	public R submit(@RequestBody WfDraft wfDraft) {
		if (ObjectUtil.isAnyEmpty(wfDraft.getProcessDefId(), wfDraft.getPlatform())) {
			return R.fail("参数错误");
		}
		wfDraftService.submit(wfDraft);
		return R.success("保存成功");
	}

	@PostMapping("delete/{draftId}")
	@ApiOperation("删除")
	@Transactional(rollbackFor = Exception.class)
	public R deleteWfDraft(@PathVariable Long draftId) {
		wfDraftService.removeById(draftId);
		return R.success("删除成功");
	}

}
