package org.springblade.plugin.workflow.process.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springblade.core.tool.utils.Base64Util;
import org.springblade.core.tool.utils.Charsets;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class JumpParamsDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	private String processId;

	private String taskId;

	private String processInsId;

	@Override
	public String toString() {
		String jsonString = JSON.toJSONString(this);

		log.info(jsonString);
		// 执行编码
		byte[] b = Base64.encodeBase64URLSafe(jsonString.getBytes(Charsets.UTF_8));
		return new String(b, Charsets.UTF_8);
	}
}
