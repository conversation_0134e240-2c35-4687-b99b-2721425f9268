package org.springblade.plugin.workflow.process.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * 流程消息实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WfNotice implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "发送者")
	private String fromUserId;

	@ApiModelProperty(value = "接受者")
	private String toUserId;

	@ApiModelProperty(value = "评论")
	private String comment;

	@ApiModelProperty(value = "任务id")
	private String taskId;

	@ApiModelProperty(value = "流程实例id")
	private String processId;

	@ApiModelProperty(value = "任务变量")
	private Map<String, Object> taskVariables;

	@ApiModelProperty(value = "消息类型")
	private Type type;

	@Getter
	@AllArgsConstructor
	public enum Type {
		/**
		 * 发起流程
		 */
		START("start", "发起流程", 1),
		/**
		 * 审核通过
		 */
		PASS("pass", "审核通过", 2),
		/**
		 * 审核驳回
		 */
		REJECT("reject", "审核驳回", 3),
		/**
		 * 转办任务
		 */
		TRANSFER("transfer", "转办任务", 4),
		/**
		 * 委托任务
		 */
		DELEGATE("delegate", "委托任务", 5),
		/**
		 * 流程终结
		 */
		TERMINATE("terminate", "流程终结", 6),
		/**
		 * 流程结束
		 */
		FINISH("finish", "流程结束", 7),
		/**
		 * 候选任务
		 */
		CANDIDATE("candidate", "候选任务", 8),
		/**
		 * 抄送任务
		 */
		COPY("copy", "抄送任务", 9),
		/**
		 * 变更审核人
		 */
		ASSIGNEE("assignee", "变更审核人", 10),
		/**
		 * 流程挂起
		 */
		SUSPEND("suspend", "流程挂起", 11),
		/**
		 * 流程激活
		 */
		ACTIVE("active", "流程激活", 12),
		/**
		 * 催办任务
		 */
		URGE("urge", "催办任务", 13),
		/**
		 * 调度任务
		 */
		DISPATCH("dispatch", "调度任务", 14),
		/**
		 * 加签任务
		 */
		ADD_MULTI_INSTANCE("addMultiInstance", "加签任务", 15),
		/**
		 * 减签任务
		 */
		DELETE_MULTI_INSTANCE("deleteMultiInstance", "减签任务", 16),
		/**
		 * 撤销流程
		 */
		CANCEL("cancel", "撤销流程", 17),
		/**
		 * 删除流程
		 */
		DELETE_PROCESS("deleteProcess", "删除流程", 18);

		private final String type;
		private final String name;
		private final Integer code;
	}

}
