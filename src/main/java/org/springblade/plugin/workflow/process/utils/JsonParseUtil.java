package org.springblade.plugin.workflow.process.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 解析Json字符串，并且用指定分隔符拼接
 */
public class JsonParseUtil {

	/**
	 * 解析JSON数据
	 * @param variables
	 * @return
	 */
	public static String analysisJson(String variables,String symbol) {
		// 解析JSON字符串
		Object json = JSON.parse(variables);
		// 提取所有值
		List<Object> values = extractValues(json);
		StringBuilder result = new StringBuilder();
		for (Object value : values) {
			if (value != null && !"".equals(value)){
				result.append(value).append(symbol);
			}
		}
		if (result.length() > 0) {
			result.setLength(result.length() - 1);
		}
		return result.toString();
	}

	/**
	 * 递归提取JSON中的所有值
	 */
	public static List<Object> extractValues(Object json) {
		List<Object> values = new ArrayList<>();
		if (json instanceof JSONObject) {
			JSONObject jsonObject = (JSONObject) json;
			for (String key : jsonObject.keySet()) {
				Object value = jsonObject.get(key);
				values.addAll(extractValues(value));
			}
		} else if (json instanceof JSONArray) {
			JSONArray jsonArray = (JSONArray) json;
			for (Object item : jsonArray) {
				values.addAll(extractValues(item));
			}
		} else {
			// 基本类型值（字符串、数字、布尔等）
			values.add(json);
		}
		return values;
	}
}
