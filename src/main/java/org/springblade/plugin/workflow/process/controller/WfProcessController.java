package org.springblade.plugin.workflow.process.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.jsonwebtoken.lang.Assert;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springblade.common.annotation.Debounce;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
import org.springblade.plugin.workflow.core.utils.ObjectUtil;
import org.springblade.plugin.workflow.core.utils.WfTaskUtil;
import org.springblade.plugin.workflow.design.entity.WfButton;
import org.springblade.plugin.workflow.design.entity.WfProcessDef;
import org.springblade.plugin.workflow.design.service.IWfButtonService;
import org.springblade.plugin.workflow.design.service.IWfDesignService;
import org.springblade.plugin.workflow.design.service.IWfFormService;
import org.springblade.plugin.workflow.process.ann.RequestInheritableThread;
import org.springblade.plugin.workflow.process.dto.WfScanDTO;
import org.springblade.plugin.workflow.process.entity.WfCopy;
import org.springblade.plugin.workflow.process.mapper.WfProcessScanMapper;
import org.springblade.plugin.workflow.process.model.WfNode;
import org.springblade.plugin.workflow.process.model.WfProcess;
import org.springblade.plugin.workflow.process.service.IWfCopyService;
import org.springblade.plugin.workflow.process.service.IWfProcessService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/blade-workflow/process")
@AllArgsConstructor
@Validated
public class WfProcessController {

	private final IWfDesignService designService;
	private final IWfProcessService processService;
	private final IWfFormService formService;
	private final IWfButtonService buttonService;
	private final IWfCopyService copyService;

	private final HistoryService historyService;

	private final WfProcessScanMapper wfProcessScanMapper;

	@GetMapping("processList")
	@ApiOperation("可发起流程列表")
	@ApiImplicitParams({@ApiImplicitParam(name = "name", value = "流程名称"),
		@ApiImplicitParam(name = "key", value = "流程标识"),
		@ApiImplicitParam(name = "category", value = "流程分类"),
		@ApiImplicitParam(name = "current", value = "当前第几页", required = true),
		@ApiImplicitParam(name = "size", value = "每页条数", required = true),})
	public R<IPage<WfProcessDef>> processList(@ApiIgnore WfProcessDef processDef, Query query) {
		if (ObjectUtil.isAnyEmpty(query.getCurrent(), query.getSize())) {
			return R.fail("参数错误");
		}
		processDef.setStatus(1);
		processDef.setScope(true);
		return R.data(designService.deploymentPage(processDef, query));
	}

	@GetMapping("getFormByProcessId")
	@ApiOperation("获取流程表单")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processId", value = "流程定义id", required = true),})
	public R<Map<String, Object>> getFormByProcessId(String processId) {
		if (ObjectUtil.isAnyEmpty(processId)) {
			return R.fail("参数错误");
		}
		return R.data(formService.getFormByProcessDefId(processId));
	}

	@GetMapping("getFormByProcessDefKey")
	@ApiOperation("获取流程表单")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processDefKey", value = "流程定义key", required = true),})
	public R<Map<String, Object>> getFormByProcessDefKey(String processDefKey) {
		if (ObjectUtil.isAnyEmpty(processDefKey)) {
			return R.fail("参数错误");
		}
		return R.data(formService.getFormByProcessDefKey(processDefKey));
	}


	@Debounce(time = 5,
		perUser = true,
		userKeyExpression = "T(org.springblade.core.secure.utils.AuthUtil).getUserId()",
		timeUnit = TimeUnit.SECONDS, message = "该请求已提交过，请勿重复提交请求")
	@RequestInheritableThread //处理request无法被子线程共享的问题
	@PostMapping("startProcess")
	@ApiOperation("发起流程")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processId", value = "流程定义id", required = true),})
	public R<String> startProcess(@ApiIgnore @RequestBody Map<String,Object> body) {
		String processDefId = (String) body.get("processId");
		body.remove("processId");
		try {
			return R.data(processService.startProcessInstanceById(processDefId, body));
		} catch (Exception e) {
			return R.fail(e.getLocalizedMessage());
		}
	}

	@Debounce(time = 5,
		perUser = true,
		userKeyExpression = "T(org.springblade.core.secure.utils.AuthUtil).getUserId()",
		timeUnit = TimeUnit.SECONDS, message = "该请求已提交过，请勿重复提交请求")
	@RequestInheritableThread //处理request无法被子线程共享的问题
	@PostMapping("startProcessByKey")
	@ApiOperation("发起流程 - 根据key")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processDefKey", value = "流程定义key", required = true),})
	public R<String> startProcessByKey(@ApiIgnore @RequestBody Map<String,Object> body) {
		String processDefKey = (String) body.get("processDefKey");
		body.remove("processDefKey");
		try {
			return R.data(
				processService.startProcessInstanceByKey(processDefKey, processDefKey, body));
		} catch (Exception e) {
			return R.fail(e.getLocalizedMessage());
		}
	}

	@GetMapping("todoList")
	@ApiOperation("待办列表（包含待签）")
	@ApiImplicitParams({@ApiImplicitParam(name = "name", value = "流程名称"),
		@ApiImplicitParam(name = "key", value = "流程标识"),
		@ApiImplicitParam(name = "category", value = "流程分类"),
		@ApiImplicitParam(name = "current", value = "当前第几页", required = true),
		@ApiImplicitParam(name = "size", value = "每页条数", required = true),})
	public R<IPage<WfProcess>> todoList(@ApiIgnore WfProcess process, Query query) {
		if (ObjectUtil.isAnyEmpty(query.getCurrent(), query.getSize())) {
			return R.fail("参数错误");
		}
		if (StringUtils.isNotBlank(process.getSerialNo())) {
			String formSearch = process.getFormSearch();
			if (StringUtils.isBlank(formSearch)) {
				formSearch = "";
			}
			formSearch += String.join(",", "serialNo:like:" + process.getSerialNo());
			process.setFormSearch(formSearch);
		}
		process.setStatus(WfProcessConstant.STATUS_TODO);
		return R.data(processService.selectTaskPage(process, query));
	}

	@GetMapping("doneList")
	@ApiOperation("办结列表")
	@ApiImplicitParams({@ApiImplicitParam(name = "name", value = "流程名称"),
		@ApiImplicitParam(name = "key", value = "流程标识"),
		@ApiImplicitParam(name = "category", value = "流程分类"),
		@ApiImplicitParam(name = "current", value = "当前第几页", required = true),
		@ApiImplicitParam(name = "size", value = "每页条数", required = true),})
	public R<IPage<WfProcess>> doneList(@ApiIgnore WfProcess process, Query query) {
		if (ObjectUtil.isAnyEmpty(query.getCurrent(), query.getSize())) {
			return R.fail("参数错误");
		}
		if (StringUtils.isNotBlank(process.getSerialNo())) {
			String formSearch = process.getFormSearch();
			if (StringUtils.isBlank(formSearch)) {
				formSearch = "";
			}
			formSearch += String.join(",", "serialNo:like:" + process.getSerialNo());
			process.setFormSearch(formSearch);
		}
		process.setStatus(WfProcessConstant.STATUS_DONE);
		return R.data(processService.selectProcessPage(process, query));
	}

	@GetMapping("myDoneList")
	@ApiOperation("我的已办（流程不一定办结）")
	@ApiImplicitParams({@ApiImplicitParam(name = "name", value = "流程名称"),
		@ApiImplicitParam(name = "key", value = "流程标识"),
		@ApiImplicitParam(name = "category", value = "流程分类"),
		@ApiImplicitParam(name = "current", value = "当前第几页", required = true),
		@ApiImplicitParam(name = "size", value = "每页条数", required = true),})
	public R<IPage<WfProcess>> myDoneList(@ApiIgnore WfProcess process, Query query) {
		if (ObjectUtil.isAnyEmpty(query.getCurrent(), query.getSize())) {
			return R.fail("参数错误");
		}
		if (StringUtils.isNotBlank(process.getSerialNo())) {
			String formSearch = process.getFormSearch();
			if (StringUtils.isBlank(formSearch)) {
				formSearch = "";
			}
			formSearch += String.join(",", "serialNo:like:" + process.getSerialNo());
			process.setFormSearch(formSearch);
		}
		process.setStatus(WfProcessConstant.STATUS_DONE);
		return R.data(processService.selectTaskPage(process, query));
	}

	@GetMapping("sendList")
	@ApiOperation("我的请求列表")
	@ApiImplicitParams({@ApiImplicitParam(name = "name", value = "流程名称"),
		@ApiImplicitParam(name = "key", value = "流程标识"),
		@ApiImplicitParam(name = "category", value = "流程分类"),
		@ApiImplicitParam(name = "current", value = "当前第几页", required = true),
		@ApiImplicitParam(name = "size", value = "每页条数", required = true),})
	public R<IPage<WfProcess>> sendList(@ApiIgnore WfProcess process, Query query) {
		if (ObjectUtil.isAnyEmpty(query.getCurrent(), query.getSize())) {
			return R.fail("参数错误");
		}
		if (StringUtils.isNotBlank(process.getSerialNo())) {
			String formSearch = process.getFormSearch();
			if (StringUtils.isBlank(formSearch)) {
				formSearch = "";
			}
			formSearch += String.join(",", "serialNo:like:" + process.getSerialNo());
			process.setFormSearch(formSearch);
		}
		process.setStatus(WfProcessConstant.STATUS_SEND);
		return R.data(processService.selectProcessPage(process, query));
	}

	@GetMapping("claimList")
	@ApiOperation("待签列表")
	@ApiImplicitParams({@ApiImplicitParam(name = "name", value = "流程名称"),
		@ApiImplicitParam(name = "key", value = "流程标识"),
		@ApiImplicitParam(name = "category", value = "流程分类"),
		@ApiImplicitParam(name = "current", value = "当前第几页", required = true),
		@ApiImplicitParam(name = "size", value = "每页条数", required = true),})
	public R<IPage<WfProcess>> claimList(@ApiIgnore WfProcess process, Query query) {
		if (ObjectUtil.isAnyEmpty(query.getCurrent(), query.getSize())) {
			return R.fail("参数错误");
		}
		if (StringUtils.isNotBlank(process.getSerialNo())) {
			String formSearch = process.getFormSearch();
			if (StringUtils.isBlank(formSearch)) {
				formSearch = "";
			}
			formSearch += String.join(",", "serialNo:like:" + process.getSerialNo());
			process.setFormSearch(formSearch);
		}
		process.setStatus(WfProcessConstant.STATUS_CLAIM);
		return R.data(processService.selectTaskPage(process, query));
	}

	@GetMapping("copyList")
	@ApiOperation("抄送列表")
	@ApiImplicitParams({@ApiImplicitParam(name = "title", value = "标题"),
		@ApiImplicitParam(name = "initiator", value = "发起人"),
		@ApiImplicitParam(name = "current", value = "当前第几页", required = true),
		@ApiImplicitParam(name = "size", value = "每页条数", required = true),})
	public R<IPage<WfCopy>> page(@ApiIgnore @RequestParam Map<String, Object> copy, Query query) {
		if (ObjectUtil.isAnyEmpty(query.getCurrent(), query.getSize())) {
			return R.fail("参数错误");
		}
		return R.data(copyService.page(Condition.getPage(query),
			Condition.getQueryWrapper(copy, WfCopy.class)
				.eq("user_id", Long.valueOf(WfTaskUtil.getTaskUser())).orderByDesc("id")));
	}

	@GetMapping("detail")
	@ApiOperation("流程详情")
	@RequestInheritableThread
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id"),
		@ApiImplicitParam(name = "processInsId", value = "流程实例id", required = true),})
	public R<Map<String, Object>> detail(String taskId, String processInsId)
		throws ExecutionException, InterruptedException {
		if (ObjectUtil.isAnyEmpty(processInsId)) {
			return R.fail("参数错误");
		}
		if (StringUtil.isBlank(taskId)) { // 不传taskId的情况下，取最后一个创建的任务
			List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery()
				.processInstanceId(processInsId).orderByTaskCreateTime().desc()
				.orderByHistoricTaskInstanceEndTime().desc().list();
			Assert.notEmpty(historyTasks, "查询不到任务详情");
			List<HistoricTaskInstance> runTasks = historyTasks.stream()
				.filter(task -> task.getEndTime() == null).collect(Collectors.toList());
			if (runTasks.isEmpty()) {
				taskId = historyTasks.iterator().next().getId();
			} else {
				taskId = runTasks.iterator().next().getId();
			}
		}

		Future<WfProcess> processFuture = processService.detail(taskId, WfTaskUtil.getTaskUser(),
			WfTaskUtil.getCandidateGroup());
		Future<Map<String, Object>> formFuture = formService.getFormByTaskId(taskId);
		Future<List<WfButton>> buttonFuture = buttonService.getButtonByTaskId(taskId);
		List<WfProcess> flowData = processService.syncHistoryFlowList(processInsId, null, null);
		Map<String, Object> result = new HashMap<>();
		result.put("process", processFuture.get());
		result.put("form", formFuture.get());
		result.put("button", buttonFuture.get());
		result.put("flow", flowData);
		return R.data(result);
	}

	@GetMapping("flow")
	@ApiOperation("流转信息")
	@RequestInheritableThread
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processInsId", value = "流程实例id", required = true),})
	public R<List<WfProcess>> flow(@RequestParam String processInsId) {
		List<WfProcess> flowData = processService.syncHistoryFlowList(processInsId, null, null);
		return R.data(flowData);
	}

	@GetMapping("flows")
	@ApiOperation("流转信息")
	@RequestInheritableThread
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processInsId", value = "流程实例id", required = true),})
	public R<List<WfProcess>> flows(@RequestParam String processInsIds) {
		List<WfProcess> flowData = processService.getHistoryFlowListByProcessInsIds(
			Func.toStrList(processInsIds));
		return R.data(flowData);
	}

	@RequestInheritableThread //处理request无法被子线程共享的问题
	@PostMapping("completeTask")
	@ApiOperation("完成任务")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),
		@ApiImplicitParam(name = "processInstanceId", value = "流程实例id", required = true),
		@ApiImplicitParam(name = "pass", value = "同意/驳回", required = true),
		@ApiImplicitParam(name = "comment", value = "评论"),
		@ApiImplicitParam(name = "copyUser", value = "抄送人"),
		@ApiImplicitParam(name = "assignee", value = "指定审批人"),
		@ApiImplicitParam(name = "variables", value = "表单参数"),})
	public R completeTask(@ApiIgnore @RequestBody WfProcess process) {
		if (ObjectUtil.isAnyEmpty(process.getTaskId(), process.getProcessInstanceId())) {
			return R.fail("参数错误");
		}
		return (R) processService.completeTask(process);
	}

	@RequestInheritableThread //处理request无法被子线程共享的问题
	@PostMapping("transferTask")
	@ApiOperation("转办任务")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),
		@ApiImplicitParam(name = "assignee", value = "接受人", required = true),})
	public R transferTask(@ApiIgnore @RequestBody WfProcess process) {
		if (ObjectUtil.isAnyEmpty(process.getTaskId(), process.getAssignee())) {
			return R.fail("参数错误");
		}
		return (R) processService.transferTask(process);
	}

	@PostMapping("delegateTask")
	@ApiOperation("代理任务")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),
		@ApiImplicitParam(name = "assignee", value = "接受人", required = true),})
	public R delegateTask(@ApiIgnore @RequestBody WfProcess process) {
		if (ObjectUtil.isAnyEmpty(process.getTaskId(), process.getAssignee())) {
			return R.fail("参数错误");
		}
		return (R) processService.delegateTask(process);
	}

	@PostMapping("claimTask")
	@ApiOperation("签收任务（签收成功后回到待办任务中）")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),})
	public R claimTask(@ApiIgnore @RequestBody WfProcess process) {
		if (ObjectUtil.isAnyEmpty(process.getTaskId())) {
			return R.fail("参数错误");
		}
		return (R) processService.claimTask(process.getTaskId());
	}

	@GetMapping("getBackNodes")
	@ApiOperation("获取可退回节点")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),})
	public R<List<WfNode>> getBackNodes(@ApiIgnore WfProcess process) {
		return R.data(processService.getBackNodes(process));
	}

	@PostMapping("rollbackTask")
	@ApiOperation("退回到指定节点")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),
		@ApiImplicitParam(name = "nodeId", value = "节点id", required = true),
		@ApiImplicitParam(name = "comment", value = "评论", required = true),})
	public R rollbackTask(@ApiIgnore @RequestBody WfProcess process) {
		if (ObjectUtil.isAnyEmpty(process.getTaskId(), process.getNodeId(), process.getComment())) {
			return R.fail("参数错误");
		}
		return (R) processService.rollbackTask(process);
	}

	@PostMapping("terminateProcess")
	@ApiOperation("终止流程")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),})
	public R terminateProcess(@ApiIgnore @RequestBody WfProcess process) {
		if (ObjectUtil.isAnyEmpty(process.getTaskId())) {
			return R.fail("参数错误");
		}
		return (R) processService.terminateProcess(process);
	}

	@PostMapping("addMultiInstance")
	@ApiOperation("加签")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),
		@ApiImplicitParam(name = "assignee", value = "加签人员", required = true),})
	public R addMultiInstance(@ApiIgnore @RequestBody WfProcess process) {
		if (ObjectUtil.isAnyEmpty(process.getTaskId(), process.getAssignee())) {
			return R.fail("参数错误");
		}
		return (R) processService.addMultiInstance(process);
	}

	@PostMapping("withdrawTask")
	@ApiOperation("撤销/撤回")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),
		@ApiImplicitParam(name = "withdrawType", value = "类型", required = true),})
	public R withdrawTask(@ApiIgnore @RequestBody WfProcess process) {
		if (ObjectUtil.isAnyEmpty(process.getTaskId())) {
			return R.fail("参数错误");
		}
		return (R) processService.withdrawTask(process);
	}


	@GetMapping("getProcessIdByProcessKey")
	@ApiOperation("根据processkey查询processId")
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id", required = true),
		@ApiImplicitParam(name = "withdrawType", value = "类型", required = true),})
	public R getProcessIdByProcessKey(@NotEmpty @RequestParam String processKey) {

		Map<String, Object> map = formService.getFormByProcessDefKey(processKey);
		String processDefId = ((WfProcessDef) map.get("process")).getId();

		return R.data(processDefId);
	}

	@GetMapping("getScanProcessByProcessInsId")
	@ApiOperation("根据ProcessInsId查询流程跳转参数")
	public R getScanProcessByProcessInsId(@RequestParam String processInsId) {
		WfScanDTO wfScanDTO = new WfScanDTO();
		wfScanDTO.setProcInstId(processInsId);
		wfScanDTO = wfProcessScanMapper.selectWfScanByProcessInsId(wfScanDTO);
		return R.data(wfScanDTO);
	}

	@GetMapping("detail/lite")
	@ApiOperation("流程详情")
	@RequestInheritableThread
	@ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务id"),
		@ApiImplicitParam(name = "processInsId", value = "流程实例id", required = true),})
	public R<Map<String, Object>> detail(String processInsId)
		throws ExecutionException, InterruptedException {
		if (ObjectUtil.isAnyEmpty(processInsId)) {
			return R.fail("参数错误");
		}
		List<WfProcess> flowList = processService.syncHistoryFlowList(processInsId, null, null);
		Map<String, Object> result = new HashMap<>();
		StringBuilder flows = new StringBuilder();//创建动态字符串
		boolean hasApplyNode = false;
		for (WfProcess flow : flowList) {//遍历流程列表
			if (Arrays.asList(WfProcessConstant.SEQUENCE_FLOW, WfProcessConstant.CANDIDATE)
				.contains(flow.getHistoryActivityType())) {//若flow的历史活动类型在以上节点，则跳过
				continue;
			}
			if (WfProcessConstant.START_EVENT.equals(flow.getHistoryActivityType())) { // 如果是发起人节点
				flows = new StringBuilder();//重新初始化flows
				hasApplyNode = false;
				flows.append(flow.getAssigneeName()).append("(").append(flow.getComment())
					.append(")")
					.append(WfProcessConstant.CONNECTOR);//添加节点人的姓名和->
			} else if (WfProcessConstant.USER_TASK.equals(
				flow.getHistoryActivityType())) { // 如果是用户任务节点
				if (Arrays.asList("发起人", "申请人")
					.contains(flow.getHistoryActivityName())) {//并且节点的名称是发起人或申请人
					hasApplyNode = true;
				}
				flows.append(flow.getAssigneeName());//添加接点人姓名
				List<Comment> comments = flow.getComments();// 获取当前历史流程节点的评论列表
				Collections.reverse(comments);// 将评论列表倒序排列，把最新的评论排在最前面
				if (!comments.isEmpty()) { // 节点内流转
					flows.append("(");
					for (Comment comment : comments) {//遍历添加操作状态
						switch (comment.getType()) {
							case WfProcessConstant.COMMENT_TYPE_WITHDRAW:
								flows.append("【撤销】");
								break;
							case WfProcessConstant.COMMENT_TYPE_RECALL:
								flows.append("【撤回】");
								break;
							case WfProcessConstant.COMMENT_TYPE_ROLLBACK:
								flows.append("【驳回】");
								break;
							case WfProcessConstant.COMMENT_TYPE_ADD_MULTI_INSTANCE:
								flows.append("【加签】");
								break;
							case WfProcessConstant.COMMENT_TYPE_DELETE_MULTI_INSTANCE:
								flows.append("【减签】");
								break;
							case WfProcessConstant.COMMENT_TYPE_DISPATCH:
								flows.append("【调度】");
								break;
							case WfProcessConstant.COMMENT_TYPE_TRANSFER:
								flows.append("【转办】");
								break;
							case WfProcessConstant.COMMENT_TYPE_DELEGATE:
								flows.append("【委托】");
								break;
							case WfProcessConstant.COMMENT_TYPE_TERMINATE:
								flows.append("【终止】");
								break;
							case WfProcessConstant.COMMENT_TYPE_COMMENT:
								flows.append("【审批】");
								break;
							case WfProcessConstant.COMMENT_TYPE_ASSIGNEE:
								flows.append("【变更】");
								break;
						}
						flows.append(comment.getFullMessage()).append(";");//添加操作人
					}
					flows.append(")");
				}
				flows.append(WfProcessConstant.CONNECTOR);//添加->
			} else if (WfProcessConstant.END_EVENT.equals(
				flow.getHistoryActivityType())) {//若不是用户任务节点，则直接结束
				flows.append("结束");
			}
		}//跳出流程遍历列表
		String flow = flows.toString();//把流程信息存储到flow字符串中
		if (hasApplyNode) { // 若包含发起人节点，去除开始节点和发起人节点重复的问题
			flow = flow.substring(flow.indexOf(WfProcessConstant.CONNECTOR) + 2);
		}
		if (!flow.endsWith("结束") && flow.contains(
			WfProcessConstant.CONNECTOR)) {//如果流程没有结束，那么在最后添加审核中
			String substring = flow.substring(0, flow.lastIndexOf(WfProcessConstant.CONNECTOR));
			substring += "(审核中)";
			result.put("flowStr", substring);//返回未结束的流程
		} else {
			result.put("flowStr", flow);//返回已结束的流程
		}
		result.put("flow", flowList);
		return R.data(result);

	}
}
