package org.springblade.plugin.workflow.process.service.impl;


import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.engine.RepositoryService;
import org.springblade.plugin.workflow.core.utils.ObjectUtil;
import org.springblade.plugin.workflow.core.utils.WfModelUtil;
import org.springblade.plugin.workflow.core.validate.FormValidateListener;
import org.springblade.plugin.workflow.process.service.IWfFormValidateService;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class WfFormValidateServiceImpl implements IWfFormValidateService {

	private final RepositoryService repositoryService;
	private final ApplicationContext applicationContext;

	public WfFormValidateServiceImpl(RepositoryService repositoryService, ApplicationContext applicationContext) {
		this.repositoryService = repositoryService;
		this.applicationContext = applicationContext;
	}

	private final static String NAME_SPACE = "http://flowable.org/bpmn";

	private final static String ATTR_KEY = "formSubmissionVerification";

	@Override
	public void formValidate(String processDefId, String processInstanId, String userTaskKey, String taskId, Map<String, Object> variables) {

		BpmnModel model = repositoryService.getBpmnModel(processDefId);

		FlowElement element = StringUtils.isNotBlank(userTaskKey) ? WfModelUtil.getUserTaskByUserTaskKey(model, userTaskKey) : WfModelUtil.getPromoterEvent(model);

		Objects.requireNonNull(element,"节点元素未找到");

		String formSubmissionVerification = element.getAttributeValue(NAME_SPACE, ATTR_KEY);

		if (StringUtils.isBlank(formSubmissionVerification)) {
			return;
		}

		String placeholder = ObjectUtil.getPlaceholder(formSubmissionVerification);

		if (StringUtils.isBlank(placeholder)) {
			return;
		}

		FormValidateListener formValidate = (FormValidateListener) applicationContext.getBean(placeholder);

		Objects.requireNonNull(formValidate,"未找到定义的验证代理类");

		formValidate.validate(processDefId, processInstanId, taskId, variables);
	}

}
