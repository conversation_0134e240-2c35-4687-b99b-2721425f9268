package org.springblade.plugin.workflow.process.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.plugin.workflow.process.entity.WfDraft;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 流程草稿箱 服务类
 *
 * <AUTHOR>
 */
public interface IWfDraftService extends BaseService<WfDraft> {

	void submit(WfDraft wfDraft);

	void deleteByProcessDefId(String processDefId, String userId, Object platform);

	void deleteByFormKey(String formKey);

}
