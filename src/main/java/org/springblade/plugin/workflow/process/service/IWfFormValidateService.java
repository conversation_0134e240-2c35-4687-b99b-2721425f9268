package org.springblade.plugin.workflow.process.service;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-11-17
 */
public interface IWfFormValidateService {

	/**
	 * 表单验证
	 *
	 * @param processDefId    流程定义id
	 * @param processInstanId 流程实例id
	 * @param userTaskKey     用户任务的key
	 * @param taskId          任务id
	 * @param variables       表单参数
	 */
	void formValidate(String processDefId, String processInstanId, String userTaskKey, String taskId, Map<String, Object> variables);
}
