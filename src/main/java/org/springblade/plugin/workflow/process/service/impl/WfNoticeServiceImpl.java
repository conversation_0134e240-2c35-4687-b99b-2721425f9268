package org.springblade.plugin.workflow.process.service.impl;

import static org.springblade.plugin.workflow.process.entity.WfNotice.Type.FINISH;

import com.google.common.collect.ImmutableList;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springblade.common.cache.UserCache;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.notification.dto.NotificationDto;
import org.springblade.modules.notification.event.NotificationEvent;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserSearchService;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
import org.springblade.plugin.workflow.core.utils.ObjectUtil;
import org.springblade.plugin.workflow.process.entity.WfNotice;
import org.springblade.plugin.workflow.process.service.IWfNoticeService;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

/**
 * 流程消息 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class WfNoticeServiceImpl implements IWfNoticeService {

  private final TaskService taskService;
  private final HistoryService historyService;

  private final IUserSearchService userSearchService;

  private final ApplicationContext applicationContext;


  //	@Async
  @Override
  public void sendNotice(User fromUser, User toUser, String comment, WfNotice.Type type,
      HistoricProcessInstance processInstance, @Nullable HistoricTaskInstance task,
      Map<String, Object> taskVariables) {
    org.springblade.plugin.workflow.process.model.WfProcess detail = new org.springblade.plugin.workflow.process.model.WfProcess();
    detail.setProcessDefinitionName(processInstance.getProcessDefinitionName());
    detail.setTaskName(Objects.nonNull(task) ? task.getName() : "结束");
    User user = UserCache.getUser(Long.valueOf(processInstance.getStartUserId()));
    detail.setStartUsername(user.getName());
    detail.setCreateTime(processInstance.getStartTime());
    detail.setProcessInstanceId(processInstance.getId());
    Object serialNumber = processInstance.getProcessVariables()
        .get(WfProcessConstant.TASK_VARIABLE_SN);
    if (ObjectUtil.isNotEmpty(serialNumber)) {
      detail.setSerialNumber(serialNumber.toString());
    }else {
      detail.setSerialNumber("_");
    }
    Object serialNo = processInstance.getProcessVariables()
        .get("serialNo");
    if (ObjectUtil.isNotEmpty(serialNo)) {
      detail.setSerialNo(serialNo.toString());
    }else{
      detail.setSerialNo("_");
    }
    if (!fromUser.getId().equals(toUser.getId())) {
      processInstance.getProcessDefinitionKey();
      NotificationDto notificationDto = new NotificationDto();
      notificationDto.setCode("flowTodo");
      notificationDto.setTitle(processInstance.getProcessDefinitionName());
      notificationDto.setType("info");
      notificationDto.setContent(String.format("%s 在 %s 操作: %s!", fromUser.getName(),
          DateUtil.format(processInstance.getStartTime(), "yyyy-MM-dd HH:mm:ss"),
          type.getName()));
      notificationDto.setBusinessId(processInstance.getId());
      notificationDto.setReceiveType(NotificationDto.RECEIVE_TYPE_USER);
      notificationDto.setReceiverId(
          ImmutableList.<Long>builder().add(toUser.getId()).build());
      notificationDto.setWfProcess(detail);
      applicationContext.publishEvent(new NotificationEvent(notificationDto));
    }
		/*
		  模拟同业务对接，以下操作不保证正确，请根据实际情况进行修改。
		  ！！！此处对接业务只是临时方法，目前缺少连接流程与业务的独立模块，正在规划中！！！
		  1、通过流程定义key区分操作哪一个业务
		  2、通过WfNotice.Type区分流程进行了哪一步操作
		  3、此方法为异步方法，请确保操作的业务能成功。若不能保证，请去掉@Async注解，并添加事务控制。
		 */
    // 流程/模型定义key，用于区分保存到哪个业务表
  }

  @Async
  @Override
  public Future<String> resolveNoticeInfo(WfNotice notice) {
    // 流程实例
    String processId = notice.getProcessId();
    HistoricProcessInstance processInstance = historyService
        .createHistoricProcessInstanceQuery()
        .includeProcessVariables()
        .processInstanceId(processId).singleResult();
    if (processInstance == null) {
      return new AsyncResult<>("");
    }

    // 当前任务实例
    String taskId = notice.getTaskId();
    HistoricTaskInstance task = null;
    if (StringUtil.isNotBlank(taskId)) {
      task = historyService.createHistoricTaskInstanceQuery()
          .taskId(taskId)
          .singleResult();
    }

    // 当前节点用户
    String fromUserId = notice.getFromUserId();
    User fromUser = UserCache.getUser(Long.valueOf(fromUserId));

    // 流程申请人
    String applyUserId = processInstance.getStartUserId();
    User applyUser = UserCache.getUser(Long.valueOf(applyUserId));

    // 审核意见
    String comment = notice.getComment();

    // 任务变量
    Map<String, Object> taskVariables = notice.getTaskVariables();

    WfNotice.Type type = notice.getType();

    switch (type) {
      case START:
//				this.sendNotice(applyUser, applyUser, comment, type, processInstance, task,
//					taskVariables);
      case PASS:
      case REJECT:
        this.handleCompleteTask(fromUser, applyUser, processInstance, task, comment,
            type,
            taskVariables);
        break;
      case COPY:
      case TRANSFER:
      case DELEGATE:
      case ASSIGNEE:
      case SUSPEND:
      case ACTIVE:
      case URGE:
      case DISPATCH:
      case ADD_MULTI_INSTANCE:
      case DELETE_MULTI_INSTANCE:
        String toUserId = notice.getToUserId();
        User toUser = UserCache.getUser(Long.valueOf(toUserId));
        this.sendNotice(fromUser, toUser, comment, type, processInstance, task,
            taskVariables);
        break;
      case TERMINATE:
      case DELETE_PROCESS:
      case CANCEL:

        this.sendNotice(fromUser, applyUser, comment, type, processInstance, task,
            taskVariables);
        break;
      default:
        break;
    }
    return new AsyncResult<>("");
  }

  /**
   * 处理审核通过/驳回信息
   *
   * @param fromUser        当前节点用户
   * @param applyUser       申请用户
   * @param processInstance 流程实例
   * @param task            任务实例
   */
  private void handleCompleteTask(User fromUser, User applyUser,
      HistoricProcessInstance processInstance, HistoricTaskInstance task, String comment,
      WfNotice.Type type, Map<String, Object> taskVariables) {
    if (processInstance.getEndTime() == null) {
      List<HistoricTaskInstance> taskList = historyService.createHistoricTaskInstanceQuery()
          .processInstanceId(processInstance.getId())
          .taskVariableNotExists(WfProcessConstant.TASK_VARIABLE_NOTICE)
          .includeIdentityLinks()
          .unfinished()
          .list();
      if (taskList != null && !taskList.isEmpty()) {
        for (HistoricTaskInstance nextTask : taskList) {
          // 添加调用消息服务标记，相同任务只调用一次
          if (nextTask.getEndTime() == null) {
            taskService.setVariableLocal(nextTask.getId(),
                WfProcessConstant.TASK_VARIABLE_NOTICE, "1");
          }

          // 当前节点名称
          String currentNode = nextTask.getName();
          // 当前节点执行人id
          String toUserId = nextTask.getAssignee();
          if (StringUtil.isBlank(toUserId)) { // 无执行人，查看候选
            List<? extends IdentityLinkInfo> identityLinks = nextTask.getIdentityLinks();
            // 候选组，角色ID、部门ID、职位ID
            List<String> roles = new ArrayList<>();
            // 候选人
            List<String> userIds = new ArrayList<>();
            identityLinks.forEach(link -> {
              if (StringUtil.isNotBlank(link.getGroupId())) {
                roles.add(link.getGroupId());
              }
              if (StringUtil.isNotBlank(link.getUserId())) {
                userIds.add(link.getUserId());
              }
            });
            // 给候选人发送消息
            for (String userId : userIds) {
              this.sendNotice(fromUser, UserCache.getUser(Long.valueOf(userId)),
                  comment, WfNotice.Type.CANDIDATE, processInstance, nextTask,
                  taskVariables);
            }
            // 给候选组发消息
            if (!roles.isEmpty()) {
              List<Long> groups = roles.stream().map(val -> {
                try {
                  return Long.valueOf(val);
                } catch (Exception ignore) {
                  return 0L;
                }
              }).collect(Collectors.toList());
              // 部门
              userSearchService.listByDept(groups).forEach(
                  user -> this.sendNotice(fromUser,
                      UserCache.getUser(user.getId()),
                      comment, WfNotice.Type.CANDIDATE, processInstance, nextTask,
                      taskVariables));
              // 角色
              userSearchService.listByRole(groups).forEach(
                  user -> this.sendNotice(fromUser,
                      UserCache.getUser(user.getId()),
                      comment, WfNotice.Type.CANDIDATE, processInstance, nextTask,
                      taskVariables));
              // 职位
              userSearchService.listByPost(groups).forEach(
                  user -> this.sendNotice(fromUser,
                      UserCache.getUser(user.getId()),
                      comment, WfNotice.Type.CANDIDATE, processInstance, nextTask,
                      taskVariables));
            }
          } else {
            // 下一节点执行人
            User toUser = UserCache.getUser(Long.valueOf(toUserId));
            this.sendNotice(fromUser, toUser, comment, type,
                processInstance, nextTask, taskVariables);
          }
        }
      }
    } else {
      this.sendNotice(fromUser, applyUser, comment, FINISH, processInstance,
          null, taskVariables);
    }
  }


}
