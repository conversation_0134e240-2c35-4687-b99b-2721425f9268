package org.springblade.plugin.workflow.process.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springblade.plugin.workflow.process.model.WfNode;
import org.springblade.plugin.workflow.process.model.WfProcess;
import org.springblade.core.mp.support.Query;
import org.springblade.plugin.workflow.process.model.WfTaskUser;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

public interface IWfProcessService {

	/**
	 * 发起流程 - 根据流程定义id
	 *
	 * @param processDefId 流程定义id
	 * @param variables    表单参数
	 * @return processInsId
	 */
	String startProcessInstanceById(String processDefId, Map<String, Object> variables);

	/**
	 * 发起流程 - 根据流程定义id
	 *
	 * @param processDefId 流程定义id
	 * @param variables    表单参数
	 * @return processInsId
	 */
	String startProcessInstanceById(String processDefId, Map<String, Object> variables,
									String userId);

	/**
	 * 发起流程 - 根据流程定义key
	 *
	 * @param processDefKey 流程定义key
	 * @param variables     表单参数
	 * @return processInsId
	 */
	String startProcessInstanceByKey(String processDefKey, String businessKey,
									 Map<String, Object> variables);

	/**
	 * 待办/待签/我的已办列表
	 */
	IPage<WfProcess> selectTaskPage(WfProcess process, Query query);

	/**
	 * 查询某个用户 某个流程实例的任务
	 *
	 * @param process  查询条件
	 * @param query    查询条件
	 * @param taskUser 指定用户
	 * @param status   状态
	 * @return 待办任务
	 */
	WfProcess selectToDoTaskPageByUser(WfProcess process, Query query, String taskUser,
									   String status);

	/**
	 * 我的请求/办结列表
	 */
	IPage<WfProcess> selectProcessPage(WfProcess process, Query query);

	/**
	 * 获取流转历史列表
	 */
	List<WfProcess> syncHistoryFlowList(String processInstanceId, String startActivityId,

										String endActivityId);

	/**
	 * 获取流转历史列表
	 */
	Future<List<WfProcess>> historyFlowList(String processInstanceId, String startActivityId,
											String endActivityId);

	/**
	 * 获取流程详情
	 */
	Future<WfProcess> detail(String taskId, String assignee, String candidateGroup);

	/**
	 * 完成任务
	 */
	Object completeTask(WfProcess process);


	/**
	 * 根据实例id，完成所有任务
	 * @param processInstanceId 实例id
	 * @return 状态
	 */
	 Object completeAllTask(String processInstanceId) ;

	/**
	 * 转办任务
	 */
	Object transferTask(WfProcess process);



	/**
	 * 委托任务
	 */
	Object delegateTask(WfProcess process);

	/**
	 * 签收任务
	 */
	Object claimTask(String taskId);

	/**
	 * 获取可退回节点
	 */
	List<WfNode> getBackNodes(WfProcess process);

	/**
	 * 退回到指定节点
	 */
	Object rollbackTask(WfProcess process);

	/**
	 * 终止流程
	 */
	Object terminateProcess(WfProcess process);

	/**
	 * 加签
	 */
	Object addMultiInstance(WfProcess process);

	/**
	 * 撤回
	 */
	Object withdrawTask(WfProcess process);

	/**
	 * 是否可撤回 默认只可撤回一次，若需无限撤回可注释相关代码块。 判断当前流程已完成的任务的处理人是否都是当前登录人，若是则可撤回
	 *
	 * @param taskId      任务实例id
	 * @param currentUser 当前登录人
	 */
	Boolean isReturnable(String taskId, String currentUser);

	/**
	 * 判断当前节点是否是多实例
	 */
	Boolean isMultiInstance(String taskKey, String processDefId);

	/**
	 * 获取指定节点的用户
	 *
	 * @param processDefId 流程定义id
	 * @param processInsId 流程实例id
	 * @param nodeId       节点id
	 */
	WfTaskUser getTaskUser(String processDefId, @Nullable String processInsId, String nodeId);

	/**
	 * 调度流程实例到指定节点
	 *
	 * @param processInsId 流程实例id
	 * @param nodeId       节点id
	 */
	void dispatchTaskTo(String processInsId, String nodeId);

	/**
	 * 获取流程瞬时状态 进行中/已结束/已撤回/已撤销/已驳回/...
	 */
	void setProcessStatus(WfProcess process, HistoricProcessInstance processInstance);

	/**
	 * 代办数量
	 *
	 * @return
	 */
	long todoCount();


	/**
	 * 同步获取流程信息
	 */
	WfProcess syncDetail(String taskId, String assignee, String candidateGroup);

	void addVariable(String processInstanceId, Map<String, Object> variables);

	/**
	 * 完成最新任务节点
	 *
	 * @param processInstanceId 流程实例弟
	 * @param copyUserId        抄送人
	 * @param taskNickName      当前操作人昵称
	 * @param taskUser          当前操作人id
	 */
	void completeProcessTask(String processInstanceId, Long copyUserId, String taskNickName, String taskUser);

	/**
	 * 批量获取流程信息
	 *
	 * @param processInsIds
	 * @return
	 */
	List<WfProcess> getHistoryFlowListByProcessInsIds(List<String> processInsIds);
}
