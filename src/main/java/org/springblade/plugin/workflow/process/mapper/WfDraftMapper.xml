<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.plugin.workflow.process.mapper.WfDraftMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wfDraftResultMap" type="org.springblade.plugin.workflow.process.entity.WfDraft">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="form_key" property="formKey"/>
        <result column="process_def_id" property="processDefId"/>
        <result column="variables" property="variables"/>
        <result column="platform" property="platform"/>
    </resultMap>
    <delete id="deleteByProcessDefId">
      UPDATE blade_wf_draft set is_deleted=1 WHERE process_def_id = #{processDefId} AND user_id =
      #{userId}
      <choose>
        <when test="platform != null and platform != ''">
          AND platform = #{platform}
        </when>
        <otherwise>
          AND platform = 'pc'
        </otherwise>
      </choose>
    </delete>

</mapper>
