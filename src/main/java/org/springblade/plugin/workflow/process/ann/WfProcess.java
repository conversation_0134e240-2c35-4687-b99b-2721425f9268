package org.springblade.plugin.workflow.process.ann;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springblade.plugin.workflow.core.entity.WfEntity;
import org.springblade.plugin.workflow.process.entity.WfNotice;

/**
 * 工作流业务对接模块 工作流中的参数存储到map中
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface WfProcess {

	/**
	 * 流程标识
	 *
	 * @return
	 */
	String value();

	/**
	 * 类型
	 *
	 * @return
	 */
	WfNotice.Type[] type();

	String businessKey() default "id";

	Class<? extends WfEntity> entity();

}
